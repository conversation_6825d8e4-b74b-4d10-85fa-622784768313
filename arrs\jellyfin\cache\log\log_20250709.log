[2025-07-09 21:54:54.334 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-09 21:54:54.382 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[J<PERSON>L<PERSON>FIN_CACHE_DIR, /cache]", "[JELLYFIN_DATA_DIR, /config]", "[J<PERSON><PERSON><PERSON>FIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CONFIG_DIR, /config/config]"]
[2025-07-09 21:54:54.384 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-09 21:54:54.390 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-09 21:54:54.390 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: Processor count: 12
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-09 21:54:54.391 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-09 21:54:54.392 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-09 21:54:54.893 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-09 21:54:55.182 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-09 21:54:55.303 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-09 21:54:55.311 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-09 21:54:55.336 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-09 21:54:55.518 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-09 21:54:55.518 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-09 21:54:55.518 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-09 21:54:55.519 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-09 21:54:55.520 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-09 21:54:55.520 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-09 21:54:55.520 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-09 21:55:06.742 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-09 21:55:06.745 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-09 21:55:06.746 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-09 21:55:06.746 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-09 21:55:06.746 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-09 21:55:06.799 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-09 21:55:06.799 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-09 21:55:06.916 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-09 21:55:07.540 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-09 21:55:07.609 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-09 21:55:07.707 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-10 03:00:00.000 +02:00, which is 05:04:52.2923711 from now.
[2025-07-09 21:55:07.756 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-10 02:00:00.000 +02:00, which is 04:04:52.2437968 from now.
[2025-07-09 21:55:08.103 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-09 21:55:08.460 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:08.610 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-09 21:55:08.703 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-09 21:55:08.780 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-09 21:55:08.794 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:08.799 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:55:08.807 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-09 21:55:08.835 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-09 21:55:09.005 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-09 21:55:10.152 +02:00] [INF] [11] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-09 21:55:10.782 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-09 21:55:10.844 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-09 21:55:12.769 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-07-09 21:55:18.306 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:18.312 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:18.313 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:55:22.861 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-09 21:55:22.906 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-09 21:55:22.906 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-09 21:55:22.906 +02:00] [INF] [1] Main: Startup complete 0:00:28.9030625
[2025-07-09 21:55:28.301 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:28.303 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:28.303 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:55:38.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:38.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:38.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:55:44.701 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:44.702 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:44.702 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:55:48.301 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:48.301 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:48.301 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:55:58.297 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:58.297 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:55:58.298 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:56:08.301 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:08.301 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:08.301 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:56:18.297 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:18.297 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:18.297 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:56:28.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:28.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:28.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:56:38.298 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:38.298 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:38.298 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:56:48.296 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:48.296 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:48.297 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:56:58.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:58.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:56:58.297 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:57:08.297 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:08.297 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:08.298 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:57:18.295 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:18.296 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:18.296 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:57:28.295 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:28.295 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:28.295 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:57:38.299 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:38.299 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:38.299 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:57:48.299 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:48.299 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:48.299 +02:00] [INF] [25] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:57:58.294 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:58.295 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:57:58.295 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:58:08.296 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:08.297 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:08.297 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:58:18.291 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:18.292 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:18.292 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:58:28.293 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:28.293 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:28.293 +02:00] [INF] [26] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:58:38.293 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:38.293 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:38.293 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:58:48.294 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:48.294 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:48.294 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:58:58.289 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:58.289 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:58:58.290 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:59:08.291 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:08.291 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:08.291 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:59:18.292 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:18.292 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:18.292 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:59:28.294 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:28.294 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:28.294 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:59:38.288 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:38.288 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:38.288 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:59:48.290 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:48.291 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:48.291 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 21:59:58.289 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:58.289 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 21:59:58.290 +02:00] [INF] [28] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 22:00:08.288 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:08.288 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:08.288 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 22:00:18.289 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:18.289 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:18.290 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 22:00:28.286 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:28.286 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:28.286 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 22:00:38.287 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:38.287 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:38.287 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 22:00:48.285 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:48.285 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:48.285 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 22:00:58.284 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:58.285 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:00:58.285 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 22:01:08.781 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:01:08.782 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:01:08.782 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-09 22:01:18.288 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:01:18.288 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-09 22:01:18.289 +02:00] [INF] [27] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
