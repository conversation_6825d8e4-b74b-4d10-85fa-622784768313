{"Protocol":0,"Id":"20034cd52067d57502c2df4412f102e0","Path":"/CONTENIDO/PELIS/<PERSON> - Chapter 4 (2023)/<PERSON> - Chapter 4 (2023) Bluray-1080p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":5443057474,"Name":"<PERSON> Wick - Chapter 4 (2023) Bluray-1080p","IsRemote":false,"ETag":"3fd410c3136ad2e3088dfe3275bf7d03","RunTimeTicks":101576700000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"1080p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":4286855,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":796,"Width":1920,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":40,"IsAnamorphic":false},{"Codec":"aac","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Castellano Ac3 2.0 320 kbps","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Castellano Ac3 2.0 320 kbps - Spanish - AAC - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":192000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":44100,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"LC","Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":4478855,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/PELIS/John Wick - Chapter 4 (2023)/John Wick - Chapter 4 (2023) Bluray-1080p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "7a451e5924c557a0ad08d4e0ef99e429-1.mp4" -start_number 0 -hls_segment_filename "/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e429%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e429.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, matroska,webm, from 'file:/CONTENIDO/PELIS/John Wick - Chapter 4 (2023)/John Wick - Chapter 4 (2023) Bluray-1080p.mkv':
  Metadata:
    title           : John Wick Chapter 4 (2023) [1080P][Castellano][VerPeliculasOnline.org]
    ARTIST          : WinX HD Video Converter Deluxe
    MAJOR_BRAND     : mp42
    MINOR_VERSION   : 512
    COMPATIBLE_BRANDS: mp42iso2avc1mp41
    ENCODER         : Lavf58.29.100
  Duration: 02:49:17.67, start: 0.000000, bitrate: 4286 kb/s
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x796 [SAR 199:270 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        ENCODER         : Lavc58.54.100 libx264
        DURATION        : 02:49:17.670000000
  Stream #0:1(spa): Audio: aac (LC), 44100 Hz, stereo, fltp (default)
      Metadata:
        HANDLER_NAME    : Castellano Ac3 2.0 320 kbps
        ENCODER         : Lavc58.54.100 aac
        DURATION        : 02:41:28.322000000
[out#0/hls @ 0x5a7cd941da80] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e429-1.mp4' for writing
Output #0, hls, to '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e429.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (Main), yuv420p(progressive), 1920x796 [SAR 199:270 DAR 16:9], q=2-31, 23.98 fps, 23.98 tbr, 16k tbn (default)
  Stream #0:1: Audio: aac (LC), 44100 Hz, stereo, fltp (default)
Press [q] to stop, [?] for help
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4290.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4291.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4292.mp4' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4293.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4294.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4295.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4296.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4297.mp4' for writing
size=N/A time=00:00:24.73 bitrate=N/A speed=24.7x    
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4298.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e4299.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42910.mp4' for writing
size=N/A time=00:00:45.50 bitrate=N/A speed=30.3x    
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42911.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42912.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42913.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42914.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42915.mp4' for writing
size=N/A time=00:01:10.94 bitrate=N/A speed=35.4x    
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42916.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42917.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42918.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42919.mp4' for writing
size=N/A time=00:01:34.09 bitrate=N/A speed=37.6x    
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42920.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42921.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42922.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42923.mp4' for writing
size=N/A time=00:01:59.36 bitrate=N/A speed=39.7x    
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42924.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42925.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42926.mp4' for writing
[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42927.mp4' for writing


[q] command received. Exiting.

[hls @ 0x5a7cd93eac80] Opening '/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e42928.mp4' for writing
[out#0/hls @ 0x5a7cd941da80] video:84674KiB audio:6613KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:02:28.27 bitrate=N/A speed=41.7x    