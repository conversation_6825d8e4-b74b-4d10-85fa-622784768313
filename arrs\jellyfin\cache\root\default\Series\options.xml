<?xml version="1.0" encoding="utf-8"?>
<LibraryOptions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Enabled>true</Enabled>
  <EnablePhotos>true</EnablePhotos>
  <EnableRealtimeMonitor>true</EnableRealtimeMonitor>
  <EnableLUFSScan>true</EnableLUFSScan>
  <EnableChapterImageExtraction>false</EnableChapterImageExtraction>
  <ExtractChapterImagesDuringLibraryScan>false</ExtractChapterImagesDuringLibraryScan>
  <EnableTrickplayImageExtraction>false</EnableTrickplayImageExtraction>
  <ExtractTrickplayImagesDuringLibraryScan>false</ExtractTrickplayImagesDuringLibraryScan>
  <PathInfos>
    <MediaPathInfo>
      <Path>/CONTENIDO/SERIES</Path>
    </MediaPathInfo>
  </PathInfos>
  <SaveLocalMetadata>false</SaveLocalMetadata>
  <EnableAutomaticSeriesGrouping>false</EnableAutomaticSeriesGrouping>
  <EnableEmbeddedTitles>false</EnableEmbeddedTitles>
  <EnableEmbeddedExtrasTitles>false</EnableEmbeddedExtrasTitles>
  <EnableEmbeddedEpisodeInfos>false</EnableEmbeddedEpisodeInfos>
  <AutomaticRefreshIntervalDays>0</AutomaticRefreshIntervalDays>
  <PreferredMetadataLanguage>es</PreferredMetadataLanguage>
  <MetadataCountryCode>ES</MetadataCountryCode>
  <SeasonZeroDisplayName>Specials</SeasonZeroDisplayName>
  <MetadataSavers />
  <DisabledLocalMetadataReaders />
  <LocalMetadataReaderOrder>
    <string>Nfo</string>
  </LocalMetadataReaderOrder>
  <DisabledSubtitleFetchers />
  <SubtitleFetcherOrder />
  <DisabledMediaSegmentProviders />
  <MediaSegmentProvideOrder />
  <SkipSubtitlesIfEmbeddedSubtitlesPresent>false</SkipSubtitlesIfEmbeddedSubtitlesPresent>
  <SkipSubtitlesIfAudioTrackMatches>false</SkipSubtitlesIfAudioTrackMatches>
  <SubtitleDownloadLanguages />
  <RequirePerfectSubtitleMatch>true</RequirePerfectSubtitleMatch>
  <SaveSubtitlesWithMedia>true</SaveSubtitlesWithMedia>
  <DisabledLyricFetchers />
  <LyricFetcherOrder />
  <CustomTagDelimiters>
    <string>/</string>
    <string>|</string>
    <string>;</string>
    <string>\</string>
  </CustomTagDelimiters>
  <DelimiterWhitelist />
  <AutomaticallyAddToCollection>false</AutomaticallyAddToCollection>
  <AllowEmbeddedSubtitles>AllowAll</AllowEmbeddedSubtitles>
  <TypeOptions>
    <TypeOptions>
      <Type>Series</Type>
      <MetadataFetchers>
        <string>Missing Episode Fetcher</string>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
        <string>The Open Movie Database</string>
      </MetadataFetchers>
      <MetadataFetcherOrder>
        <string>Missing Episode Fetcher</string>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
        <string>The Open Movie Database</string>
      </MetadataFetcherOrder>
      <ImageFetchers>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
      </ImageFetchers>
      <ImageFetcherOrder>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
      </ImageFetcherOrder>
      <ImageOptions />
    </TypeOptions>
    <TypeOptions>
      <Type>Season</Type>
      <MetadataFetchers>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
      </MetadataFetchers>
      <MetadataFetcherOrder>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
      </MetadataFetcherOrder>
      <ImageFetchers>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
      </ImageFetchers>
      <ImageFetcherOrder>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
      </ImageFetcherOrder>
      <ImageOptions />
    </TypeOptions>
    <TypeOptions>
      <Type>Episode</Type>
      <MetadataFetchers>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
        <string>The Open Movie Database</string>
      </MetadataFetchers>
      <MetadataFetcherOrder>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
        <string>The Open Movie Database</string>
      </MetadataFetcherOrder>
      <ImageFetchers>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
        <string>The Open Movie Database</string>
        <string>Embedded Image Extractor</string>
        <string>Screen Grabber</string>
      </ImageFetchers>
      <ImageFetcherOrder>
        <string>TheTVDB</string>
        <string>TheMovieDb</string>
        <string>The Open Movie Database</string>
        <string>Embedded Image Extractor</string>
        <string>Screen Grabber</string>
      </ImageFetcherOrder>
      <ImageOptions />
    </TypeOptions>
  </TypeOptions>
</LibraryOptions>