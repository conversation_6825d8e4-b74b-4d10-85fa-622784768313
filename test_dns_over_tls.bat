@echo off
echo ========================================
echo Verificando DNS-over-TLS en AdGuard Home
echo ========================================
echo.

echo 1. Verificando puerto 853 (DNS-over-TLS)...
telnet 192.168.18.10 853

echo.
echo 2. Verificando certificado SSL...
echo | openssl s_client -connect tankeguard.duckdns.org:853 -servername tankeguard.duckdns.org

echo.
echo 3. Verificando DNS normal (puerto 53)...
nslookup google.com 192.168.18.10

echo.
echo ========================================
echo Verificacion completada!
echo.
echo Si el puerto 853 responde y el certificado es valido,
echo DNS-over-TLS esta funcionando correctamente.
echo ========================================
pause
