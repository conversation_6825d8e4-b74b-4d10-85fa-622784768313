blueprint:
  name: Zone Notification
  description: Send a notification to a device when a person leaves a specific zone.
  domain: automation
  source_url: https://github.com/home-assistant/core/blob/dev/homeassistant/components/automation/blueprints/notify_leaving_zone.yaml
  author: Home Assistant
  input:
    person_entity:
      name: Person
      selector:
        entity:
          filter:
            domain: person
    zone_entity:
      name: Zone
      selector:
        entity:
          filter:
            domain: zone
    notify_device:
      name: Devi<PERSON> to notify
      description: <PERSON><PERSON> needs to run the official Home Assistant app to receive notifications.
      selector:
        device:
          filter:
            integration: mobile_app

triggers:
  trigger: state
  entity_id: !input person_entity

variables:
  zone_entity: !input zone_entity
  # This is the state of the person when it's in this zone.
  zone_state: "{{ states[zone_entity].name }}"
  person_entity: !input person_entity
  person_name: "{{ states[person_entity].name }}"

conditions:
  condition: template
  # The first case handles leaving the Home zone which has a special state when zoning called 'home'.
  # The second case handles leaving all other zones.
  value_template: "{{ zone_entity == 'zone.home' and trigger.from_state.state == 'home' and trigger.to_state.state != 'home' or trigger.from_state.state == zone_state and trigger.to_state.state != zone_state }}"

actions:
  - alias: "Notify that a person has left the zone"
    domain: mobile_app
    type: notify
    device_id: !input notify_device
    message: "{{ person_name }} has left {{ zone_state }}"
