2025-07-17T22:00:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:00:00.076Z [info][Jobs]: Starting scheduled job: Je<PERSON><PERSON> Recently Added Scan 
2025-07-17T22:00:00.076Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5bf4b9f9-feb1-4d5c-9cd6-df76653278e3"}
2025-07-17T22:00:00.084Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:00:00.155Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:00:00.281Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:00:00.283Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:00:00.283Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:00:00.283Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:00:00.286Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:00:00.328Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:00:00.397Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:00:00.413Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:00:00.424Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:00:00.424Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:00:00.425Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:00:00.426Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:00:04.429Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:00:04.792Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:00:04.796Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:00:08.799Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:01:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:01:00.094Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:02:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:02:00.089Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:03:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:03:00.104Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:04:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:04:00.108Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:05:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:05:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:05:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7a171912-8433-4f77-8fe3-1d89c379828d"}
2025-07-17T22:05:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:05:00.135Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:05:00.262Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:05:00.264Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:05:00.265Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:05:00.266Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:05:00.266Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:05:00.266Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:05:00.268Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:05:00.385Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:05:00.394Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:05:00.396Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:05:00.396Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:05:00.397Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:05:04.401Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:05:04.991Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:05:05.007Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:05:09.008Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:06:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:06:00.079Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:07:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:07:00.094Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:08:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:08:00.088Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:09:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:09:00.189Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:10:00.050Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:10:00.085Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:10:00.086Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7482b581-95c9-4e4e-88e5-4b18aada1531"}
2025-07-17T22:10:00.101Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:10:00.234Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:10:00.460Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:10:00.469Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:10:00.552Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:10:00.615Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:10:00.645Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:10:00.646Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:10:00.646Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:10:00.652Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:10:00.655Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:10:00.658Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:10:00.659Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:10:00.660Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:10:04.661Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:10:05.161Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:10:05.181Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:10:09.185Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:11:00.031Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:11:00.160Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:12:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:12:00.150Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:13:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:13:00.213Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:14:00.030Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:14:00.160Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:15:00.031Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:15:00.046Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:15:00.052Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a2dcec75-8f0e-45d6-bc45-60130bcd553d"}
2025-07-17T22:15:00.066Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:15:00.165Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:15:00.434Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:15:00.435Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:15:00.435Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:15:00.435Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:15:00.436Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:15:00.436Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:15:00.439Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:15:00.440Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:15:00.440Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:15:00.440Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:15:00.478Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:15:00.573Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:15:04.578Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:15:05.059Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:15:05.079Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:15:09.083Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:16:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:16:00.150Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:17:00.030Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:17:00.137Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:18:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:18:00.113Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:19:00.030Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:19:00.251Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:20:00.035Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:20:00.047Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:20:00.047Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0480b135-0786-41fc-bd47-98dc1cb5fe48"}
2025-07-17T22:20:00.068Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:20:00.222Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:20:00.426Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:20:00.542Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:20:00.564Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:20:00.582Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:20:00.597Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:20:00.601Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:20:00.627Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:20:00.632Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:20:00.646Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:20:00.650Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:20:00.668Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:20:00.687Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:20:04.692Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:20:05.422Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:20:05.435Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:20:09.440Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:21:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:21:00.159Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:22:00.053Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:22:00.235Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:23:00.031Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:23:00.170Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:24:00.032Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:24:00.173Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:25:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:25:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:25:00.031Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bbfdc9b5-f19e-4569-9d40-bfc478efb202"}
2025-07-17T22:25:00.048Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:25:00.167Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:25:00.365Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:25:00.371Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:25:00.428Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:25:00.477Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:25:00.482Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:25:00.485Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:25:00.488Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:25:00.489Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:25:00.493Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:25:00.496Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:25:00.571Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:25:00.571Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:25:04.576Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:25:05.312Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:25:05.317Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:25:09.320Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:26:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:26:00.127Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:27:00.031Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:27:00.146Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:28:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:28:00.100Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:29:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:29:00.111Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:30:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:30:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:30:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8011d84f-c77a-4ef3-ab46-1f58b4b53539"}
2025-07-17T22:30:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:30:00.149Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:30:00.255Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:30:00.256Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:30:00.297Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:30:00.310Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:30:00.346Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:30:00.352Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:30:00.375Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:30:00.383Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:30:00.386Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:30:00.387Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:30:00.390Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:30:00.392Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:30:04.396Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:30:04.877Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:30:04.904Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:30:08.908Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:31:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:31:00.135Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:32:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:32:00.141Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:33:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:33:00.151Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:34:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:34:00.101Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:35:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:35:00.032Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:35:00.033Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"3e420da0-fec4-44a1-973b-056caf2fad6b"}
2025-07-17T22:35:00.040Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:35:00.136Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:35:00.235Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:35:00.417Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:35:00.417Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:35:00.419Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:35:00.419Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:35:00.421Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:35:00.485Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:35:00.494Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:35:00.495Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:35:00.496Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:35:00.497Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:35:00.498Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:35:04.499Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:35:05.057Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:35:05.069Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:35:09.073Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:36:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:36:00.106Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:37:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:37:00.133Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:38:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:38:00.110Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:39:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:39:00.125Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:40:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:40:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:40:00.031Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"50ce64d9-fcdb-458b-99dd-0fcb40c29441"}
2025-07-17T22:40:00.037Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:40:00.152Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:40:00.290Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:40:00.290Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:40:00.300Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:40:00.340Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:40:00.360Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:40:00.361Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:40:00.442Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:40:00.443Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:40:00.443Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:40:00.444Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:40:00.449Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:40:00.451Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:40:04.453Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:40:04.925Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:40:04.946Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:40:08.949Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:41:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:41:00.088Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:42:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:42:00.135Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:43:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:43:00.126Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:44:00.030Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:44:00.138Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:45:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:45:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:45:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6ab4910a-4361-4614-a7de-b3760135c579"}
2025-07-17T22:45:00.035Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:45:00.286Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:45:00.288Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:45:00.294Z [debug][Jellyfin Sync]: Title already exists and no new media types found John Wick 4 
2025-07-17T22:45:00.304Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:45:00.313Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:45:00.318Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:45:00.325Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:45:00.389Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:45:00.389Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:45:00.391Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:45:00.404Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:45:00.405Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:45:00.406Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:45:04.410Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:45:05.017Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:45:05.028Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:45:09.032Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:46:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:46:00.138Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:47:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:47:00.142Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:48:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:48:00.459Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:49:00.067Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:50:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:50:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:50:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ebd82ca4-45ae-4078-893c-ad3aba33a1ab"}
2025-07-17T22:50:00.040Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:50:00.142Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 3 
2025-07-17T22:50:00.208Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 2 
2025-07-17T22:50:00.262Z [debug][Jellyfin Sync]: Title already exists and no new media types found Un lugar tranquilo: Día uno 
2025-07-17T22:50:00.265Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 
2025-07-17T22:50:00.266Z [debug][Jellyfin Sync]: Title already exists and no new media types found Ocean's 8 
2025-07-17T22:50:00.269Z [debug][Jellyfin Sync]: Title already exists and no new media types found El señor de los anillos: La comunidad del anillo 
2025-07-17T22:50:00.345Z [debug][Jellyfin Sync]: Title already exists and no new media types found Kung Fu Panda 4 
2025-07-17T22:50:00.352Z [debug][Jellyfin Sync]: Title already exists and no new media types found Capitán América: Brave New World 
2025-07-17T22:50:00.352Z [debug][Jellyfin Sync]: Title already exists and no new media types found Una Película de Minecraft 
2025-07-17T22:50:00.354Z [debug][Jellyfin Sync]: Title already exists and no new media types found Los odiosos ocho 
2025-07-17T22:50:00.355Z [debug][Jellyfin Sync]: Title already exists and no new media types found PECADORES 
2025-07-17T22:50:00.383Z [debug][Jellyfin Sync]: Title already exists and no new media types found Las crónicas de Narnia: El príncipe Caspian 
2025-07-17T22:50:04.387Z [info][Jellyfin Sync]: Beginning to process recently added for library: Series 
2025-07-17T22:50:04.857Z [debug][Jellyfin Sync]: Updating existing title: Outlander 
2025-07-17T22:50:04.860Z [debug][Jellyfin Sync]: Updating existing title: Dexter 
2025-07-17T22:50:08.864Z [info][Jellyfin Sync]: Recently Added Scan Complete 
2025-07-17T22:51:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:52:00.030Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:53:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:53:00.083Z [debug][Download Tracker]: Found 1 item(s) in progress on Radarr server: Radarr 
2025-07-17T22:54:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:55:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:55:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T22:55:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f2239823-de60-466e-a68a-38cfbe5f6eb4"}
2025-07-17T22:55:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T22:55:00.093Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T22:55:00.095Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T22:56:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:57:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:58:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T22:59:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:00:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:00:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:00:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bcc9767f-4e82-4fa0-a4ee-0e9f5da42647"}
2025-07-17T23:00:00.033Z [info][Jobs]: Starting scheduled job: Download Sync Reset 
2025-07-17T23:00:00.037Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:00:00.099Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:00:00.102Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:01:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:02:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:03:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:04:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:05:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:05:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:05:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"95ad6ebc-4931-4dca-9199-5edb14a37bc5"}
2025-07-17T23:05:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:05:00.065Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:05:00.065Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:06:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:07:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:08:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:09:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:10:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:10:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:10:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2186a770-c1f1-404a-b3d3-79f1ed69a5f7"}
2025-07-17T23:10:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:10:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:10:00.055Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:11:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:12:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:13:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:14:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:15:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:15:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:15:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"282b542a-f638-4920-b86e-b0cc1859ee5a"}
2025-07-17T23:15:00.037Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:15:00.088Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:15:00.088Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:16:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:17:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:18:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:19:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:20:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:20:00.031Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:20:00.031Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2f95a404-cffa-48ae-b9f5-0a507047f13f"}
2025-07-17T23:20:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:20:00.079Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:20:00.079Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:21:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:22:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:23:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:24:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:25:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:25:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:25:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"471d30e0-c110-433e-b4b0-575da8610ad5"}
2025-07-17T23:25:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:25:00.079Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:25:00.080Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:26:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:27:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:28:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:29:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:30:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:30:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:30:00.031Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ad45f444-843a-457a-8a2d-0137e374e990"}
2025-07-17T23:30:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:30:00.049Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:30:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:31:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:32:00.030Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:33:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:34:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:35:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:35:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:35:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2d108a61-5d7f-48cf-a2e0-102f99426b4b"}
2025-07-17T23:35:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:35:00.084Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:35:00.084Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:36:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:37:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:38:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:39:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:40:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:40:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:40:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b815bd73-1505-4ff3-aa2d-516a09b009c3"}
2025-07-17T23:40:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:40:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:40:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:41:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:42:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:43:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:44:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:45:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:45:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:45:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5928bf36-381c-4a98-80b3-d9075beb1bb5"}
2025-07-17T23:45:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:45:00.057Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:45:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:46:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:47:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:48:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:49:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:50:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:50:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:50:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d9423e2c-8bf6-4904-bdb2-58ee76073b2e"}
2025-07-17T23:50:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:50:00.049Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:50:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:51:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:52:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:53:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:54:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:55:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:55:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-17T23:55:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"943a3e9a-641e-4ffa-9547-a99b54ed9940"}
2025-07-17T23:55:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-17T23:55:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-17T23:55:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-17T23:56:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:57:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:58:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-17T23:59:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:00:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:00:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:00:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"98a5a88a-b58a-452d-ad81-6487f5e992df"}
2025-07-18T00:00:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:00:00.074Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:00:00.074Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:01:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:02:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:03:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:04:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:05:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:05:00.017Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:05:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c7f99ac7-5d58-424e-9c98-039424332bf1"}
2025-07-18T00:05:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:05:00.063Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:05:00.064Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:06:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:07:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:08:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:09:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:10:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:10:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:10:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"53d4401b-3cb1-4bc4-bc12-6ad2fbba5bab"}
2025-07-18T00:10:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:10:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:10:00.049Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:11:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:12:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:13:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:14:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:15:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:15:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:15:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"434b3804-ec20-453a-9d88-19bb62ce24ba"}
2025-07-18T00:15:00.019Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:15:00.061Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:15:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:16:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:17:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:18:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:19:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:20:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:20:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:20:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bd0ac7ba-fc24-4eea-a3ab-21b826f954a5"}
2025-07-18T00:20:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:20:00.081Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:20:00.081Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:21:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:22:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:23:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:24:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:25:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:25:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:25:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"33486e19-f08b-42cb-b7f6-e1df6a9c07a4"}
2025-07-18T00:25:00.024Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:25:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:25:00.067Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:26:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:27:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:28:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:29:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:30:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:30:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:30:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"43914791-575c-445b-97e1-036c217d59b1"}
2025-07-18T00:30:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:30:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:30:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:31:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:32:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:33:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:34:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:35:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:35:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:35:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4dbbb491-9cad-490e-bd97-3a7decbe2d1c"}
2025-07-18T00:35:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:35:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:35:00.060Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:36:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:37:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:38:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:39:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:40:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:40:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:40:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bb395e61-b984-448e-b156-6fae09860f77"}
2025-07-18T00:40:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:40:00.039Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:40:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:41:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:42:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:43:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:44:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:45:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:45:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:45:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9f192972-238c-4e27-bb55-0799bcaa4e1b"}
2025-07-18T00:45:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:45:00.070Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:45:00.072Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:46:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:47:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:48:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:49:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:50:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:50:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:50:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8b3ab75a-e1f3-437f-af9b-4c7e7bf804c8"}
2025-07-18T00:50:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:50:00.062Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:50:00.063Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:51:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:52:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:53:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:54:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:55:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:55:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T00:55:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"47eb2a05-63b1-464d-8018-38abe779e344"}
2025-07-18T00:55:00.039Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T00:55:00.082Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T00:55:00.082Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T00:56:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:57:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:58:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T00:59:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:00:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:00:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:00:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"dae9abec-480b-4f2b-a46f-2127acdd2953"}
2025-07-18T01:00:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Full Scan 
2025-07-18T01:00:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bb7f44ad-fcbf-45ed-861d-dc7cae47ca05"}
2025-07-18T01:00:00.049Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:00:00.052Z [info][Jellyfin Sync]: Beginning to process library: Películas 
2025-07-18T01:00:00.122Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:00:00.123Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:00:00.132Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:00:00.133Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:01:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:02:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:03:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:04:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:05:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:05:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:05:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5df4dd97-20c2-4a40-bf42-1c8f04afa309"}
2025-07-18T01:05:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:05:00.079Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:05:00.081Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:06:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:07:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:08:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:09:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:10:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:10:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:10:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2a13d6af-8b8d-474d-a051-68c581477209"}
2025-07-18T01:10:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:10:00.074Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:10:00.075Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:11:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:12:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:13:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:14:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:15:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:15:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:15:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"62cf5794-7120-4742-8ddb-df9a81569d7c"}
2025-07-18T01:15:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:15:00.081Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:15:00.082Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:16:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:17:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:18:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:19:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:20:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:20:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:20:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0420939b-86af-4ac7-a6e8-18ac364d04a7"}
2025-07-18T01:20:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:20:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:20:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:21:00.031Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:22:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:23:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:24:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:25:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:25:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:25:00.024Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9549658e-086f-48ae-8aea-12cb25fc524a"}
2025-07-18T01:25:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:25:00.082Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:25:00.084Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:26:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:27:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:28:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:29:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:30:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:30:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:30:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"43021f75-73c6-4a66-a51e-d09ccfb817f2"}
2025-07-18T01:30:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:30:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:30:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:31:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:32:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:33:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:34:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:35:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:35:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:35:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9b94516b-c8e8-4b73-aa61-85c3d08d23ac"}
2025-07-18T01:35:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:35:00.067Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:35:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:36:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:37:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:38:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:39:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:40:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:40:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:40:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d37aae91-657f-4197-8075-e50563cfa435"}
2025-07-18T01:40:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:40:00.082Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:40:00.083Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:41:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:42:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:43:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:44:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:45:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:45:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:45:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9d9e5843-5ea8-498b-b1a9-de485b15c4b7"}
2025-07-18T01:45:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:45:00.085Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:45:00.087Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:46:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:47:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:48:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:49:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:50:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:50:00.019Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:50:00.019Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a34707ad-c22e-4b21-ba67-adfdb3441db7"}
2025-07-18T01:50:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:50:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:50:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:51:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:52:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:53:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:54:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:55:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:55:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T01:55:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"19fc27c4-792f-4014-9215-cf9b5f0c36a6"}
2025-07-18T01:55:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T01:55:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T01:55:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T01:56:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:57:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:58:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T01:59:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:00:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:00:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:00:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"31bf3788-37b0-411d-b40f-4413e9757aba"}
2025-07-18T02:00:00.032Z [info][Jobs]: Starting scheduled job: Radarr Scan 
2025-07-18T02:00:00.033Z [info][Radarr Scan]: Scan starting {"sessionId":"2c7e90b2-e5f0-40e8-accc-9ce263015ba6"}
2025-07-18T02:00:00.034Z [info][Radarr Scan]: Beginning to process Radarr server: Radarr 
2025-07-18T02:00:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:00:00.073Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:00:00.073Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:00:00.432Z [debug][Radarr Scan]: Title is unmonitored and has not been downloaded. Skipping item. {"title":"El mundo perdido: Jurassic Park"}
2025-07-18T02:00:00.535Z [debug][Radarr Scan]: Title already exists and no changes detected for John Wick 4 
2025-07-18T02:00:00.550Z [debug][Radarr Scan]: Saved new media: Destino final 4 
2025-07-18T02:00:00.552Z [debug][Radarr Scan]: Saved new media: Destino final 
2025-07-18T02:00:00.552Z [debug][Radarr Scan]: Saved new media: Destino final 2 
2025-07-18T02:00:00.553Z [debug][Radarr Scan]: Saved new media: Jurassic Park (Parque Jurásico) 
2025-07-18T02:00:00.555Z [debug][Radarr Scan]: Saved new media: El renacido 
2025-07-18T02:00:00.556Z [debug][Radarr Scan]: Saved new media: Cómo entrenar a tu dragón 2 
2025-07-18T02:00:00.556Z [debug][Radarr Scan]: Saved new media: Cómo entrenar a tu dragón 
2025-07-18T02:00:00.557Z [debug][Radarr Scan]: Saved new media: Cómo entrenar a tu dragón 3 
2025-07-18T02:00:00.559Z [debug][Radarr Scan]: Saved new media: Náufrago 
2025-07-18T02:00:00.561Z [debug][Radarr Scan]: Saved new media: Django desencadenado 
2025-07-18T02:00:00.561Z [debug][Radarr Scan]: Saved new media: Babylon 
2025-07-18T02:00:00.562Z [debug][Radarr Scan]: Saved new media: Destino final 5 
2025-07-18T02:00:00.567Z [debug][Radarr Scan]: Saved new media: Destino final 3 
2025-07-18T02:00:00.583Z [info][Radarr Scan]: Media for Jungla de cristal exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.621Z [debug][Radarr Scan]: Saved new media: Vengadores: Infinity War 
2025-07-18T02:00:00.693Z [info][Radarr Scan]: Media for Un lugar tranquilo: Día uno exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.694Z [info][Radarr Scan]: Media for Oppenheimer exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.694Z [info][Radarr Scan]: Media for Las crónicas de Narnia: El príncipe Caspian exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.694Z [info][Radarr Scan]: Media for Las crónicas de Narnia: El león, la bruja y el armario exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.694Z [info][Radarr Scan]: Media for El contable 2 exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.694Z [info][Radarr Scan]: Media for Prometheus exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.694Z [info][Radarr Scan]: Media for El señor de los anillos: La comunidad del anillo exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.695Z [info][Radarr Scan]: Media for Until Dawn exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.695Z [info][Radarr Scan]: Media for Los pecadores exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.695Z [info][Radarr Scan]: Media for Kung Fu Panda exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.695Z [info][Radarr Scan]: Media for Kung Fu Panda 2 exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.695Z [info][Radarr Scan]: Media for Kung Fu Panda 4 exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.695Z [info][Radarr Scan]: Media for Una película de Minecraft exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.696Z [info][Radarr Scan]: Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.696Z [info][Radarr Scan]: Media for Kung Fu Panda 3 exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.696Z [info][Radarr Scan]: Media for Destino final: Lazos de sangre exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.696Z [info][Radarr Scan]: Media for Capitán América: Brave New World exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.702Z [info][Radarr Scan]: Media for Gru 4. Mi villano favorito exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.703Z [info][Radarr Scan]: Media for Las crónicas de Narnia: La travesía del viajero del alba exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.703Z [info][Radarr Scan]: Media for Shutter Island exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.704Z [info][Radarr Scan]: Media for Ocean's Twelve exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.704Z [info][Radarr Scan]: Media for Malditos bastardos exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.705Z [info][Radarr Scan]: Media for Ocean's 8 exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.705Z [info][Radarr Scan]: Media for Los odiosos ocho exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.705Z [info][Radarr Scan]: Media for Ocean's Thirteen exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.719Z [info][Radarr Scan]: Media for Ocean's Eleven. Hagan juego exists. Changes were detected and the title will be updated. 
2025-07-18T02:00:00.870Z [info][Notifications]: Sending notification(s) for MEDIA_AVAILABLE {"subject":"Gru 4. Mi villano favorito (2024)"}
2025-07-18T02:00:04.722Z [info][Radarr Scan]: Radarr scan complete 
2025-07-18T02:01:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:02:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:03:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:04:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:05:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:05:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:05:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8bfbf5df-eec1-4e79-87f0-f32d1ec1369a"}
2025-07-18T02:05:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:05:00.077Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:05:00.078Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:06:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:07:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:08:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:09:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:10:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:10:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:10:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c1c3d58e-ba56-45c2-bba5-4b89055a0c1e"}
2025-07-18T02:10:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:10:00.039Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:10:00.040Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:11:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:12:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:13:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:14:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:15:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:15:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:15:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"66075141-c09a-42de-840d-e643b8e1b890"}
2025-07-18T02:15:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:15:00.072Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:15:00.073Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:16:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:17:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:18:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:19:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:20:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:20:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:20:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"542fa648-97a9-4860-930d-d867ccb90859"}
2025-07-18T02:20:00.022Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:20:00.068Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:20:00.069Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:21:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:22:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:23:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:24:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:25:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:25:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:25:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"de06f3b9-5c17-42c2-86bf-f728c4eca47e"}
2025-07-18T02:25:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:25:00.049Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:25:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:26:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:27:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:28:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:29:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:30:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:30:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:30:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"50c3e05c-98c0-4ea6-8ce2-2823d62ed818"}
2025-07-18T02:30:00.008Z [info][Jobs]: Starting scheduled job: Sonarr Scan 
2025-07-18T02:30:00.009Z [info][Sonarr Scan]: Scan starting {"sessionId":"65f1d67f-8a98-4946-9985-3789b3df9c0e"}
2025-07-18T02:30:00.009Z [info][Sonarr Scan]: Beginning to process Sonarr server: Sonarr 
2025-07-18T02:30:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:30:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:30:00.048Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:30:00.679Z [debug][Sonarr Scan]: Updating existing title: Dexter 
2025-07-18T02:30:00.689Z [debug][Sonarr Scan]: Updating existing title: Outlander 
2025-07-18T02:30:00.820Z [debug][Sonarr Scan]: Updating existing title: Dark 
2025-07-18T02:30:00.829Z [debug][Sonarr Scan]: Updating existing title: Black Mirror 
2025-07-18T02:30:00.843Z [debug][Sonarr Scan]: Detected 1 new standard season(s) for Dragon Ball 
2025-07-18T02:30:00.852Z [debug][Sonarr Scan]: Updating existing title: Breaking Bad 
2025-07-18T02:30:00.865Z [debug][Sonarr Scan]: Updating existing title: Prison Break 
2025-07-18T02:30:00.919Z [debug][Sonarr Scan]: Updating existing title: Dragon Ball 
2025-07-18T02:30:00.962Z [debug][Sonarr Scan]: Updating existing title: House of the Dragon 
2025-07-18T02:30:00.962Z [debug][Sonarr Scan]: Updating existing title: Squid Game 
2025-07-18T02:30:00.965Z [debug][Sonarr Scan]: Updating existing title: Peaky Blinders 
2025-07-18T02:30:00.983Z [debug][Sonarr Scan]: Updating existing title: Stranger Things 
2025-07-18T02:30:01.017Z [debug][Sonarr Scan]: Updating existing title: The Big Bang Theory 
2025-07-18T02:30:01.023Z [debug][Sonarr Scan]: Updating existing title: The Boys 
2025-07-18T02:30:01.025Z [debug][Sonarr Scan]: Updating existing title: Rick and Morty 
2025-07-18T02:30:01.030Z [debug][Sonarr Scan]: Updating existing title: Better Call Saul 
2025-07-18T02:30:01.041Z [debug][Sonarr Scan]: Updating existing title: Game of Thrones 
2025-07-18T02:30:01.050Z [debug][Sonarr Scan]: Updating existing title: Friends 
2025-07-18T02:30:05.050Z [info][Sonarr Scan]: Sonarr scan complete 
2025-07-18T02:31:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:32:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:33:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:34:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:35:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:35:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:35:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"db257b0a-2aa3-4174-9113-6c445f7cb82a"}
2025-07-18T02:35:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:35:00.075Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:35:00.076Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:36:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:37:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:38:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:39:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:40:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:40:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:40:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"28ff0707-cdec-4d12-99ad-d5e2b84a0874"}
2025-07-18T02:40:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:40:00.083Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:40:00.083Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:41:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:42:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:43:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:44:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:45:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:45:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:45:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b7786572-0cbe-437f-94e4-641c60c63008"}
2025-07-18T02:45:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:45:00.095Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:45:00.095Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:46:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:47:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:48:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:49:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:50:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:50:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:50:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7707cf58-4f90-4d46-8b3a-841582bdde4a"}
2025-07-18T02:50:00.053Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:50:00.176Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:50:00.178Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:51:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:52:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:53:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:54:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:55:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:55:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T02:55:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b4fa5dfa-26af-4d14-a3a1-a00335ea4e7d"}
2025-07-18T02:55:00.050Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T02:55:00.147Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T02:55:00.149Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T02:56:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:57:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:58:00.031Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T02:59:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:00:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:00:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:00:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"df69321a-9ae2-4090-b3a5-ce4fc82c3749"}
2025-07-18T03:00:00.044Z [info][Jobs]: Starting scheduled job: Image Cache Cleanup 
2025-07-18T03:00:00.063Z [info][Jobs]: Starting scheduled job: Media Availability Sync 
2025-07-18T03:00:00.068Z [info][Availability Sync]: Starting availability sync... 
2025-07-18T03:00:00.073Z [error][Image Cache]: ENOENT: no such file or directory, scandir '/app/config/cache/images/tmdb' 
2025-07-18T03:00:00.073Z [info][Image Cache]: Cleared 0 stale image(s) from cache 'tmdb' 
2025-07-18T03:00:00.078Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:00:00.179Z [info][Image Cache]: Cleared 2 stale image(s) from cache 'avatar' 
2025-07-18T03:00:00.321Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:00.321Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:00:00.743Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:00.744Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 49444] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:00.744Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 49444] still exists. Preventing removal. 
2025-07-18T03:00:00.843Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:00.849Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 870028] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:00.850Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 870028] still exists. Preventing removal. 
2025-07-18T03:00:00.971Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:00.971Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 10140] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:00.972Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 10140] still exists. Preventing removal. 
2025-07-18T03:00:01.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:01.057Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 9502] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:01.057Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 9502] still exists. Preventing removal. 
2025-07-18T03:00:01.167Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:01.167Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 16869] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:01.168Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 16869] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:01.339Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:01.340Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 411] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:01.340Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 411] still exists. Preventing removal. 
2025-07-18T03:00:01.431Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:01.431Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 1233413] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:01.432Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 1233413] still exists. Preventing removal. 
2025-07-18T03:00:01.512Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:01.514Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 822119] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:01.514Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 822119] still exists. Preventing removal. 
2025-07-18T03:00:01.592Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:01.592Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 402900] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:01.607Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:01.607Z [debug][AvailabilitySync]: Failure retrieving the 4K movie [TMDB ID 402900] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:01.608Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 402900] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:01.641Z [info][AvailabilitySync]: The 4K movie [TMDB ID 402900] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:01.829Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:01.831Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 163] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:01.832Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 163] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.068Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.069Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 161] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.069Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 161] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.176Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.176Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 140300] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.177Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 140300] still exists. Preventing removal. 
2025-07-18T03:00:02.280Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.280Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 120] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.294Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.294Z [debug][AvailabilitySync]: Failure retrieving the 4K movie [TMDB ID 120] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.294Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 120] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.310Z [info][AvailabilitySync]: The 4K movie [TMDB ID 120] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.403Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.404Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 1011985] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.404Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 1011985] still exists. Preventing removal. 
2025-07-18T03:00:02.468Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.468Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 273248] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.480Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.481Z [debug][AvailabilitySync]: Failure retrieving the 4K movie [TMDB ID 273248] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.481Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 273248] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.488Z [info][AvailabilitySync]: The 4K movie [TMDB ID 273248] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.576Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.576Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 574475] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.576Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 574475] still exists. Preventing removal. 
2025-07-18T03:00:02.635Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.635Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 70981] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.635Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 70981] still exists. Preventing removal. 
2025-07-18T03:00:02.693Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.693Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 298] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.693Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 298] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.765Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.766Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 2454] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.766Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 2454] still exists. Preventing removal. 
2025-07-18T03:00:02.825Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.825Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 872585] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.825Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 872585] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.884Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.884Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 11324] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.884Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 11324] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:02.958Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:02.958Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 1232546] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:02.958Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 1232546] still exists. Preventing removal. 
2025-07-18T03:00:03.015Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:03.015Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 762441] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:03.028Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:03.028Z [debug][AvailabilitySync]: Failure retrieving the 4K movie [TMDB ID 762441] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:03.028Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 762441] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.035Z [info][AvailabilitySync]: The 4K movie [TMDB ID 762441] was not found in any Radarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.122Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:03.122Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 950387] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:03.122Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 950387] still exists. Preventing removal. 
2025-07-18T03:00:03.343Z [info][AvailabilitySync]: The non-4K show [TMDB ID 12609] still exists. Preventing removal. 
2025-07-18T03:00:03.344Z [info][Availability Sync]: Marking the non-4K show [TMDB ID 12609] as PARTIALLY_AVAILABLE because season removal has occurred. 
2025-07-18T03:00:03.366Z [info][AvailabilitySync]: The non-4K season(s) [1] [TMDB ID 12609] was not found in any Sonarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.440Z [info][AvailabilitySync]: The non-4K show [TMDB ID 2288] still exists. Preventing removal. 
2025-07-18T03:00:03.456Z [info][AvailabilitySync]: The non-4K season(s) [1] [TMDB ID 2288] was not found in any Sonarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.513Z [info][AvailabilitySync]: The non-4K show [TMDB ID 66732] still exists. Preventing removal. 
2025-07-18T03:00:03.552Z [info][AvailabilitySync]: The non-4K season(s) [1,2,3,4] [TMDB ID 66732] was not found in any Sonarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.595Z [info][AvailabilitySync]: The non-4K show [TMDB ID 42009] still exists. Preventing removal. 
2025-07-18T03:00:03.604Z [info][AvailabilitySync]: The non-4K season(s) [1,2,3] [TMDB ID 42009] was not found in any Sonarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.672Z [info][AvailabilitySync]: The non-4K show [TMDB ID 60625] still exists. Preventing removal. 
2025-07-18T03:00:03.688Z [info][AvailabilitySync]: The non-4K season(s) [1,2,3,4,5,6,7,8] [TMDB ID 60625] was not found in any Sonarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.700Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:03.701Z [debug][AvailabilitySync]: Failure retrieving the non-4K show [TMDB ID 56570] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:03.756Z [info][AvailabilitySync]: The non-4K show [TMDB ID 56570] still exists. Preventing removal. 
2025-07-18T03:00:03.770Z [info][AvailabilitySync]: The non-4K season(s) [7] [TMDB ID 56570] was not found in any Sonarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.782Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:03.783Z [debug][AvailabilitySync]: Failure retrieving the non-4K show [TMDB ID 1405] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:03.858Z [info][AvailabilitySync]: The non-4K show [TMDB ID 1405] still exists. Preventing removal. 
2025-07-18T03:00:03.859Z [info][Availability Sync]: Marking the non-4K show [TMDB ID 1405] as PARTIALLY_AVAILABLE because season removal has occurred. 
2025-07-18T03:00:03.885Z [info][AvailabilitySync]: The non-4K season(s) [1,2,3,4,5,6,7,8] [TMDB ID 1405] was not found in any Sonarr and jellyfin instance. Status will be changed to deleted. 
2025-07-18T03:00:03.943Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:00:03.944Z [debug][AvailabilitySync]: Failure retrieving the non-4K movie [TMDB ID 603692] from Jellyfin. {"errorMessage":""}
2025-07-18T03:00:03.944Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 603692] still exists. Preventing removal. 
2025-07-18T03:00:03.986Z [info][AvailabilitySync]: The non-4K movie [TMDB ID 519182] still exists. Preventing removal. 
2025-07-18T03:00:03.989Z [info][Availability Sync]: Availability sync complete. 
2025-07-18T03:01:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:02:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:03:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:04:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:05:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:05:00.034Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:05:00.034Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d472d596-28c5-4d90-b3c6-caefe71992be"}
2025-07-18T03:05:00.070Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:05:00.155Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:05:00.157Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:06:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:07:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:08:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:09:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:10:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:10:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:10:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e33d9f7f-8045-4678-acf4-c3f71e0ee53a"}
2025-07-18T03:10:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:10:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:10:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:11:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:12:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:13:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:14:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:15:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:15:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:15:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"1dde0907-2c30-4668-bddc-36b45bdacae6"}
2025-07-18T03:15:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:15:00.084Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:15:00.084Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:16:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:17:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:18:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:19:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:20:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:20:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:20:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c5341206-c3e4-428e-9681-48afb4476179"}
2025-07-18T03:20:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:20:00.064Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:20:00.065Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:21:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:22:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:23:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:24:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:25:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:25:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:25:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9af935c7-6663-4850-a90e-c81e92112ab7"}
2025-07-18T03:25:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:25:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:25:00.070Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:26:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:27:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:28:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:29:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:30:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:30:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:30:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7c1de195-5650-4550-b20b-37ef5e8c648a"}
2025-07-18T03:30:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:30:00.078Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:30:00.078Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:31:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:32:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:33:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:34:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:35:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:35:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:35:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"51cf1eec-1586-452a-beb6-a5c830ed6803"}
2025-07-18T03:35:00.040Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:35:00.086Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:35:00.086Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:36:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:37:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:38:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:39:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:40:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:40:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:40:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ed399fc6-97ab-4f45-9823-32022e815850"}
2025-07-18T03:40:00.041Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:40:00.078Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:40:00.078Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:41:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:42:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:43:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:44:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:45:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:45:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:45:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0f2acba3-992f-4016-8345-be60a7b72a65"}
2025-07-18T03:45:00.024Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:45:00.068Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:45:00.070Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:46:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:47:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:48:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:49:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:50:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:50:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:50:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"06db5c73-e6d3-4bc5-a5ca-b454c69bb91f"}
2025-07-18T03:50:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:50:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:50:00.048Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:51:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:52:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:53:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:54:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:55:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:55:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T03:55:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9758d806-2753-40a7-a954-fa3b0c8defeb"}
2025-07-18T03:55:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T03:55:00.072Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T03:55:00.072Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T03:56:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:57:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:58:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T03:59:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:00:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:00:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:00:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"41b2de16-70e4-4737-984a-c75da7f26001"}
2025-07-18T04:00:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:00:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:00:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:01:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:02:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:03:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:04:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:05:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:05:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:05:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"70a7c64f-b8e6-46f5-a8ab-378fe9a96990"}
2025-07-18T04:05:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:05:00.065Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:05:00.066Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:06:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:07:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:08:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:09:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:10:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:10:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:10:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2024858b-8309-44a8-b965-e6f6c9287628"}
2025-07-18T04:10:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:10:00.084Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:10:00.086Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:11:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:12:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:13:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:14:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:15:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:15:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:15:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bec144ad-e3ec-4134-baca-5762e6b4f188"}
2025-07-18T04:15:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:15:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:15:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:16:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:17:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:18:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:19:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:20:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:20:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:20:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4a4235a3-5c63-4431-b3a9-e1daccd3d791"}
2025-07-18T04:20:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:20:00.028Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:20:00.028Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:21:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:22:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:23:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:24:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:25:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:25:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:25:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"969251b8-fc81-4bb7-8f1e-c757814e82cb"}
2025-07-18T04:25:00.061Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:25:00.144Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:25:00.155Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:26:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:27:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:28:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:29:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:30:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:30:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:30:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5ad41903-13ef-4693-9ff5-b463cc0b2f92"}
2025-07-18T04:30:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:30:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:30:00.070Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:31:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:32:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:33:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:34:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:35:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:35:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:35:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0c30073c-3488-41aa-8042-31551af56fa2"}
2025-07-18T04:35:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:35:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:35:00.069Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:36:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:37:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:38:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:39:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:40:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:40:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:40:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"26c6c889-c7a7-45bb-9904-5ce268b3c592"}
2025-07-18T04:40:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:40:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:40:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:41:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:42:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:43:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:44:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:45:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:45:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:45:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6e67f65f-90fe-4e2b-8eba-0c585ccb1da5"}
2025-07-18T04:45:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:45:00.076Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:45:00.077Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:46:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:47:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:48:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:49:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:50:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:50:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:50:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"30a76665-3b5f-45be-8a68-22781f5a774f"}
2025-07-18T04:50:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:50:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:50:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:51:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:52:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:53:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:54:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:55:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:55:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T04:55:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d0aac94c-df06-4f29-bcba-ead53fcf6ee1"}
2025-07-18T04:55:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T04:55:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T04:55:00.070Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T04:56:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:57:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:58:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T04:59:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:00:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:00:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:00:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2c1b7342-66ef-4f4c-97c1-e13206024719"}
2025-07-18T05:00:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:00:00.083Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:00:00.084Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:01:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:02:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:03:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:04:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:05:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:05:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:05:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"1d7b57eb-c052-4a54-bd7c-e31c2b17527b"}
2025-07-18T05:05:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:05:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:05:00.067Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:06:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:07:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:08:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:09:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:10:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:10:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:10:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a21f0212-4249-43e6-b2b7-3f3d4665ad6a"}
2025-07-18T05:10:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:10:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:10:00.069Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:11:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:12:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:13:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:14:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:15:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:15:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:15:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"fd7937d0-d745-4c1f-b772-0196853a4cd5"}
2025-07-18T05:15:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:15:00.072Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:15:00.072Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:16:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:17:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:18:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:19:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:20:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:20:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:20:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"985e1fa3-46ee-4e8d-981a-b8096d1d08a3"}
2025-07-18T05:20:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:20:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:20:00.058Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:21:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:22:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:23:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:24:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:25:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:25:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:25:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"74e9a949-8cab-4e39-a67f-a9d1365f929b"}
2025-07-18T05:25:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:25:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:25:00.069Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:26:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:27:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:28:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:29:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:30:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:30:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:30:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5edcba7a-d4cb-443a-b042-bfa4fb6a5ae6"}
2025-07-18T05:30:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:30:00.067Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:30:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:31:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:32:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:33:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:34:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:35:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:35:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:35:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"73d874d6-6c2b-40f4-bc84-0a5305ce883b"}
2025-07-18T05:35:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:35:00.063Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:35:00.064Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:36:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:37:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:38:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:39:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:40:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:40:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:40:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f69bf859-e614-4609-8168-80afaccbcb3b"}
2025-07-18T05:40:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:40:00.032Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:40:00.032Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:41:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:42:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:43:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:44:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:45:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:45:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:45:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7480cc7c-74cb-4cdf-b245-1b59bb522c0a"}
2025-07-18T05:45:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:45:00.085Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:45:00.086Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:46:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:47:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:48:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:49:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:50:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:50:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:50:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7f30d698-765b-4db7-8910-ac4bf6317b71"}
2025-07-18T05:50:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:50:00.072Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:50:00.073Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:51:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:52:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:53:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:54:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:55:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:55:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T05:55:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6b589185-988b-4d43-b136-c21d9b6b38fd"}
2025-07-18T05:55:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T05:55:00.071Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T05:55:00.072Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T05:56:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:57:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:58:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T05:59:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:00:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:00:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:00:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c4fd1514-4323-4896-9171-065dea18d483"}
2025-07-18T06:00:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:00:00.089Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:00:00.089Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:01:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:02:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:03:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:04:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:05:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:05:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:05:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"af6984ef-dc2d-4cee-90c8-48b5cb2cdafb"}
2025-07-18T06:05:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:05:00.067Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:05:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:06:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:07:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:08:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:09:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:10:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:10:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:10:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ae360b9a-b795-4212-85fd-8caf06eb2322"}
2025-07-18T06:10:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:10:00.080Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:10:00.081Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:11:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:12:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:13:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:14:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:15:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:15:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:15:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"070f7d40-b7fa-4ac0-a08c-d522b430ee5a"}
2025-07-18T06:15:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:15:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:15:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:16:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:17:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:18:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:19:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:20:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:20:00.002Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:20:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0beb8b0d-6375-40e5-9687-24443701ba4d"}
2025-07-18T06:20:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:20:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:20:00.055Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:21:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:22:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:23:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:24:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:25:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:25:00.015Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:25:00.015Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"43f4c95f-dfc4-4fa5-b9b5-983a85356de0"}
2025-07-18T06:25:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:25:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:25:00.055Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:26:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:27:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:28:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:29:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:30:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:30:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:30:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"74f7761b-7206-4548-85b8-4566ea89b26f"}
2025-07-18T06:30:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:30:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:30:00.069Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:31:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:32:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:33:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:34:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:35:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:35:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:35:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"3cdfad3d-703b-4d02-bbbd-1f4ebd3a43ac"}
2025-07-18T06:35:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:35:00.067Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:35:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:36:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:37:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:38:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:39:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:40:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:40:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:40:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f2fa8e4b-06fb-43bc-9dea-3e7b68850833"}
2025-07-18T06:40:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:40:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:40:00.060Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:41:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:42:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:43:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:44:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:45:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:45:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:45:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ca329fb0-47e3-42ba-90ea-b42c67578741"}
2025-07-18T06:45:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:45:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:45:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:46:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:47:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:48:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:49:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:50:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:50:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:50:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"12fecdfa-0bd9-400e-b2c0-405c907bda02"}
2025-07-18T06:50:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:50:00.068Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:50:00.070Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:51:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:52:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:53:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:54:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:55:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:55:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T06:55:00.024Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"825278f0-5f5d-412a-a9a5-3de258af0eb7"}
2025-07-18T06:55:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T06:55:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T06:55:00.070Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T06:56:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:57:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:58:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T06:59:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:00:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:00:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:00:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bb3986a0-6a00-421d-bc30-922c3eedfd9d"}
2025-07-18T07:00:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:00:00.072Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:00:00.073Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:01:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:02:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:03:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:04:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:05:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:05:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:05:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8a1f6809-67b4-424e-9e04-e7fdcf9e7b85"}
2025-07-18T07:05:00.019Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:05:00.057Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:05:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:06:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:07:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:08:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:09:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:10:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:10:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:10:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b251f639-3e76-4002-8e30-57eb023edabc"}
2025-07-18T07:10:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:10:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:10:00.045Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:11:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:12:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:13:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:14:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:15:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:15:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:15:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7f909927-4ec6-452e-9f8c-b954f584916d"}
2025-07-18T07:15:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:15:00.073Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:15:00.074Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:16:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:17:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:18:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:19:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:20:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:20:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:20:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ad8d8c21-2a60-409b-9b41-4184adb85ffe"}
2025-07-18T07:20:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:20:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:20:00.069Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:21:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:22:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:23:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:24:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:25:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:25:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:25:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"542f8a88-e0b2-4dbd-b57d-1a588f80cc1c"}
2025-07-18T07:25:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:25:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:25:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:26:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:27:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:28:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:29:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:30:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:30:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:30:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0423cbc0-8f63-42a0-939e-9f6d0e63315c"}
2025-07-18T07:30:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:30:00.057Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:30:00.058Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:31:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:32:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:33:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:34:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:35:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:35:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:35:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8f3bdaaa-916e-4a82-852f-a81f4a12877f"}
2025-07-18T07:35:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:35:00.062Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:35:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:36:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:37:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:38:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:39:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:40:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:40:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:40:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d02ce601-d610-4c2b-8a3e-6d7454e09b38"}
2025-07-18T07:40:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:40:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:40:00.054Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:41:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:42:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:43:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:44:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:45:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:45:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:45:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"1dade865-2e67-44ae-a0cc-4752de63be2f"}
2025-07-18T07:45:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:45:00.049Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:45:00.049Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:46:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:47:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:48:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:49:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:50:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:50:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:50:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9ad723db-556d-40ac-84dc-3f55f68eb27f"}
2025-07-18T07:50:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:50:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:50:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:51:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:52:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:53:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:54:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:55:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:55:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T07:55:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9d1adf4f-86f1-4613-a4f4-cdd91255fbb7"}
2025-07-18T07:55:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T07:55:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T07:55:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T07:56:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:57:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:58:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T07:59:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T08:00:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T08:00:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-18T08:00:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4e1b3340-0011-4b9c-bd79-cc11d4eda45f"}
2025-07-18T08:00:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-18T08:00:00.068Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-18T08:00:00.069Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-18T08:01:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T08:02:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-18T08:03:00.039Z [debug][Jobs]: Starting scheduled job: Download Sync 
