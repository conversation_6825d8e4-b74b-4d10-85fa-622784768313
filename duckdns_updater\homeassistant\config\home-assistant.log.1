2025-07-09 12:22:04.761 WARNING (Recorder) [homeassistant.components.recorder.util] The system could not validate that the sqlite3 database at //config/home-assistant_v2.db was shutdown cleanly
2025-07-09 12:22:05.595 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:05.767 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:13.485 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:13.539 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:13.932 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:13.979 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.111 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.165 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.282 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.331 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.431 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.482 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.590 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.641 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.761 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.814 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.937 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:14.987 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:15.255 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:15.304 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:15.417 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:15.468 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:15.566 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:15.614 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:22.360 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:22.406 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:22.509 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:22.554 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:22.667 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:22.733 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:22.800 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:22.854 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 12:22:54.111 WARNING (ImportExecutor_0) [py.warnings] /usr/local/lib/python3.13/site-packages/miio/miot_device.py:23: FutureWarning: functools.partial will be a method descriptor in future Python versions; wrap it in enum.member() if you want to preserve the old behavior
  Bool = partial(_str2bool)

2025-07-09 12:51:03.742 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:51:03.744 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:51:04.787 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:51:05.363 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:51:05.655 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:51:05.714 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:51:05.950 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:54:20.889 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:54:21.426 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:54:22.367 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:54:23.071 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:54:23.217 ERROR (MainThread) [frontend.js.modern.202507021] Uncaught error from Chrome ********* on Windows 10
TypeError: Cannot read properties of undefined (reading 'domain')
_keyFunction (src/panels/config/integrations/dialog-add-integration.ts:478:16)
keyFunction (src/virtualize.ts:116:41)
r (src/directives/repeat.ts:56:28)
dt (src/directives/repeat.ts:93:52)
update (src/directive.ts:135:16)
_$AS (src/lit-html.ts:1174:23)
S (src/lit-html.ts:1452:12)
_$AI (src/async-directive.ts:366:18)
setValue (src/virtualize.ts:139:13)
dispatchEvent (src/Virtualizer.ts:816:23)
2025-07-09 12:54:27.965 WARNING (ImportExecutor_0) [py.warnings] /usr/local/lib/python3.13/site-packages/google/__init__.py:2: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  __import__('pkg_resources').declare_namespace(__name__)

2025-07-09 13:01:07.316 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:01:07.453 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:04:08.256 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:04:25.159 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:04:26.486 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:05:11.581 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:05:52.135 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:05:59.790 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:06:07.452 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
2025-07-09 13:06:09.104 ERROR (MainThread) [homeassistant.components.http.forwarded] A request from a reverse proxy was received from **********, but your HTTP integration is not set-up for reverse proxies
