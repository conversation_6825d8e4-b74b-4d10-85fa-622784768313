
# === CONFIGURACIÓN HTTP PARA PROXY REVERSO ===
http:
  # Permitir conexiones a través de proxy reverso
  use_x_forwarded_for: true

  # IPs de confianza (redes Docker y locales)
  trusted_proxies:
    - **********/12    # Rango completo de Docker
    - ***********/16   # Red local
    - 127.0.0.1        # Localhost
    - ::1              # IPv6 localhost
    - **********       # IP específica del proxy Caddy

# Loads default set of integrations. Do not remove.
default_config:

# Load frontend themes from the themes folder
frontend:
  themes: !include_dir_merge_named themes

automation: !include automations.yaml
script: !include scripts.yaml
scene: !include scenes.yaml
