{"version": 1, "minor_version": 10, "key": "core.device_registry", "data": {"devices": [{"area_id": null, "config_entries": ["01JZQ439CPNGR69Y62QKXBA5K1"], "config_entries_subentries": {"01JZQ439CPNGR69Y62QKXBA5K1": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-09T08:16:09.148195+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "44603cbe9e62b1f01385df9df5093530", "identifiers": [["sun", "01JZQ439CPNGR69Y62QKXBA5K1"]], "labels": [], "manufacturer": null, "model": null, "model_id": null, "modified_at": "2025-07-09T08:16:09.148301+00:00", "name_by_user": null, "name": "Sun", "primary_config_entry": "01JZQ439CPNGR69Y62QKXBA5K1", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JZQ43CD30F1MQJ730E76GS6E"], "config_entries_subentries": {"01JZQ43CD30F1MQJ730E76GS6E": [null]}, "configuration_url": "homeassistant://config/backup", "connections": [], "created_at": "2025-07-09T08:16:12.257630+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "3e85ceddf7918ed2fc8365aad8343fa1", "identifiers": [["backup", "backup_manager"]], "labels": [], "manufacturer": "Home Assistant", "model": "Home Assistant Backup", "model_id": null, "modified_at": "2025-07-09T08:16:12.257660+00:00", "name_by_user": null, "name": "Backup", "primary_config_entry": "01JZQ43CD30F1MQJ730E76GS6E", "serial_number": null, "sw_version": "2025.7.1", "via_device_id": null}, {"area_id": null, "config_entries": ["01JZQ4EEBV0YK27KNSVNMA8HYP"], "config_entries_subentries": {"01JZQ4EEBV0YK27KNSVNMA8HYP": [null]}, "configuration_url": "https://www.met.no/en", "connections": [], "created_at": "2025-07-09T08:22:14.940846+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "ce53d0d15afe4c1141d162ef8773e1cd", "identifiers": [["met", "01JZQ4EEBV0YK27KNSVNMA8HYP"]], "labels": [], "manufacturer": "Met.no", "model": "Forecast", "model_id": null, "modified_at": "2025-07-09T08:22:14.940998+00:00", "name_by_user": null, "name": "Forecast", "primary_config_entry": "01JZQ4EEBV0YK27KNSVNMA8HYP", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JZQ4HFF5EJ386F7VTQD1PXGA"], "config_entries_subentries": {"01JZQ4HFF5EJ386F7VTQD1PXGA": [null]}, "configuration_url": "https://forecast.solar", "connections": [], "created_at": "2025-07-09T08:23:54.912181+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "30b5c18e5073c6374ff61f1ecb366e5b", "identifiers": [["forecast_solar", "01JZQ4HFF5EJ386F7VTQD1PXGA"]], "labels": [], "manufacturer": "Forecast.Solar", "model": "public", "model_id": null, "modified_at": "2025-07-09T08:23:54.912274+00:00", "name_by_user": null, "name": "Solar production forecast", "primary_config_entry": "01JZQ4HFF5EJ386F7VTQD1PXGA", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": null, "config_entries": ["01JZQ4J6Q67936F3RMKMBQQFS3"], "config_entries_subentries": {"01JZQ4J6Q67936F3RMKMBQQFS3": [null]}, "configuration_url": "https://forecast.solar", "connections": [], "created_at": "2025-07-09T08:24:18.200105+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "06782bf6d13eda3955dd1c44125b36d7", "identifiers": [["forecast_solar", "01JZQ4J6Q67936F3RMKMBQQFS3"]], "labels": [], "manufacturer": "Forecast.Solar", "model": "public", "model_id": null, "modified_at": "2025-07-09T08:24:18.200133+00:00", "name_by_user": null, "name": "Solar production forecast", "primary_config_entry": "01JZQ4J6Q67936F3RMKMBQQFS3", "serial_number": null, "sw_version": null, "via_device_id": null}, {"area_id": "cuarto_de_juanjo", "config_entries": ["01JZQCZNSEDEPQGW72JVYRCYJH"], "config_entries_subentries": {"01JZQCZNSEDEPQGW72JVYRCYJH": [null]}, "configuration_url": null, "connections": [], "created_at": "2025-07-09T10:51:28.017481+00:00", "disabled_by": null, "entry_type": "service", "hw_version": null, "id": "a34ba6cee7167d10d1fa65ac9014fd75", "identifiers": [["epic_games_store", "01JZQCZNSEDEPQGW72JVYRCYJH"]], "labels": [], "manufacturer": "Epic Games Store", "model": null, "model_id": null, "modified_at": "2025-07-09T10:52:33.862957+00:00", "name_by_user": null, "name": "Epic Games Store", "primary_config_entry": "01JZQCZNSEDEPQGW72JVYRCYJH", "serial_number": null, "sw_version": null, "via_device_id": null}], "deleted_devices": []}}