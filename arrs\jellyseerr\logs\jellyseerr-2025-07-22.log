2025-07-21T22:00:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:00:00.102Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:00:00.102Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b7b78105-d4e0-48ff-8419-b6789c4ce3d4"}
2025-07-21T22:00:00.197Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:00:00.304Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:00:00.305Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:01:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:02:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:03:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:04:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:05:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:05:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:05:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"189eb4a3-e017-42fe-8ff3-ed40fb96127f"}
2025-07-21T22:05:00.035Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:05:00.077Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:05:00.077Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:06:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:07:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:08:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:09:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:10:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:10:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:10:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9a985af2-54fd-4499-8a6e-fe0b6a3854d6"}
2025-07-21T22:10:00.007Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:10:00.045Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:10:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:11:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:12:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:13:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:14:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:15:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:15:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:15:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8264979f-2ce2-4a70-a1d4-7af58d4abea3"}
2025-07-21T22:15:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:15:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:15:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:16:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:17:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:18:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:19:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:20:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:20:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:20:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6adc4cf4-72c2-47db-9df3-af8bc9c4d0c6"}
2025-07-21T22:20:00.007Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:20:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:20:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:21:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:22:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:23:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:24:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:25:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:25:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:25:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"40175190-f614-4220-9a6d-34ebf2ee5853"}
2025-07-21T22:25:00.007Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:25:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:25:00.040Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:26:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:27:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:28:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:29:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:30:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:30:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:30:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e8ac34c3-8994-4ec4-abca-77efb793ba4a"}
2025-07-21T22:30:00.006Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:30:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:30:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:31:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:32:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:33:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:34:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:35:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:35:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:35:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f3813c47-72b5-437b-92b5-15f556871169"}
2025-07-21T22:35:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:35:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:35:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:36:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:37:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:38:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:39:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:40:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:40:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:40:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f39c0143-fcf8-4547-9dfd-bbf2f7b72eee"}
2025-07-21T22:40:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:40:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:40:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:41:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:42:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:43:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:44:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:45:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:45:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:45:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"816aaddf-3c2a-45ea-93ad-4f68b2050c64"}
2025-07-21T22:45:00.007Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:45:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:45:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:46:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:47:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:48:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:49:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:50:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:50:00.002Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:50:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6cda8e7a-41e4-4d0f-9b0b-fe2379020407"}
2025-07-21T22:50:00.006Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:50:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:50:00.040Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:51:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:52:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:53:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:54:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:55:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:55:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T22:55:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"347a532d-26a4-4451-b186-75294cb5d832"}
2025-07-21T22:55:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T22:55:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T22:55:00.066Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T22:56:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:57:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:58:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T22:59:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:00:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:00:00.002Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:00:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"91a69c23-c418-425a-8d07-9bb0a097e99e"}
2025-07-21T23:00:00.007Z [info][Jobs]: Starting scheduled job: Download Sync Reset 
2025-07-21T23:00:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:00:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:00:00.048Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:01:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:02:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:03:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:04:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:05:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:05:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:05:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2d8fc7c5-087d-4b33-974a-64b22aa0c523"}
2025-07-21T23:05:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:05:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:05:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:06:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:07:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:08:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:09:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:10:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:10:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:10:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4221a058-7327-4906-a65f-460d176dcf5a"}
2025-07-21T23:10:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:10:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:10:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:11:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:12:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:13:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:14:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:15:00.033Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:15:00.040Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:15:00.040Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"75ac3831-db1c-4af3-8c14-5f952fdf5f8f"}
2025-07-21T23:15:00.046Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:15:00.089Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:15:00.090Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:16:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:17:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:18:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:19:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:20:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:20:00.019Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:20:00.019Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6bbf06dc-5081-4ce3-befa-bae61ef5243b"}
2025-07-21T23:20:00.022Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:20:00.061Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:20:00.063Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:21:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:22:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:23:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:24:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:25:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:25:00.020Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:25:00.020Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"14416613-1882-4f72-89b5-3b5b01f93446"}
2025-07-21T23:25:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:25:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:25:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:26:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:27:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:28:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:29:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:30:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:30:00.009Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:30:00.009Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b80f1bc5-7f34-4187-86e5-6549ebbc55b1"}
2025-07-21T23:30:00.013Z [info][Jobs]: Starting scheduled job: Process Blacklisted Tags 
2025-07-21T23:30:00.019Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:30:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:30:00.054Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:31:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:32:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:33:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:34:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:35:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:35:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:35:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"79f21fe9-c4c4-483b-b026-5049598274c9"}
2025-07-21T23:35:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:35:00.061Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:35:00.063Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:36:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:37:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:38:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:39:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:40:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:40:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:40:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5bf70704-19b1-4025-838b-610ca46ab241"}
2025-07-21T23:40:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:40:00.061Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:40:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:41:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:42:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:43:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:44:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:45:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:45:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:45:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"29c80a2f-c181-4939-943d-6d11f1e715da"}
2025-07-21T23:45:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:45:00.070Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:45:00.071Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:46:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:47:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:48:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:49:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:50:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:50:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:50:00.009Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"88d4846b-cdf8-4a99-a076-f17ef477d8a7"}
2025-07-21T23:50:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:50:00.045Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:50:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:51:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:52:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:53:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:54:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:55:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:55:00.017Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-21T23:55:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d903c937-c91e-46f0-8a8c-b811872cb56e"}
2025-07-21T23:55:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-21T23:55:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-21T23:55:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-21T23:56:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:57:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:58:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-21T23:59:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:00:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:00:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:00:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"26dbe8c4-7221-43e8-a5e0-f6f40145bd00"}
2025-07-22T00:00:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:00:00.090Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:00:00.090Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:01:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:02:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:03:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:04:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:05:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:05:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:05:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6dfb9d9b-e9ad-4c17-87db-03da8da123fc"}
2025-07-22T00:05:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:05:00.073Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:05:00.073Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:06:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:07:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:08:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:09:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:10:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:10:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:10:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a7de07d4-1779-4698-aaef-47add9539a1d"}
2025-07-22T00:10:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:10:00.072Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:10:00.073Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:11:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:12:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:13:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:14:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:15:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:15:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:15:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e1d8e4b0-b1bb-4dad-aa37-95a0160f5ac9"}
2025-07-22T00:15:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:15:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:15:00.070Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:16:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:17:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:18:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:19:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:20:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:20:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:20:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"1c91e4cb-fd5a-4e0c-8434-5485e741bc84"}
2025-07-22T00:20:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:20:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:20:00.067Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:21:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:22:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:23:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:24:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:25:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:25:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:25:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b489d086-1142-4ff8-bfce-d1cd53b09f95"}
2025-07-22T00:25:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:25:00.075Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:25:00.075Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:26:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:27:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:28:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:29:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:30:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:30:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:30:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"83d06357-c1fa-4433-bd59-922bc1bc2f94"}
2025-07-22T00:30:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:30:00.070Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:30:00.072Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:31:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:32:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:33:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:34:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:35:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:35:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:35:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4e0dcf5b-0d4d-417c-bbb0-f64f20598ec8"}
2025-07-22T00:35:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:35:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:35:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:36:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:37:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:38:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:39:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:40:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:40:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:40:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6bc2456c-9070-4897-8b8f-26ece2f6f46b"}
2025-07-22T00:40:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:40:00.087Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:40:00.087Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:41:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:42:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:43:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:44:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:45:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:45:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:45:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8e3921c0-524e-4c80-8ed5-7b73d58241bf"}
2025-07-22T00:45:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:45:00.057Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:45:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:46:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:47:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:48:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:49:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:50:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:50:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:50:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ad5bab25-7856-4423-9fb2-27ff4d289833"}
2025-07-22T00:50:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:50:00.079Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:50:00.081Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:51:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:52:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:53:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:54:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:55:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:55:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T00:55:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2855b68a-cfc6-4000-81d9-3d33b1e2778e"}
2025-07-22T00:55:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T00:55:00.067Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T00:55:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T00:56:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:57:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:58:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T00:59:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:00:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:00:00.031Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:00:00.034Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"32126468-5c96-4a09-b40a-ebc301a72771"}
2025-07-22T01:00:00.056Z [info][Jobs]: Starting scheduled job: Jellyfin Full Scan 
2025-07-22T01:00:00.056Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2585bf88-6bcd-417f-8013-4ae3b6025342"}
2025-07-22T01:00:00.074Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:00:00.079Z [info][Jellyfin Sync]: Beginning to process library: Películas 
2025-07-22T01:00:00.207Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:00:00.209Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:00:00.213Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:00:00.214Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:01:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:02:02.820Z [info]: Commit Tag: $GIT_SHA 
2025-07-22T01:02:03.451Z [info]: Starting Jellyseerr version 2.7.2 
2025-07-22T01:02:05.034Z [debug][Settings Migrator]: Checking migration '0001_migrate_hostname.js'... 
2025-07-22T01:02:05.036Z [debug][Settings Migrator]: Checking migration '0002_migrate_apitokens.js'... 
2025-07-22T01:02:05.039Z [debug][Settings Migrator]: Checking migration '0003_emby_media_server_type.js'... 
2025-07-22T01:02:05.040Z [debug][Settings Migrator]: Checking migration '0004_migrate_region_setting.js'... 
2025-07-22T01:02:05.041Z [debug][Settings Migrator]: Checking migration '0005_migrate_network_settings.js'... 
2025-07-22T01:02:05.042Z [debug][Settings Migrator]: Checking migration '0006_remove_lunasea.js'... 
2025-07-22T01:02:05.046Z [info][Notifications]: Registered notification agents 
2025-07-22T01:02:05.094Z [info][Jobs]: Scheduled jobs loaded 
2025-07-22T01:02:05.300Z [info][Server]: Server ready on port 5055 
2025-07-22T01:03:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:04:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:05:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:05:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:05:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6bd40773-7c45-4262-b95d-4e87963ce1fa"}
2025-07-22T01:05:00.038Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:05:00.117Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:05:00.118Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:06:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:07:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:08:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:09:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:10:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:10:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:10:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2ff68b07-ab10-4eeb-bd37-dccc72b27ff7"}
2025-07-22T01:10:00.035Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:10:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:10:00.066Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:11:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:12:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:13:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:14:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:15:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:15:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:15:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6ad115c3-e088-4987-a268-519847c07beb"}
2025-07-22T01:15:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:15:00.071Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:15:00.072Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:16:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:17:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:18:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:19:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:20:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:20:00.015Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:20:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"eb36fc80-68e6-4662-be1a-6e0e7f3c577e"}
2025-07-22T01:20:00.037Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:20:00.067Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:20:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:21:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:22:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:23:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:24:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:25:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:25:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:25:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"fa431fe3-d607-4d7e-80db-4c6b35a92a9f"}
2025-07-22T01:25:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:25:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:25:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:26:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:27:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:28:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:29:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:30:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:30:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:30:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bcdfd763-ceb9-44a9-8689-a181930d5514"}
2025-07-22T01:30:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:30:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:30:00.052Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:31:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:32:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:33:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:34:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:35:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:35:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:35:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"38e65cec-2ec6-4526-b885-e3e989c8e38f"}
2025-07-22T01:35:00.061Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:35:00.110Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:35:00.111Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:36:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:37:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:38:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:39:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:40:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:40:00.042Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:40:00.043Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"924b4d8d-fa91-492f-80ee-56fcc5d2c704"}
2025-07-22T01:40:00.056Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:40:00.106Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:40:00.107Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:41:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:42:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:43:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:44:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:45:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:45:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:45:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8e76c852-c0ae-412f-9912-bcabdc076846"}
2025-07-22T01:45:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:45:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:45:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:46:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:47:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:48:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:49:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:50:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:50:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:50:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7776ee67-a9b3-45a0-afc6-e59a114c86e7"}
2025-07-22T01:50:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:50:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:50:00.067Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:51:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:52:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:53:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:54:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:55:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:55:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T01:55:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"058b033a-068f-4dc1-a5a0-631bbb3083fd"}
2025-07-22T01:55:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T01:55:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T01:55:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T01:56:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:57:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:58:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T01:59:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:00:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:00:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:00:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6286a039-f230-4b4b-a9f9-b4e1210cdf20"}
2025-07-22T02:00:00.030Z [info][Jobs]: Starting scheduled job: Radarr Scan 
2025-07-22T02:00:00.030Z [info][Radarr Scan]: Scan starting {"sessionId":"8698012c-138d-470c-aeea-44858caa5905"}
2025-07-22T02:00:00.031Z [info][Radarr Scan]: Beginning to process Radarr server: Radarr 
2025-07-22T02:00:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:00:00.063Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:00:00.064Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:00:00.473Z [debug][Radarr Scan]: Title is unmonitored and has not been downloaded. Skipping item. {"title":"El mundo perdido: Jurassic Park"}
2025-07-22T02:00:00.546Z [debug][Radarr Scan]: Title already exists and no changes detected for Malditos bastardos 
2025-07-22T02:00:00.547Z [debug][Radarr Scan]: Title already exists and no changes detected for Ocean's 8 
2025-07-22T02:00:00.547Z [debug][Radarr Scan]: Title already exists and no changes detected for Ocean's Eleven. Hagan juego 
2025-07-22T02:00:00.548Z [debug][Radarr Scan]: Title already exists and no changes detected for Ocean's Thirteen 
2025-07-22T02:00:00.548Z [debug][Radarr Scan]: Title already exists and no changes detected for Ocean's Twelve 
2025-07-22T02:00:00.548Z [debug][Radarr Scan]: Title already exists and no changes detected for Oppenheimer 
2025-07-22T02:00:00.549Z [debug][Radarr Scan]: Title already exists and no changes detected for Prometheus 
2025-07-22T02:00:00.549Z [debug][Radarr Scan]: Title already exists and no changes detected for Shutter Island 
2025-07-22T02:00:00.550Z [debug][Radarr Scan]: Title already exists and no changes detected for El contable 2 
2025-07-22T02:00:00.550Z [debug][Radarr Scan]: Title already exists and no changes detected for Las crónicas de Narnia: El príncipe Caspian 
2025-07-22T02:00:00.551Z [debug][Radarr Scan]: Title already exists and no changes detected for Las crónicas de Narnia: El león, la bruja y el armario 
2025-07-22T02:00:00.551Z [debug][Radarr Scan]: Title already exists and no changes detected for Las crónicas de Narnia: La travesía del viajero del alba 
2025-07-22T02:00:00.551Z [debug][Radarr Scan]: Title already exists and no changes detected for Los odiosos ocho 
2025-07-22T02:00:00.552Z [debug][Radarr Scan]: Title already exists and no changes detected for El señor de los anillos: La comunidad del anillo 
2025-07-22T02:00:00.553Z [debug][Radarr Scan]: Title already exists and no changes detected for Until Dawn 
2025-07-22T02:00:00.554Z [debug][Radarr Scan]: Title already exists and no changes detected for Los pecadores 
2025-07-22T02:00:00.556Z [debug][Radarr Scan]: Title already exists and no changes detected for Kung Fu Panda 
2025-07-22T02:00:00.556Z [debug][Radarr Scan]: Title already exists and no changes detected for Kung Fu Panda 2 
2025-07-22T02:00:00.556Z [debug][Radarr Scan]: Title already exists and no changes detected for Kung Fu Panda 3 
2025-07-22T02:00:00.556Z [debug][Radarr Scan]: Title already exists and no changes detected for Kung Fu Panda 4 
2025-07-22T02:00:00.557Z [debug][Radarr Scan]: Title already exists and no changes detected for Una película de Minecraft 
2025-07-22T02:00:00.559Z [debug][Radarr Scan]: Title already exists and no changes detected for Gru 4. Mi villano favorito 
2025-07-22T02:00:00.562Z [debug][Radarr Scan]: Title already exists and no changes detected for Destino final: Lazos de sangre 
2025-07-22T02:00:00.563Z [debug][Radarr Scan]: Title already exists and no changes detected for Un lugar tranquilo: Día uno 
2025-07-22T02:00:00.564Z [debug][Radarr Scan]: Title already exists and no changes detected for Capitán América: Brave New World 
2025-07-22T02:00:00.616Z [info][Radarr Scan]: Media for Vengadores: Infinity War exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.621Z [info][Radarr Scan]: Media for Destino final 4 exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.622Z [info][Radarr Scan]: Media for Jurassic Park (Parque Jurásico) exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.622Z [info][Radarr Scan]: Media for Cómo entrenar a tu dragón 3 exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.623Z [info][Radarr Scan]: Media for Náufrago exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.623Z [info][Radarr Scan]: Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.624Z [info][Radarr Scan]: Media for Jungla de cristal exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.629Z [info][Radarr Scan]: Media for Babylon exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.630Z [info][Radarr Scan]: Media for American History X exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.630Z [info][Radarr Scan]: Media for John Wick 4 exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.631Z [info][Radarr Scan]: Media for Destino final 3 exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.631Z [info][Radarr Scan]: Media for Destino final 2 exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.632Z [info][Radarr Scan]: Media for El renacido exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.632Z [info][Radarr Scan]: Media for Cómo entrenar a tu dragón 2 exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.634Z [info][Radarr Scan]: Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.634Z [info][Radarr Scan]: Media for Destino final 5 exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.635Z [info][Radarr Scan]: Media for Django desencadenado exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:00.635Z [info][Radarr Scan]: Media for Destino final exists. Changes were detected and the title will be updated. 
2025-07-22T02:00:04.637Z [info][Radarr Scan]: Radarr scan complete 
2025-07-22T02:01:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:02:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:03:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:04:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:05:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:05:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:05:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d3d958bd-8541-479c-8c96-78e2c09446ea"}
2025-07-22T02:05:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:05:00.061Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:05:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:06:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:07:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:08:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:09:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:10:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:10:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:10:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"05597d33-bbfb-428b-a51e-592d51be23ee"}
2025-07-22T02:10:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:10:00.069Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:10:00.071Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:11:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:12:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:13:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:14:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:15:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:15:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:15:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"776411cc-6d9d-4623-9b36-f8262e7aeff6"}
2025-07-22T02:15:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:15:00.065Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:15:00.066Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:16:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:17:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:18:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:19:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:20:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:20:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:20:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e6dd4cc5-5f0e-4fb7-a81f-c37e06f74b45"}
2025-07-22T02:20:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:20:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:20:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:21:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:22:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:23:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:24:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:25:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:25:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:25:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"16559542-9d43-49e6-a5af-180fd38db27b"}
2025-07-22T02:25:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:25:00.049Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:25:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:26:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:27:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:28:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:29:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:30:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:30:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:30:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0160e4f7-3d7d-48c8-b6da-91c20f683cbc"}
2025-07-22T02:30:00.022Z [info][Jobs]: Starting scheduled job: Sonarr Scan 
2025-07-22T02:30:00.022Z [info][Sonarr Scan]: Scan starting {"sessionId":"4d420dcd-5afe-4448-82bf-8bc590a9cf8d"}
2025-07-22T02:30:00.022Z [info][Sonarr Scan]: Beginning to process Sonarr server: Sonarr 
2025-07-22T02:30:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:30:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:30:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:30:00.682Z [debug][Sonarr Scan]: Updating existing title: House of the Dragon 
2025-07-22T02:30:00.696Z [debug][Sonarr Scan]: Updating existing title: Rick and Morty 
2025-07-22T02:30:00.763Z [debug][Sonarr Scan]: Updating existing title: Stranger Things 
2025-07-22T02:30:00.792Z [debug][Sonarr Scan]: Updating existing title: Dragon Ball 
2025-07-22T02:30:00.823Z [debug][Sonarr Scan]: Updating existing title: Squid Game 
2025-07-22T02:30:00.832Z [debug][Sonarr Scan]: Updating existing title: Dark 
2025-07-22T02:30:00.844Z [debug][Sonarr Scan]: Updating existing title: Peaky Blinders 
2025-07-22T02:30:00.848Z [debug][Sonarr Scan]: Updating existing title: Prison Break 
2025-07-22T02:30:00.894Z [debug][Sonarr Scan]: Updating existing title: Breaking Bad 
2025-07-22T02:30:00.899Z [debug][Sonarr Scan]: Updating existing title: Black Mirror 
2025-07-22T02:30:00.901Z [debug][Sonarr Scan]: Updating existing title: The Big Bang Theory 
2025-07-22T02:30:00.917Z [debug][Sonarr Scan]: Updating existing title: The Boys 
2025-07-22T02:30:00.944Z [debug][Sonarr Scan]: Updating existing title: Dexter 
2025-07-22T02:30:00.948Z [debug][Sonarr Scan]: Updating existing title: Better Call Saul 
2025-07-22T02:30:00.954Z [debug][Sonarr Scan]: Updating existing title: Outlander 
2025-07-22T02:30:00.959Z [debug][Sonarr Scan]: Updating existing title: Friends 
2025-07-22T02:30:00.982Z [debug][Sonarr Scan]: Updating existing title: Game of Thrones 
2025-07-22T02:30:04.987Z [info][Sonarr Scan]: Sonarr scan complete 
2025-07-22T02:31:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:32:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:33:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:34:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:35:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:35:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:35:00.024Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"926eefae-ae63-4c37-ab7f-796b0eb30495"}
2025-07-22T02:35:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:35:00.067Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:35:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:36:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:37:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:38:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:39:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:40:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:40:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:40:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5ad073da-c861-49f2-a5eb-0e7d195df951"}
2025-07-22T02:40:00.038Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:40:00.077Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:40:00.079Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:41:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:42:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:43:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:44:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:45:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:45:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:45:00.024Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d6ca7b5b-7eb2-44bc-9c1b-351e60cdcde3"}
2025-07-22T02:45:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:45:00.061Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:45:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:46:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:47:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:48:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:49:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:50:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:50:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:50:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"957bde68-8f74-4e4f-8cbb-d4e141ec0c4c"}
2025-07-22T02:50:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:50:00.072Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:50:00.073Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:51:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:52:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:53:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:54:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:55:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:55:00.009Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T02:55:00.009Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"734c695a-fd9a-4a1a-b212-abbfe75f1cbc"}
2025-07-22T02:55:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T02:55:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T02:55:00.044Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T02:56:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:57:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:58:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T02:59:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:00:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:00:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:00:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ac2e12a0-966a-4900-a842-600c371a2627"}
2025-07-22T03:00:00.012Z [info][Jobs]: Starting scheduled job: Image Cache Cleanup 
2025-07-22T03:00:00.015Z [info][Jobs]: Starting scheduled job: Media Availability Sync 
2025-07-22T03:00:00.016Z [info][Availability Sync]: Starting availability sync... 
2025-07-22T03:00:00.018Z [error][Image Cache]: ENOENT: no such file or directory, scandir '/app/config/cache/images/tmdb' 
2025-07-22T03:00:00.018Z [info][Image Cache]: Cleared 0 stale image(s) from cache 'tmdb' 
2025-07-22T03:00:00.018Z [info][Image Cache]: Cleared 0 stale image(s) from cache 'avatar' 
2025-07-22T03:00:00.022Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:00:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:00:00.054Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:00:00.063Z [error][AvailabilitySync]: Sync interrupted. {"status":401,"error":"apiError","errorMessage":"INVALID_AUTH_TOKEN"}
2025-07-22T03:00:00.065Z [info][Availability Sync]: Availability sync complete. 
2025-07-22T03:01:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:02:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:03:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:04:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:05:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:05:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:05:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7aa1e777-3701-4b82-a72b-1699b21ae253"}
2025-07-22T03:05:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:05:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:05:00.042Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:06:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:07:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:08:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:09:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:10:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:10:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:10:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"74fc9331-93f7-4b61-aae1-eb51bfca7de9"}
2025-07-22T03:10:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:10:00.061Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:10:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:11:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:12:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:13:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:14:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:15:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:15:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:15:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a7345760-5dba-448b-b5af-083db9e77528"}
2025-07-22T03:15:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:15:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:15:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:16:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:17:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:18:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:19:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:20:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:20:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:20:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"715d541a-9221-4757-bed3-6786e4330b33"}
2025-07-22T03:20:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:20:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:20:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:21:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:22:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:23:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:24:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:25:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:25:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:25:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"18e2c9ea-c925-4230-9166-b13b07314f9e"}
2025-07-22T03:25:00.037Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:25:00.074Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:25:00.075Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:26:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:27:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:28:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:29:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:30:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:30:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:30:00.024Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d8626829-5c6c-4de1-9e04-64f617808402"}
2025-07-22T03:30:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:30:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:30:00.058Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:31:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:32:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:33:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:34:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:35:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:35:00.017Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:35:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2c55b2a4-43fb-4718-9276-45aff48629a2"}
2025-07-22T03:35:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:35:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:35:00.052Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:36:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:37:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:38:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:39:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:40:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:40:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:40:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5880385f-0346-40b2-8a0a-5e8a61986fd7"}
2025-07-22T03:40:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:40:00.057Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:40:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:41:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:42:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:43:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:44:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:45:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:45:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:45:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5887096a-a8cb-4d5b-b43e-848aec890fd3"}
2025-07-22T03:45:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:45:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:45:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:46:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:47:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:48:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:49:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:50:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:50:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:50:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8f590820-4fdb-4ea4-b1f5-ab0f011ce62b"}
2025-07-22T03:50:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:50:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:50:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:51:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:52:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:53:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:54:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:55:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:55:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T03:55:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"3b8131d6-c87f-43cf-94c8-022bca8326c9"}
2025-07-22T03:55:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T03:55:00.065Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T03:55:00.066Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T03:56:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:57:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:58:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T03:59:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:00:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:00:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:00:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"fde7b4bf-4fdb-4bb1-8c72-f6d08d26b78f"}
2025-07-22T04:00:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:00:00.061Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:00:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:01:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:02:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:03:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:04:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:05:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:05:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:05:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"af14076b-ed23-470a-aa9f-8e97559a3c9c"}
2025-07-22T04:05:00.022Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:05:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:05:00.053Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:06:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:07:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:08:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:09:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:10:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:10:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:10:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"af8c807a-f03a-4801-8a9e-7bec9f571a3d"}
2025-07-22T04:10:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:10:00.055Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:10:00.055Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:11:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:12:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:13:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:14:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:15:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:15:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:15:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2b750323-6bbb-4474-8816-fc0520bb9b9f"}
2025-07-22T04:15:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:15:00.064Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:15:00.065Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:16:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:17:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:18:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:19:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:20:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:20:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:20:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c7686dff-cd25-450e-83ae-51921e4b872f"}
2025-07-22T04:20:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:20:00.034Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:20:00.036Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:21:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:22:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:23:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:24:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:25:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:25:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:25:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2346e8de-80b4-4671-969b-1d0a84187bc7"}
2025-07-22T04:25:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:25:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:25:00.060Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:26:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:27:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:28:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:29:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:30:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:30:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:30:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"cdc09e4d-d459-4b2f-b79e-efc81757c846"}
2025-07-22T04:30:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:30:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:30:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:31:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:32:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:33:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:34:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:35:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:35:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:35:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d18c5de6-9427-449e-b474-27731f0bb581"}
2025-07-22T04:35:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:35:00.062Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:35:00.064Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:36:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:37:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:38:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:39:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:40:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:40:00.017Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:40:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"09206c53-d101-443e-89da-5b3f92c994ba"}
2025-07-22T04:40:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:40:00.057Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:40:00.058Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:41:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:42:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:43:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:44:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:45:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:45:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:45:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b2459659-c709-4387-9f1e-e6b88e62af92"}
2025-07-22T04:45:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:45:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:45:00.060Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:46:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:47:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:48:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:49:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:50:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:50:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:50:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f0333afd-38f0-4ba7-98bf-b6425dd95aaa"}
2025-07-22T04:50:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:50:00.065Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:50:00.069Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:51:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:52:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:53:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:54:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:55:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:55:00.009Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T04:55:00.010Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"66ab1078-8f00-4f07-8914-6a49358862ed"}
2025-07-22T04:55:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T04:55:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T04:55:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T04:56:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:57:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:58:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T04:59:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:00:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:00:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:00:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6577b761-0f0e-4942-933f-dd3188a79642"}
2025-07-22T05:00:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:00:00.037Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:00:00.037Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:01:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:02:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:03:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:04:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:05:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:05:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:05:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"161bf78d-e361-48b0-b92b-c2c02c62b07c"}
2025-07-22T05:05:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:05:00.055Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:05:00.060Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:06:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:07:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:08:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:09:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:10:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:10:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:10:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"85ca810b-9115-4cfd-b32f-e13f8693e19e"}
2025-07-22T05:10:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:10:00.062Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:10:00.063Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:11:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:12:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:13:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:14:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:15:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:15:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:15:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b1b4d8d5-c89d-43dc-a12b-adbb43f8fd94"}
2025-07-22T05:15:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:15:00.052Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:15:00.053Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:16:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:17:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:18:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:19:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:20:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:20:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:20:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c68be5d2-e894-41a1-9c45-8c066ecb11da"}
2025-07-22T05:20:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:20:00.045Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:20:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:21:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:22:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:23:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:24:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:25:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:25:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:25:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d3136678-196a-46a3-9113-1d41a4edc3d5"}
2025-07-22T05:25:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:25:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:25:00.053Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:26:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:27:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:28:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:29:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:30:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:30:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:30:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ec48d5c5-a0a8-44f9-8247-4940490e90d0"}
2025-07-22T05:30:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:30:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:30:00.054Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:31:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:32:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:33:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:34:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:35:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:35:00.019Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:35:00.019Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"662ce8cd-2493-43b1-a369-81fe1ce80c92"}
2025-07-22T05:35:00.024Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:35:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:35:00.053Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:36:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:37:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:38:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:39:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:40:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:40:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:40:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"da01c810-9b77-44ce-a999-f73e411df2fd"}
2025-07-22T05:40:00.019Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:40:00.045Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:40:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:41:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:42:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:43:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:44:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:45:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:45:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:45:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"42e930d0-7baa-4529-8f24-ebc8a4c704f2"}
2025-07-22T05:45:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:45:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:45:00.049Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:46:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:47:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:48:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:49:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:50:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:50:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:50:00.019Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"dfd15c2b-a062-4399-814e-5ae78d1da939"}
2025-07-22T05:50:00.024Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:50:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:50:00.055Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:51:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:52:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:53:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:54:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:55:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:55:00.020Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T05:55:00.020Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"73f76033-e72c-41a1-99f6-fcee696d343b"}
2025-07-22T05:55:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T05:55:00.029Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T05:55:00.029Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T05:56:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:57:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:58:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T05:59:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:00:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:00:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:00:00.010Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6f2251d3-b9fc-4528-b9c1-d81d131a671b"}
2025-07-22T06:00:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:00:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:00:00.044Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:01:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:02:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:03:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:04:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:05:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:05:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:05:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"82252c6e-904b-4944-a740-4bfa24ccfa08"}
2025-07-22T06:05:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:05:00.035Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:05:00.036Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:06:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:07:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:08:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:09:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:10:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:10:00.019Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:10:00.019Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"21f53f04-4f52-4b8e-807f-b4ee4ce89972"}
2025-07-22T06:10:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:10:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:10:00.054Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:11:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:12:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:13:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:14:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:15:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:15:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:15:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0496aa61-855d-45e1-a9a0-3a21ba462bd9"}
2025-07-22T06:15:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:15:00.064Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:15:00.065Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:16:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:17:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:18:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:19:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:20:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:20:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:20:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"fa3860c8-1851-4a23-85a2-0208b5b7de01"}
2025-07-22T06:20:00.022Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:20:00.052Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:20:00.053Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:21:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:22:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:23:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:24:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:25:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:25:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:25:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"00dddd7d-a160-4d06-b2ac-de2fa9faa5d8"}
2025-07-22T06:25:00.022Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:25:00.055Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:25:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:26:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:27:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:28:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:29:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:30:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:30:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:30:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c1854aca-22c5-4e36-bcce-1d6aacf07ee4"}
2025-07-22T06:30:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:30:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:30:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:31:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:32:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:33:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:34:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:35:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:35:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:35:00.020Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6920f846-5723-44bd-ad5b-e52b734501bb"}
2025-07-22T06:35:00.042Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:35:00.093Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:35:00.093Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:36:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:37:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:38:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:39:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:40:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:40:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:40:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"57075fda-e971-4a2a-9751-411394100d12"}
2025-07-22T06:40:00.024Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:40:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:40:00.054Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:41:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:42:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:43:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:44:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:45:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:45:00.020Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:45:00.020Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e26b6d2f-3159-46b2-8052-345110e4fe7c"}
2025-07-22T06:45:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:45:00.062Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:45:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:46:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:47:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:48:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:49:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:50:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:50:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:50:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"40b197de-d348-40e9-8e34-c1712d0b4d86"}
2025-07-22T06:50:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:50:00.055Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:50:00.058Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:51:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:52:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:53:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:54:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:55:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:55:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T06:55:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"069bcb6c-d7b1-4921-991b-93c431f5222a"}
2025-07-22T06:55:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T06:55:00.049Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T06:55:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T06:56:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:57:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:58:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T06:59:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:00:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:00:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:00:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"cef21ec4-14e1-4328-b0ff-e214bc0b2987"}
2025-07-22T07:00:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:00:00.026Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:00:00.027Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:01:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:02:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:03:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:04:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:05:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:05:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:05:00.018Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"785c0621-b8cd-477a-a567-63064607f477"}
2025-07-22T07:05:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:05:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:05:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:06:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:07:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:08:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:09:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:10:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:10:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:10:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"1dbf8bd1-fcb2-4e61-8ef5-467a22ef0cee"}
2025-07-22T07:10:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:10:00.045Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:10:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:11:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:12:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:13:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:14:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:15:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:15:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:15:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d84a2a5f-c754-4504-8a1a-96f4366832e0"}
2025-07-22T07:15:00.022Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:15:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:15:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:16:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:17:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:18:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:19:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:20:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:20:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:20:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c48b2857-b359-42db-b116-3ca09de15ea5"}
2025-07-22T07:20:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:20:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:20:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:21:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:22:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:23:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:24:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:25:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:25:00.017Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:25:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"dcfa80e3-82d2-4ec9-b56c-aa99c49d7263"}
2025-07-22T07:25:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:25:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:25:00.052Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:26:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:27:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:28:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:29:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:30:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:30:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:30:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9b46a08c-18a2-4453-afc3-85fdefc74cd5"}
2025-07-22T07:30:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:30:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:30:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:31:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:32:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:33:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:34:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:35:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:35:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:35:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ae4634bb-3c3a-4bc2-8a50-0638b4d9ed1c"}
2025-07-22T07:35:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:35:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:35:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:36:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:37:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:38:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:39:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:40:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:40:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:40:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"91cdde0b-a723-451e-b3c5-70896069a07e"}
2025-07-22T07:40:00.053Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:40:00.094Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:40:00.097Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:41:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:42:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:43:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:44:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:45:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:45:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:45:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ff6d62fa-bc92-4e3a-b66a-d2f50e19fc0a"}
2025-07-22T07:45:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:45:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:45:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:46:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:47:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:48:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:49:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:50:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:50:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:50:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"528e0479-f1f0-44ee-b517-f81b37b054c3"}
2025-07-22T07:50:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:50:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:50:00.049Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:51:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:52:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:53:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:54:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:55:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:55:00.015Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T07:55:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2f8fd6ec-eb7d-4b10-b76d-8c8eeed28d8d"}
2025-07-22T07:55:00.019Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T07:55:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T07:55:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T07:56:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:57:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:58:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T07:59:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:00:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:00:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:00:00.019Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9be8a029-9aa7-4728-a1ae-ef1304a99ae6"}
2025-07-22T08:00:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:00:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:00:00.053Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:01:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:02:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:03:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:04:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:05:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:05:00.009Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:05:00.010Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"40a6bcfe-6e56-4c72-bf10-ac92fe2497e5"}
2025-07-22T08:05:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:05:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:05:00.044Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:06:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:07:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:08:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:09:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:10:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:10:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:10:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"45eef160-fee3-4981-989c-4c6e31e9f080"}
2025-07-22T08:10:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:10:00.096Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:10:00.097Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:11:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:12:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:13:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:14:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:15:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:15:00.019Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:15:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ba003d08-ba7e-4c5b-b08f-a856f75efebc"}
2025-07-22T08:15:00.100Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:15:00.216Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:15:00.226Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:16:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:17:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:18:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:19:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:20:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:20:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:20:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e6ea25bb-6bf7-4a24-95c1-059b84f54c4a"}
2025-07-22T08:20:00.076Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:20:00.192Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:20:00.192Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:21:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:22:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:23:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:24:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:25:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:25:00.114Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:25:00.115Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"64fbac4b-c7ed-4a66-a6f7-fab8d5a1f582"}
2025-07-22T08:25:00.191Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:25:00.281Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:25:00.292Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:26:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:27:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:28:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:29:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:30:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:30:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:30:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"707a731f-f588-4045-ab76-99ea422de55f"}
2025-07-22T08:30:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:30:00.068Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:30:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:31:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:32:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:33:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:34:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:35:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:35:00.019Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:35:00.020Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d04af312-a797-47f7-bf4b-0d30bb53adc6"}
2025-07-22T08:35:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:35:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:35:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:36:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:37:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:38:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:39:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:40:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:40:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:40:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7bc8e5ae-ac06-4029-ab45-03864b62ea24"}
2025-07-22T08:40:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:40:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:40:00.045Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:41:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:42:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:43:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:44:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:45:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:45:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:45:00.010Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"cd0dd9bd-5a5b-430d-b006-af2ad3085d49"}
2025-07-22T08:45:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:45:00.055Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:45:00.055Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:46:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:47:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:48:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:49:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:50:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:50:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:50:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"3a6ee333-1a15-400e-94d1-a8df4fa686da"}
2025-07-22T08:50:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:50:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:50:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:51:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:52:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:53:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:54:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:55:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:55:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T08:55:00.016Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bd94cb5a-561e-44b7-a9f7-9876c98e811a"}
2025-07-22T08:55:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T08:55:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T08:55:00.049Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T08:56:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:57:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:58:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T08:59:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:00:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:00:00.016Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:00:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6e6c0343-b637-472f-8c03-cdd4613ae410"}
2025-07-22T09:00:00.022Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:00:00.055Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:00:00.055Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:01:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:02:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:03:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:04:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:05:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:05:00.018Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:05:00.019Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8c150ae9-7e02-41c0-8820-7f2fd3fa6f72"}
2025-07-22T09:05:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:05:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:05:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:06:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:07:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:08:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:09:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:10:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:10:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:10:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"73c565da-f070-43b9-8738-b2c4e815f5e1"}
2025-07-22T09:10:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:10:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:10:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:11:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:12:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:13:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:14:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:15:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:15:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:15:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"26f4ee38-13fa-4bbe-8b80-307f37e8360a"}
2025-07-22T09:15:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:15:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:15:00.048Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:16:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:17:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:18:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:19:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:20:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:20:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:20:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ae336f14-dd24-4c53-9fcc-99dbff83026d"}
2025-07-22T09:20:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:20:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:20:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:21:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:22:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:22:00.150Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:23:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:23:03.564Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:24:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:24:00.143Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:25:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:25:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:25:00.034Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"fa01a2e2-f65e-41c6-974a-87c090894759"}
2025-07-22T09:25:00.073Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:25:00.230Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:25:00.231Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:25:00.273Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:26:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:26:00.368Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:27:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:27:00.250Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:28:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:28:00.211Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:29:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:29:00.184Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:30:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:30:00.034Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:30:00.040Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9fa6bf43-3bcb-48a9-817a-ccf6b0257f1a"}
2025-07-22T09:30:00.147Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:30:00.342Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:30:00.345Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:30:00.385Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:31:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:31:01.240Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:32:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:32:00.245Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:33:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:33:00.158Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:34:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:34:00.226Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:35:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:35:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:35:00.015Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7e162499-c206-4605-9cb0-79b4cae2be36"}
2025-07-22T09:35:00.039Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:35:00.113Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:35:00.115Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:35:00.117Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:36:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:36:00.044Z [debug][Download Tracker]: Found 6 item(s) in progress on Sonarr server: Sonarr 
2025-07-22T09:37:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:38:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:39:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:40:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:40:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:40:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9c1bce8c-0ba8-418e-9a16-82d1408af244"}
2025-07-22T09:40:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:40:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:40:00.049Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:41:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:42:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:43:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:44:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:45:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:45:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:45:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9efed302-7130-440a-8514-0d0a14482ba2"}
2025-07-22T09:45:00.021Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:45:00.046Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:45:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:46:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:47:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:48:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:49:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:50:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:50:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:50:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0ec5af3b-688f-4977-8bb4-f6cf483192f9"}
2025-07-22T09:50:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:50:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:50:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:51:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:52:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:53:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:54:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:55:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:55:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T09:55:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"427d0416-68ad-42c0-aefb-bb9f263f757e"}
2025-07-22T09:55:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T09:55:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T09:55:00.052Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T09:56:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:57:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:58:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T09:59:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:00:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:00:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:00:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5001077f-f858-4285-b47b-1793d73697af"}
2025-07-22T10:00:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:00:00.045Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:00:00.045Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:01:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:02:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:03:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:04:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:05:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:05:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:05:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"47910277-018f-4717-9699-c644f8966267"}
2025-07-22T10:05:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:05:00.052Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:05:00.053Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:06:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:07:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:08:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:09:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:10:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:10:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:10:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e334f36b-8e41-4fce-8eed-a5b2d580a197"}
2025-07-22T10:10:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:10:00.055Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:10:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:11:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:12:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:13:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:14:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:15:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:15:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:15:00.010Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"1b8f8f7f-dd48-40f3-96a1-9b140a5fc110"}
2025-07-22T10:15:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:15:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:15:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:16:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:17:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:18:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:19:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:20:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:20:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:20:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"759b9932-6054-4b32-9aa7-a7fcef526586"}
2025-07-22T10:20:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:20:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:20:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:21:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:22:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:23:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:24:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:25:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:25:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:25:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"eb770deb-2da2-470e-8fc2-a95eac79720d"}
2025-07-22T10:25:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:25:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:25:00.045Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:26:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:27:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:28:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:29:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:30:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:30:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:30:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"43c990d3-ccdc-4a92-ba63-e6a2cb176bd4"}
2025-07-22T10:30:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:30:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:30:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:31:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:32:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:33:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:34:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:35:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:35:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:35:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"577f884f-394e-4c7b-b59d-439fd51041d2"}
2025-07-22T10:35:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:35:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:35:00.048Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:36:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:37:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:38:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:39:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:40:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:40:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:40:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c6d054e5-2707-4991-aeb8-aad9383e1224"}
2025-07-22T10:40:00.019Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:40:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:40:00.058Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:41:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:42:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:43:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:44:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:45:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:45:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:45:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"591f3d0a-048c-45f7-b1ad-4b91db0fc1d8"}
2025-07-22T10:45:00.025Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:45:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:45:00.057Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:46:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:47:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:48:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:49:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:50:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:50:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:50:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"dae4b945-b7c2-4503-bb28-6b412f01ff65"}
2025-07-22T10:50:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:50:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:50:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:51:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:52:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:53:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:54:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:55:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:55:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T10:55:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"769ba0b0-0811-4812-ae79-abac5d586814"}
2025-07-22T10:55:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T10:55:00.039Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T10:55:00.040Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T10:56:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:57:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:58:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T10:59:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:00:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:00:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:00:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"35b25869-53fb-48b8-8a1f-063640f443c7"}
2025-07-22T11:00:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:00:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:00:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:01:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:02:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:03:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:04:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:05:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:05:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:05:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5231cf51-4497-42c7-a9f0-71c51a045fae"}
2025-07-22T11:05:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:05:00.045Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:05:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:06:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:07:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:08:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:09:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:10:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:10:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:10:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f0fa7a08-11fa-4fb5-8bec-8a0f36424da2"}
2025-07-22T11:10:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:10:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:10:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:11:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:12:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:13:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:14:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:15:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:15:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:15:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"755dbf0f-01cb-471b-9fa3-11438dfc0c09"}
2025-07-22T11:15:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:15:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:15:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:16:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:17:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:18:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:19:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:20:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:20:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:20:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e918bb03-aa84-4a08-9c49-370cbed8376e"}
2025-07-22T11:20:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:20:00.034Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:20:00.035Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:21:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:22:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:23:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:24:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:25:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:25:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:25:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2ec95d28-6f6f-442d-b527-ac4d5bfbe250"}
2025-07-22T11:25:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:25:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:25:00.044Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:26:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:27:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:28:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:29:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:30:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:30:00.009Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:30:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8948ce8a-b2be-41b7-9a54-ee3d20d448e6"}
2025-07-22T11:30:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:30:00.062Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:30:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:31:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:32:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:33:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:34:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:35:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:35:00.009Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:35:00.010Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"52a77138-4dc6-4890-b5b2-fb20629812fb"}
2025-07-22T11:35:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:35:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:35:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:36:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:37:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:38:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:39:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:40:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:40:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:40:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e2d14d0f-7ce6-4fa8-8782-6e55cd08db63"}
2025-07-22T11:40:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:40:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:40:00.045Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:41:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:42:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:43:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:44:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:45:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:45:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:45:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b9a97271-3158-4a25-b84c-445533507306"}
2025-07-22T11:45:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:45:00.037Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:45:00.037Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:46:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:47:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:48:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:49:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:50:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:50:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:50:00.009Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"866222d4-9e8d-4373-bfb7-14d59c11a209"}
2025-07-22T11:50:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:50:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:50:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:51:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:52:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:53:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:54:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:55:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:55:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T11:55:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"aa1e80c8-5623-4f5a-8264-9f36043866f2"}
2025-07-22T11:55:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T11:55:00.049Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T11:55:00.049Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T11:56:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:57:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:58:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T11:59:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:00:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:00:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:00:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"219f4c13-3377-4ee5-bab9-ef91da2a9e3b"}
2025-07-22T12:00:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:00:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:00:00.048Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:01:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:02:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:03:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:04:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:05:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:05:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:05:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c4243037-29d7-45e7-9351-35ac3de312cd"}
2025-07-22T12:05:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:05:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:05:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:06:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:07:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:08:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:09:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:10:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:10:00.010Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:10:00.010Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"664313df-75f0-4359-9d49-3471d57bb69e"}
2025-07-22T12:10:00.014Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:10:00.048Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:10:00.048Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:11:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:12:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:13:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:14:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:15:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:15:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:15:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ee626e6c-8255-4bad-a32f-3b8eec627b0d"}
2025-07-22T12:15:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:15:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:15:00.044Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:16:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:17:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:18:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:19:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:20:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:20:00.008Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:20:00.008Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"cf304023-c6ec-416c-96bf-d7adac45efe0"}
2025-07-22T12:20:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:20:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:20:00.042Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:21:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:22:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:23:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:24:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:25:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:25:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:25:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b49add0c-a4a9-4786-816c-9cde3a79f20d"}
2025-07-22T12:25:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:25:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:25:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:26:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:27:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:28:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:29:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:30:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:30:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:30:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bed34516-de46-4837-a7c4-c8c3f00ec136"}
2025-07-22T12:30:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:30:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:30:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:31:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:32:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:33:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:34:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:35:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:35:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:35:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8d1a9dae-d82a-42fa-909c-2d75fc706ff5"}
2025-07-22T12:35:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:35:00.039Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:35:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:36:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:37:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:38:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:39:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:40:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:40:00.009Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:40:00.009Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ebfea184-04bf-4c99-92e9-4189d9a8efae"}
2025-07-22T12:40:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:40:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:40:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:41:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:42:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:43:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:44:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:45:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:45:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:45:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"715f9974-066d-4452-8924-76e221f781b3"}
2025-07-22T12:45:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:45:00.081Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:45:00.082Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:46:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:47:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:48:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:49:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:50:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:50:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:50:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"27f7ce7e-3cc9-47cc-90a8-6fb443011183"}
2025-07-22T12:50:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:50:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:50:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:51:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:52:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:53:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:54:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:55:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:55:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T12:55:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c9e9c9d5-d8e5-47a9-9edc-d3112eaa2209"}
2025-07-22T12:55:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T12:55:00.062Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T12:55:00.076Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T12:56:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:57:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:58:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T12:59:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:00:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:00:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:00:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c6f04709-d855-4348-8ab0-eb87d556fd72"}
2025-07-22T13:00:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:00:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:00:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:01:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:02:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:03:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:04:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:05:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:05:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:05:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"362654ab-e85d-4f8c-aa36-bcd268535778"}
2025-07-22T13:05:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:05:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:05:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:06:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:07:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:08:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:09:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:10:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:10:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:10:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a959df4e-7f8a-4f4a-a601-441b18883a49"}
2025-07-22T13:10:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:10:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:10:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:11:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:12:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:13:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:14:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:15:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:15:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:15:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"07637d00-3689-4e8d-b8cd-c807dbf18603"}
2025-07-22T13:15:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:15:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:15:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:16:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:17:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:18:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:19:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:20:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:20:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:20:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5517fcc6-c550-4ea3-b3c8-b9f0f1dca5d6"}
2025-07-22T13:20:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:20:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:20:00.037Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:21:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:22:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:23:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:24:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:25:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:25:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:25:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8677ee8a-0885-4507-ab27-6b7be5a82a46"}
2025-07-22T13:25:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:25:00.024Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:25:00.031Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:26:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:27:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:28:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:29:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:30:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:30:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:30:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bbc85234-4343-4ca9-ac6c-33ee74cfa12c"}
2025-07-22T13:30:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:30:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:30:00.044Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:31:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:32:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:33:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:34:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:35:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:35:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:35:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"94fd2958-d701-4310-9cda-81a3f1d2e789"}
2025-07-22T13:35:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:35:00.014Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:35:00.014Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:36:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:37:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:38:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:39:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:40:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:40:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:40:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"43f2739e-a570-45bc-9908-1ba459a6c0b5"}
2025-07-22T13:40:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:40:00.019Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:40:00.019Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:41:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:42:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:43:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:44:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:45:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:45:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:45:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0841f399-585e-4ba6-98bf-23b3b91ef0e5"}
2025-07-22T13:45:00.007Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:45:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:45:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:46:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:47:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:48:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:49:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:50:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:50:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:50:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"dea7e711-c99d-464d-99a0-d65b544709ef"}
2025-07-22T13:50:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:50:00.043Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:50:00.044Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:51:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:52:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:53:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:54:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:55:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:55:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T13:55:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ae5e8455-8a49-4f52-8862-bbc107690b73"}
2025-07-22T13:55:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T13:55:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T13:55:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T13:56:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:57:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:58:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T13:59:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:00:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:00:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:00:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"10fdc910-372f-44f2-bc14-20e5b600e838"}
2025-07-22T14:00:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:00:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:00:00.042Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:01:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:02:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:03:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:04:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:05:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:05:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:05:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"caa4ad62-f872-4c7f-88b3-3dd665a16486"}
2025-07-22T14:05:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:05:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:05:00.042Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:06:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:07:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:08:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:09:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:10:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:10:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:10:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"36ba231b-6ce7-424f-8420-9e4b309c36e6"}
2025-07-22T14:10:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:10:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:10:00.045Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:11:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:12:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:13:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:14:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:15:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:15:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:15:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b4f8f522-f57e-40a9-9b28-33022890504e"}
2025-07-22T14:15:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:15:00.049Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:15:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:16:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:17:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:18:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:19:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:20:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:20:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:20:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9bc488ec-ca9c-4f20-8dee-fc0c1862520f"}
2025-07-22T14:20:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:20:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:20:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:21:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:22:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:23:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:24:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:25:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:25:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:25:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a0429387-09f1-4f97-8fe2-d359bd5f7b2b"}
2025-07-22T14:25:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:25:00.037Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:25:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:26:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:27:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:28:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:29:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:30:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:30:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:30:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"5ad549ed-a14c-4ffa-846a-0ddc8cafe8ff"}
2025-07-22T14:30:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:30:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:30:00.042Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:31:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:32:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:33:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:34:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:35:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:35:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:35:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"c96ebfe7-381c-4998-83e8-84c81986de63"}
2025-07-22T14:35:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:35:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:35:00.044Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:36:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:37:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:38:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:39:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:40:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:40:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:40:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"44f02bf9-5be5-44e6-8a6a-582ac5b5f6c3"}
2025-07-22T14:40:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:40:00.035Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:40:00.037Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:41:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:42:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:43:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:44:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:45:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:45:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:45:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"469e1dda-66c2-4ae0-9790-0ea414ef3198"}
2025-07-22T14:45:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:45:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:45:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:46:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:47:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:48:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:49:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:50:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:50:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:50:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"42cbbce4-530a-416d-88b9-a12332a9a0b0"}
2025-07-22T14:50:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:50:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:50:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:51:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:52:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:53:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:54:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:55:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:55:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T14:55:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"6a0e9f30-f260-4ea7-918a-be24d2446577"}
2025-07-22T14:55:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T14:55:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T14:55:00.036Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T14:56:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:57:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:58:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T14:59:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:00:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:00:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:00:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"8abb34e4-d5ec-476d-b784-745af9eb31dd"}
2025-07-22T15:00:00.015Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:00:00.047Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:00:00.048Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:01:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:02:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:03:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:04:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:05:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:05:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:05:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"44ce9ae5-621e-4e81-b015-a873e97a9b04"}
2025-07-22T15:05:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:05:00.033Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:05:00.034Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:06:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:07:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:08:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:09:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:10:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:10:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:10:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2469cd45-96bb-4d51-b21f-597e415f24c1"}
2025-07-22T15:10:00.007Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:10:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:10:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:11:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:12:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:13:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:14:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:15:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:15:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:15:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"108da74e-bcfa-42d1-8515-34f5d984dd61"}
2025-07-22T15:15:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:15:00.039Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:15:00.040Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:16:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:17:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:18:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:19:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:20:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:20:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:20:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b9517a54-0402-4b25-ad89-46699006f42a"}
2025-07-22T15:20:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:20:00.034Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:20:00.034Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:21:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:22:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:23:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:24:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:25:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:25:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:25:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"65debb86-48f7-473d-8115-e6ee7fe58bc3"}
2025-07-22T15:25:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:25:00.034Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:25:00.035Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:26:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:27:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:28:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:29:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:30:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:30:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:30:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f8497629-e233-470b-bc04-2ca15875e422"}
2025-07-22T15:30:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:30:00.017Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:30:00.017Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:31:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:32:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:33:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:34:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:35:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:35:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:35:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"34c5c467-a7a5-4714-b58e-f2e7e2eb3394"}
2025-07-22T15:35:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:35:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:35:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:36:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:37:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:38:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:39:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:40:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:40:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:40:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"551903c8-7c48-4320-b66e-6545f17f06b9"}
2025-07-22T15:40:00.012Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:40:00.046Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:40:00.047Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:41:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:42:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:43:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:44:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:45:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:45:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:45:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ed4bb504-fc71-4310-b6f7-40f280976338"}
2025-07-22T15:45:00.017Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:45:00.022Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:45:00.024Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:46:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:47:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:48:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:49:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:50:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:50:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:50:00.024Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"71af19ee-9305-4419-b824-26f9e41a3a09"}
2025-07-22T15:50:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:50:00.031Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:50:00.031Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:51:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:52:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:53:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:54:00.032Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:55:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:55:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T15:55:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ac962a9b-0f5e-491d-b949-24f862865cab"}
2025-07-22T15:55:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T15:55:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T15:55:00.037Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T15:56:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:57:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:58:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T15:59:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:00:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:00:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:00:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9b6cb188-72bf-4193-bbd1-e86241a08091"}
2025-07-22T16:00:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:00:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:00:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:01:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:02:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:03:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:04:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:05:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:05:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:05:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"71403b59-1973-4e98-b1e5-b2268a14a092"}
2025-07-22T16:05:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:05:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:05:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:06:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:07:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:08:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:09:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:10:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:10:00.032Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:10:00.032Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"698a532a-5fb2-4522-ae45-dd685914b1fc"}
2025-07-22T16:10:00.038Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:10:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:10:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:11:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:12:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:13:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:14:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:15:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:15:00.031Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:15:00.031Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"708cecec-b923-4d84-adf2-a967b1f88ae2"}
2025-07-22T16:15:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:15:00.039Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:15:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:16:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:17:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:18:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:19:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:20:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:20:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:20:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"13000c0f-7eec-411b-a0ea-6a5496632245"}
2025-07-22T16:20:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:20:00.037Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:20:00.037Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:21:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:22:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:23:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:24:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:25:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:25:00.015Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:25:00.015Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"af36e8f1-e12f-4347-a405-738888f3818f"}
2025-07-22T16:25:00.019Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:25:00.023Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:25:00.023Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:26:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:27:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:28:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:29:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:30:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:30:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:30:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"628afc21-75a9-47a7-9007-706d14e285ac"}
2025-07-22T16:30:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:30:00.037Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:30:00.037Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:31:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:32:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:33:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:34:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:35:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:35:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:35:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"720f7f87-793c-4e25-872a-c3a84da0cc68"}
2025-07-22T16:35:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:35:00.013Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:35:00.013Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:36:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:37:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:38:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:39:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:40:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:40:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:40:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"aeec0378-4e27-4de4-b301-8d061050e860"}
2025-07-22T16:40:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:40:00.021Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:40:00.022Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:41:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:42:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:43:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:44:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:45:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:45:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:45:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f39e3445-b084-4158-913b-602403c62ba3"}
2025-07-22T16:45:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:45:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:45:00.040Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:46:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:47:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:48:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:49:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:50:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:50:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:50:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d3272531-2355-4154-bf33-a95f89acda53"}
2025-07-22T16:50:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:50:00.037Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:50:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:51:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:52:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:53:00.030Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:54:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:55:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:55:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T16:55:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"bdb8da85-1fdb-471b-bfb3-d77c4594bab1"}
2025-07-22T16:55:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T16:55:00.016Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T16:55:00.016Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T16:56:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:57:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:58:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T16:59:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:00:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:00:00.014Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:00:00.014Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"75ef3ec8-b1f1-4337-bf8a-0cb024e6e086"}
2025-07-22T17:00:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:00:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:00:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:01:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:02:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:03:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:04:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:05:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:05:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:05:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4baf94c4-236b-474f-aaeb-6e9bc0a8c634"}
2025-07-22T17:05:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:05:00.035Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:05:00.035Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:06:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:07:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:08:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:09:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:10:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:10:00.007Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:10:00.007Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"474a135e-def0-4d7d-a989-adebf2f80d73"}
2025-07-22T17:10:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:10:00.023Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:10:00.024Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:11:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:12:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:13:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:14:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:15:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:15:00.024Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:15:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"fc8d4f67-e2f1-4d78-9ad8-44a3db3c84af"}
2025-07-22T17:15:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:15:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:15:00.036Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:16:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:17:00.029Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:18:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:19:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:20:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:20:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:20:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a63cbac3-90b2-4605-a333-c775bf4846b9"}
2025-07-22T17:20:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:20:00.040Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:20:00.041Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:21:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:22:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:23:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:24:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:25:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:25:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:25:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"d9830f05-b0dc-4345-9766-0177b003f9ed"}
2025-07-22T17:25:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:25:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:25:00.036Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:26:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:27:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:28:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:29:00.028Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:30:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:30:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:30:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"62192d50-9558-4c44-b1d6-6f9e15fe8b1d"}
2025-07-22T17:30:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:30:00.016Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:30:00.017Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:31:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:32:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:33:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:34:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:35:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:35:00.036Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:35:00.037Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"40f8b449-2388-4bd2-b057-9bc5687a6101"}
2025-07-22T17:35:00.043Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:35:00.052Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:35:00.052Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:36:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:37:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:38:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:39:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:40:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:40:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:40:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"69dd0b01-0ffb-41c4-8e01-10ea7e600f98"}
2025-07-22T17:40:00.034Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:40:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:40:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:41:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:42:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:43:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:44:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:45:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:45:00.022Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:45:00.022Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"1c822c77-5b99-4bf6-b10f-8244abf7c62a"}
2025-07-22T17:45:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:45:00.030Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:45:00.031Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:46:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:47:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:48:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:49:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:50:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:50:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:50:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"eaaa2c55-a35b-4580-b6f0-19773b3e94c5"}
2025-07-22T17:50:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:50:00.039Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:50:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:51:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:52:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:53:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:54:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:55:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:55:00.015Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T17:55:00.015Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2b97f016-7f70-4352-bd48-eb492746255b"}
2025-07-22T17:55:00.020Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T17:55:00.026Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T17:55:00.026Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T17:56:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:57:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:58:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T17:59:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:00:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:00:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:00:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"486d0e24-bf55-44fb-b168-2afbd0a62b0c"}
2025-07-22T18:00:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:00:00.031Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:00:00.031Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:01:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:02:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:03:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:04:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:05:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:05:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:05:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e093b8d6-b54e-4825-bc79-86475ce58e04"}
2025-07-22T18:05:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:05:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:05:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:06:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:07:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:08:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:09:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:10:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:10:00.017Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:10:00.017Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a3735936-9ae6-47ff-b133-834e90b13480"}
2025-07-22T18:10:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:10:00.028Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:10:00.029Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:11:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:12:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:13:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:14:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:15:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:15:00.020Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:15:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"1247076b-502e-4d00-95f1-5f34e078a8f7"}
2025-07-22T18:15:00.044Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:15:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:15:00.073Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:16:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:17:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:18:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:19:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:20:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:20:00.029Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:20:00.029Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"cf0cf461-326b-4478-8dea-467091a506b9"}
2025-07-22T18:20:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:20:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:20:00.037Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:21:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:22:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:23:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:24:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:25:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:25:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:25:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f865772e-eeee-461c-922e-4c3d576620fa"}
2025-07-22T18:25:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:25:00.036Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:25:00.036Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:26:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:27:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:28:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:29:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:30:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:30:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:30:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"fc4afcea-2793-4269-9456-c76f400ff429"}
2025-07-22T18:30:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:30:00.035Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:30:00.036Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:31:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:32:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:33:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:34:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:35:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:35:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:35:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b21f0f82-e83a-43e0-8445-087660bee935"}
2025-07-22T18:35:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:35:00.031Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:35:00.032Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:36:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:37:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:38:00.000Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:39:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:40:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:40:00.005Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:40:00.005Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"81db1379-0bff-455d-af14-bfc5de5becde"}
2025-07-22T18:40:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:40:00.014Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:40:00.014Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:41:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:42:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:43:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:44:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:45:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:45:00.030Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:45:00.030Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7527ae2c-9529-42e6-b34f-86551711418e"}
2025-07-22T18:45:00.036Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:45:00.041Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:45:00.042Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:46:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:47:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:48:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:49:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:50:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:50:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:50:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"87869bed-8e24-4183-a831-6c47f4b9057e"}
2025-07-22T18:50:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:50:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:50:00.060Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:51:00.004Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:52:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:53:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:54:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:55:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:55:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T18:55:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7a64afeb-d8bc-45cb-8b27-e36c33102c7f"}
2025-07-22T18:55:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T18:55:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T18:55:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T18:56:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:57:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:58:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T18:59:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:00:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:00:00.021Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:00:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e96b3b34-f6bf-4292-a709-d476a99c6d99"}
2025-07-22T19:00:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:00:00.029Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:00:00.029Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:01:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:02:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:03:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:04:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:05:00.010Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:05:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:05:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ba92891a-81d4-471b-a662-d46ac9711778"}
2025-07-22T19:05:00.018Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:05:00.053Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:05:00.055Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:06:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:07:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:08:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:09:00.026Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:10:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:10:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:10:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7b2f353f-6772-4b29-9fdd-0d156fb79f9c"}
2025-07-22T19:10:00.030Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:10:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:10:00.054Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:11:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:12:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:13:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:14:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:15:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:15:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:15:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"2d0421a6-3d3f-4816-a854-3fa6a1b02191"}
2025-07-22T19:15:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:15:00.064Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:15:00.066Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:16:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:17:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:18:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:19:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:20:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:20:00.011Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:20:00.011Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"52e9736c-2eb1-40ba-8ee8-99a6e837dcc9"}
2025-07-22T19:20:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:20:00.042Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:20:00.043Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:21:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:22:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:23:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:24:00.011Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:25:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:25:00.009Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:25:00.009Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"afa3eda0-78fe-4465-8dcc-1ad6d2e6fd1a"}
2025-07-22T19:25:00.013Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:25:00.045Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:25:00.045Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:26:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:27:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:28:00.027Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:29:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:30:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:30:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:30:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"369f1e41-20dc-4158-bf0c-b874cd3389b6"}
2025-07-22T19:30:00.033Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:30:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:30:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:31:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:32:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:33:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:34:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:35:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:35:00.028Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:35:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e4c6fc51-1f75-401d-9f59-b2050db5194e"}
2025-07-22T19:35:00.035Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:35:00.067Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:35:00.067Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:36:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:37:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:38:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:39:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:40:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:40:00.012Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:40:00.012Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"7b94f7a9-448c-4f27-ba6a-f3baef6ffdbc"}
2025-07-22T19:40:00.016Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:40:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:40:00.046Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:41:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:42:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:43:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:44:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:45:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:45:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:45:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"a2f829cc-2f8d-4e31-bb16-800b2a6022b6"}
2025-07-22T19:45:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:45:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:45:00.061Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:46:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:47:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:48:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:49:00.013Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:50:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:50:00.020Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:50:00.020Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4aa2973f-c041-4794-a15a-19d297a6ba1f"}
2025-07-22T19:50:00.027Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:50:00.054Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:50:00.054Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:51:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:52:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:53:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:54:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:55:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:55:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T19:55:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"0fb6c73d-107d-43b2-8526-c8a08eb29b7a"}
2025-07-22T19:55:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T19:55:00.060Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T19:55:00.062Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T19:56:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:57:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:58:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T19:59:00.016Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:00:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:00:00.006Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:00:00.006Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"ee5249be-b30c-4c75-ade0-97be61940b2b"}
2025-07-22T20:00:00.010Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:00:00.038Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:00:00.039Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:01:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:02:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:03:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:04:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:05:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:05:00.023Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:05:00.023Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4d2c0e0b-bceb-4bb1-ad53-b7625b7289fd"}
2025-07-22T20:05:00.028Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:05:00.059Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:05:00.059Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:06:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:07:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:08:00.020Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:09:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:10:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:10:00.025Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:10:00.025Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f64780e2-a0ed-4441-8a38-63771ec910b5"}
2025-07-22T20:10:00.029Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:10:00.058Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:10:00.058Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:11:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:12:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:13:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:14:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:15:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:15:00.026Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:15:00.026Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"85faf7a1-5842-478e-806d-1ce8a95920e3"}
2025-07-22T20:15:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:15:00.064Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:15:00.064Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:16:00.014Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:17:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:18:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:19:00.007Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:20:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:20:00.013Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:20:00.013Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"acf7127d-c039-42c3-a1db-ec594f6c854b"}
2025-07-22T20:20:00.026Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:20:00.064Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:20:00.068Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:21:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:22:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:23:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:24:00.005Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:25:00.002Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:25:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:25:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"cddcc831-b1d1-4b34-9ff2-26b1a918b6c3"}
2025-07-22T20:25:00.009Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:25:00.044Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:25:00.045Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:26:00.015Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:27:00.021Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:28:00.006Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:29:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:30:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:30:00.020Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:30:00.021Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"4cd3bf22-829b-45c0-a91f-ad8365c38181"}
2025-07-22T20:30:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:30:00.056Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:30:00.056Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:31:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:32:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:33:00.018Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:34:00.012Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:35:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:35:00.003Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:35:00.003Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"9e458137-6a70-4f90-a1f8-fbc51bcf4c4d"}
2025-07-22T20:35:00.008Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:35:00.037Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:35:00.038Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:36:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:37:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:38:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:39:00.019Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:40:00.017Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:40:00.019Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:40:00.019Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"f7eaadb1-64e5-4c60-a336-68ad0ccdc981"}
2025-07-22T20:40:00.023Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:40:00.050Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:40:00.050Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:41:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:42:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:43:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:44:00.008Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:45:00.025Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:45:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:45:00.028Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"52853c58-5a24-4e07-8c84-302c9e75d25d"}
2025-07-22T20:45:00.031Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:45:00.073Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:45:00.075Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:46:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:47:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:48:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:49:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:50:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:50:00.027Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:50:00.027Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"e2361397-c81f-4c4b-935e-4c955cf7e882"}
2025-07-22T20:50:00.032Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:50:00.066Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:50:00.066Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:51:00.023Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:52:00.022Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:53:00.024Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:54:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:55:00.001Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:55:00.004Z [info][Jobs]: Starting scheduled job: Jellyfin Recently Added Scan 
2025-07-22T20:55:00.004Z [info][Jellyfin Sync]: Jellyfin Sync Starting {"sessionId":"b67dc98d-c412-4def-8874-670fcaaa2639"}
2025-07-22T20:55:00.011Z [info][Jellyfin Sync]: Beginning to process recently added for library: Películas 
2025-07-22T20:55:00.051Z [error][Jellyfin API]: Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401 {"error":401}
2025-07-22T20:55:00.051Z [error][Jellyfin Sync]: Sync interrupted {"errorMessage":""}
2025-07-22T20:56:00.009Z [debug][Jobs]: Starting scheduled job: Download Sync 
2025-07-22T20:57:00.003Z [debug][Jobs]: Starting scheduled job: Download Sync 
