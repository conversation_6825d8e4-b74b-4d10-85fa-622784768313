{"Protocol":0,"Id":"ebfa39effd9f4254f0573b1ac03f2075","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 3/<PERSON> and <PERSON><PERSON><PERSON> - S03E10 - The Rick<PERSON>rian Mortydate HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":236622746,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S03E10 - The Rickchurian Mortydate HDTV-720p","IsRemote":false,"ETag":"3ccbf1c8a59e40636523a4956c34db5d","RunTimeTicks":13100800000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1025829,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":127,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":128,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1444936,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E10 - The Rickchurian Mortydate HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 libfdk_aac -ac 2 -ab 256000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename "77703bc0f6c75ff567460e8ec29f6676-1.mp4" -start_number 0 -hls_segment_filename "/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676%d.mp4" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x5e0145690980] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E10 - The Rickchurian Mortydate HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : El Ricksajero del Mortimiedo
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Rick trabaja codo con codo con el Presidente en este episodio.
    show            : Rick y Morty
    episode_id      : 10
    season_number   : 3
  Duration: 00:21:50.08, start: 0.000000, bitrate: 1444 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1025 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (eac3 (native) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676-1.mp4' for writing
Output #0, hls, to '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1025 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
  Stream #0:1: Audio: aac, 48000 Hz, stereo, s16, 256 kb/s (default)
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
      Side data:
        audio service type: main
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66760.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66761.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66762.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66763.mp4' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66764.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66765.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66766.mp4' for writing
size=N/A time=00:00:19.34 bitrate=N/A speed=19.2x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66767.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66768.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f66769.mp4' for writing
size=N/A time=00:00:39.14 bitrate=N/A speed=  26x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667610.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667611.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667612.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667613.mp4' for writing
size=N/A time=00:00:59.73 bitrate=N/A speed=29.8x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667614.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667615.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667616.mp4' for writing
size=N/A time=00:01:22.00 bitrate=N/A speed=32.7x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667617.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667618.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667619.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667620.mp4' for writing
size=N/A time=00:01:46.66 bitrate=N/A speed=35.5x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667621.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667622.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667623.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667624.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667625.mp4' for writing
size=N/A time=00:02:12.43 bitrate=N/A speed=37.8x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667626.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667627.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667628.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667629.mp4' for writing
size=N/A time=00:02:40.36 bitrate=N/A speed=  40x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667630.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667631.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667632.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667633.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667634.mp4' for writing
size=N/A time=00:03:08.84 bitrate=N/A speed=41.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667635.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667636.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667637.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667638.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667639.mp4' for writing
size=N/A time=00:03:36.57 bitrate=N/A speed=43.2x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667640.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667641.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667642.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667643.mp4' for writing
size=N/A time=00:04:06.44 bitrate=N/A speed=44.7x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667644.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667645.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667646.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667647.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667648.mp4' for writing
size=N/A time=00:04:31.36 bitrate=N/A speed=45.2x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667649.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667650.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667651.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667652.mp4' for writing
size=N/A time=00:04:56.08 bitrate=N/A speed=45.5x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667653.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667654.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667655.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667656.mp4' for writing
size=N/A time=00:05:23.15 bitrate=N/A speed=46.1x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667657.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667658.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667659.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667660.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667661.mp4' for writing
size=N/A time=00:05:50.89 bitrate=N/A speed=46.7x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667662.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667663.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667664.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667665.mp4' for writing
size=N/A time=00:06:14.42 bitrate=N/A speed=46.8x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667666.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667667.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667668.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667669.mp4' for writing
size=N/A time=00:06:37.44 bitrate=N/A speed=46.7x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667670.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667671.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667672.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667673.mp4' for writing
size=N/A time=00:07:04.32 bitrate=N/A speed=47.1x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667674.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667675.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667676.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667677.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667678.mp4' for writing
size=N/A time=00:07:32.09 bitrate=N/A speed=47.5x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667679.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667680.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667681.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667682.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667683.mp4' for writing
size=N/A time=00:07:59.42 bitrate=N/A speed=47.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667684.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667685.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667686.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667687.mp4' for writing
size=N/A time=00:08:26.79 bitrate=N/A speed=48.2x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667688.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667689.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667690.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667691.mp4' for writing
size=N/A time=00:08:53.71 bitrate=N/A speed=48.5x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667692.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667693.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667694.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667695.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667696.mp4' for writing
size=N/A time=00:09:20.61 bitrate=N/A speed=48.7x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667697.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667698.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f667699.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676100.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676101.mp4' for writing
size=N/A time=00:09:49.48 bitrate=N/A speed=49.1x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676102.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676103.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676104.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676105.mp4' for writing
size=N/A time=00:10:17.02 bitrate=N/A speed=49.3x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676106.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676107.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676108.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676109.mp4' for writing
size=N/A time=00:10:35.90 bitrate=N/A speed=48.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676110.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676111.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676112.mp4' for writing
size=N/A time=00:11:01.07 bitrate=N/A speed=48.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676113.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676114.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676115.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676116.mp4' for writing
size=N/A time=00:11:21.57 bitrate=N/A speed=48.6x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676117.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676118.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676119.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676120.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676121.mp4' for writing
size=N/A time=00:11:49.35 bitrate=N/A speed=48.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676122.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676123.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676124.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676125.mp4' for writing
size=N/A time=00:12:13.71 bitrate=N/A speed=48.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676126.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676127.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676128.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676129.mp4' for writing
size=N/A time=00:12:41.55 bitrate=N/A speed=49.1x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676130.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676131.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676132.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676133.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676134.mp4' for writing
size=N/A time=00:13:09.26 bitrate=N/A speed=49.3x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676135.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676136.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676137.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676138.mp4' for writing
size=N/A time=00:13:36.44 bitrate=N/A speed=49.4x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676139.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676140.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676141.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676142.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676143.mp4' for writing
size=N/A time=00:14:08.53 bitrate=N/A speed=49.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676144.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676145.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676146.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676147.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676148.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676149.mp4' for writing
size=N/A time=00:14:40.53 bitrate=N/A speed=50.3x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676150.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676151.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676152.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676153.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676154.mp4' for writing
size=N/A time=00:15:09.90 bitrate=N/A speed=50.5x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676155.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676156.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676157.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676158.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676159.mp4' for writing
size=N/A time=00:15:37.28 bitrate=N/A speed=50.6x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676160.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676161.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676162.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676163.mp4' for writing
size=N/A time=00:16:03.77 bitrate=N/A speed=50.7x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676164.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676165.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676166.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676167.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676168.mp4' for writing
size=N/A time=00:16:33.83 bitrate=N/A speed=50.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676169.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676170.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676171.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676172.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676173.mp4' for writing
size=N/A time=00:17:05.51 bitrate=N/A speed=51.2x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676174.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676175.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676176.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676177.mp4' for writing
size=N/A time=00:17:32.43 bitrate=N/A speed=51.3x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676178.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676179.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676180.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676181.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676182.mp4' for writing
size=N/A time=00:18:00.70 bitrate=N/A speed=51.4x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676183.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676184.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676185.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676186.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676187.mp4' for writing
size=N/A time=00:18:29.99 bitrate=N/A speed=51.6x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676188.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676189.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676190.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676191.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676192.mp4' for writing
size=N/A time=00:18:58.26 bitrate=N/A speed=51.7x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676193.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676194.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676195.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676196.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676197.mp4' for writing
size=N/A time=00:19:28.44 bitrate=N/A speed=51.9x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676198.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676199.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676200.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676201.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676202.mp4' for writing
size=N/A time=00:19:58.10 bitrate=N/A speed=52.1x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676203.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676204.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676205.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676206.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676207.mp4' for writing
size=N/A time=00:20:27.79 bitrate=N/A speed=52.2x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676208.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676209.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676210.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676211.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676212.mp4' for writing
size=N/A time=00:21:00.43 bitrate=N/A speed=52.5x    
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676213.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676214.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676215.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676216.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676217.mp4' for writing
[hls @ 0x5e01456e66c0] Opening '/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676218.mp4' for writing
[out#0/hls @ 0x5e01456ec8c0] video:164037KiB audio:40941KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:21:25.29 bitrate=N/A speed=52.6x    