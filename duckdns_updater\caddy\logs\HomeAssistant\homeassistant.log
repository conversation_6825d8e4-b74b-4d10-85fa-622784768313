{"level":"error","ts":1752049678.9857109,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"34832","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"],"Priority":["u=0, i"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Site":["none"],"Sec-Ch-Ua-Mobile":["?0"],"Upgrade-Insecure-Requests":["1"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.105753567,"size":42,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1752049679.2340002,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"34832","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Dest":["image"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.0019492,"size":42,"status":503,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752056525.5973513,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["document"],"Sec-Ch-Ua-Mobile":["?0"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Priority":["u=0, i"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.005065133,"size":16,"status":400,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Type":["text/plain; charset=utf-8"],"Date":["Wed, 09 Jul 2025 10:22:05 GMT"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["16"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"]}}
{"level":"info","ts":1752056525.7684598,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Mode":["no-cors"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Dest":["image"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002945283,"size":16,"status":400,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:05 GMT"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752056533.4874358,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-User":["?1"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Upgrade-Insecure-Requests":["1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"],"Priority":["u=0, i"],"Cache-Control":["max-age=0"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.004120155,"size":16,"status":400,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Date":["Wed, 09 Jul 2025 10:22:13 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Content-Length":["16"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752056533.5409486,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Fetch-Dest":["image"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Referer":["https://tankeportainer.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00348516,"size":16,"status":400,"resp_headers":{"Content-Length":["16"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 10:22:13 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"info","ts":1752056533.9335048,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Cache-Control":["max-age=0"],"Sec-Fetch-User":["?1"],"Sec-Fetch-Dest":["document"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002556632,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 10:22:13 GMT"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"]}}
{"level":"info","ts":1752056533.9811485,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["image"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003082969,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Date":["Wed, 09 Jul 2025 10:22:13 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Content-Length":["16"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752056534.1131713,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-User":["?1"],"Cache-Control":["max-age=0"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["document"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0, i"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00251758,"size":16,"status":400,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Content-Type":["text/plain; charset=utf-8"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["16"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752056534.1666787,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Dest":["image"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003045813,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752056534.2844024,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Mode":["navigate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-User":["?1"],"Priority":["u=0, i"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003137206,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752056534.333109,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Fetch-Dest":["image"],"Referer":["https://tankeportainer.duckdns.org/"],"Sec-Fetch-Mode":["no-cors"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003698854,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"]}}
{"level":"info","ts":1752056534.4323266,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Upgrade-Insecure-Requests":["1"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-User":["?1"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002350973,"size":16,"status":400,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"]}}
{"level":"info","ts":1752056534.4842393,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Priority":["u=1, i"],"Referer":["https://tankeportainer.duckdns.org/"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["image"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003935809,"size":16,"status":400,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Length":["16"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752056534.5918121,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-User":["?1"],"Cache-Control":["max-age=0"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["none"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Mode":["navigate"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Dest":["document"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002328923,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752056534.6435363,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Dest":["image"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Referer":["https://tankeportainer.duckdns.org/"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["no-cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003833654,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752056534.7631972,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-User":["?1"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0, i"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Platform":["\"Windows\""],"Upgrade-Insecure-Requests":["1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003045013,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752056534.8162477,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Fetch-Dest":["image"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Mode":["no-cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003422178,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752056534.9382017,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Upgrade-Insecure-Requests":["1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cache-Control":["max-age=0"],"Sec-Fetch-Site":["none"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002116614,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Content-Length":["16"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752056534.98915,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["no-cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeportainer.duckdns.org/"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Dest":["image"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003852145,"size":16,"status":400,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 10:22:14 GMT"],"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"]}}
{"level":"info","ts":1752056535.2568192,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Mobile":["?0"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["navigate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Site":["none"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-User":["?1"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Dest":["document"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002562822,"size":16,"status":400,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"X-Frame-Options":["SAMEORIGIN"],"Date":["Wed, 09 Jul 2025 10:22:15 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752056535.3059552,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Fetch-Dest":["image"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003769742,"size":16,"status":400,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Date":["Wed, 09 Jul 2025 10:22:15 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"]}}
{"level":"info","ts":1752056535.4185863,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Mode":["navigate"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["document"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002477043,"size":16,"status":400,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 10:22:15 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"]}}
{"level":"info","ts":1752056535.4701922,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["image"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003628937,"size":16,"status":400,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 10:22:15 GMT"],"Content-Type":["text/plain; charset=utf-8"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Content-Length":["16"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752056535.5671027,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Sec-Fetch-User":["?1"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cache-Control":["max-age=0"],"Sec-Fetch-Site":["none"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["document"],"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Upgrade-Insecure-Requests":["1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Mode":["navigate"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002633125,"size":16,"status":400,"resp_headers":{"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["text/plain; charset=utf-8"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:15 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752056535.6161914,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Priority":["u=1, i"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Dest":["image"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Referer":["https://tankeportainer.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003705119,"size":16,"status":400,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 10:22:15 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752056542.3614209,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Upgrade-Insecure-Requests":["1"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cache-Control":["max-age=0"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Dest":["document"],"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-User":["?1"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Site":["none"],"Sec-Fetch-Mode":["navigate"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002233242,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:22 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752056542.4075267,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["image"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003641865,"size":16,"status":400,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:22 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"]}}
{"level":"info","ts":1752056542.5107024,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Upgrade-Insecure-Requests":["1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002495493,"size":16,"status":400,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:22 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752056542.5563095,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Fetch-Dest":["image"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeportainer.duckdns.org/"],"Sec-Fetch-Mode":["no-cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003405666,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Content-Length":["16"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Date":["Wed, 09 Jul 2025 10:22:22 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752056542.6688802,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"Sec-Fetch-Mode":["navigate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002385678,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 10:22:22 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"X-Frame-Options":["SAMEORIGIN"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752056542.734831,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Fetch-Dest":["image"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["no-cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003153124,"size":16,"status":400,"resp_headers":{"Content-Length":["16"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"Date":["Wed, 09 Jul 2025 10:22:22 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752056542.8014717,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Dest":["document"],"Priority":["u=0, i"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002109998,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 10:22:22 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752056542.8558218,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"45960","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeportainer.duckdns.org/"],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["image"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002986705,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 10:22:22 GMT"],"Content-Length":["16"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Type":["text/plain; charset=utf-8"],"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752058867.3195863,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"48156","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Sec-Fetch-User":["?1"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"]},"tls":{"resumed":true,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.005515196,"size":16,"status":400,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Content-Length":["16"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 11:01:07 GMT"]}}
{"level":"info","ts":1752058867.4549906,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"48156","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeportainer.duckdns.org","uri":"/favicon.ico","headers":{"Priority":["u=1, i"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeportainer.duckdns.org/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["image"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":true,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003444772,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 11:01:07 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["16"],"Content-Type":["text/plain; charset=utf-8"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752059039.5537755,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"33242","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeeee2.duckdns.org","uri":"/.well-known/acme-challenge/XTkgbGxHqzyB6_9rhE9CEi3KOq2lXzvDUo4ETGB_wXY","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000091964,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1752059040.2520514,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"33254","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeeee2.duckdns.org","uri":"/.well-known/acme-challenge/XTkgbGxHqzyB6_9rhE9CEi3KOq2lXzvDUo4ETGB_wXY","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000048382,"size":87,"status":200,"resp_headers":{"Content-Type":["text/plain"],"Server":["Caddy"]}}
{"level":"info","ts":1752059040.450204,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"33264","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeeee2.duckdns.org","uri":"/.well-known/acme-challenge/XTkgbGxHqzyB6_9rhE9CEi3KOq2lXzvDUo4ETGB_wXY","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000047902,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1752059040.954862,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"58740","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeeee2.duckdns.org","uri":"/.well-known/acme-challenge/XTkgbGxHqzyB6_9rhE9CEi3KOq2lXzvDUo4ETGB_wXY","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000045992,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1752059041.9414895,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"58748","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeeee2.duckdns.org","uri":"/.well-known/acme-challenge/XTkgbGxHqzyB6_9rhE9CEi3KOq2lXzvDUo4ETGB_wXY","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000055282,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1752059048.2572677,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"40098","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003800314,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 11:04:08 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"]}}
{"level":"info","ts":1752059065.1611803,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"47178","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002719335,"size":16,"status":400,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 11:04:25 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752059066.4873352,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"47180","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002409349,"size":16,"status":400,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 11:04:26 GMT"],"Content-Length":["16"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"]}}
{"level":"info","ts":1752059111.5826762,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"33762","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002465522,"size":16,"status":400,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 11:05:11 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752059152.136401,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"38080","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"Accept-Encoding":["gzip, deflate, br"],"Accept-Language":["en-US,en;q=0.9"],"Sec-Fetch-Mode":["navigate"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Connection":["keep-alive"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"http/1.1","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002496979,"size":16,"status":400,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 11:05:52 GMT"]}}
{"level":"info","ts":1752059159.7922025,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"49274","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"Connection":["keep-alive"],"User-Agent":["Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111"],"Accept-Encoding":["gzip, deflate, br"],"Accept-Language":["en-US,en;q=0.9"],"Sec-Fetch-Mode":["navigate"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"http/1.1","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00279512,"size":16,"status":400,"resp_headers":{"Date":["Wed, 09 Jul 2025 11:05:59 GMT"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"X-Content-Type-Options":["nosniff"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752059167.4539974,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"53382","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"Connection":["keep-alive"],"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111"],"Accept-Encoding":["gzip, deflate, br"],"Accept-Language":["en-US,en;q=0.9"],"Sec-Fetch-Mode":["navigate"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"http/1.1","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002118542,"size":16,"status":400,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Date":["Wed, 09 Jul 2025 11:06:07 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752059169.105851,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"53394","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002369863,"size":16,"status":400,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Content-Length":["16"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: *; connect-src 'self' ws: wss: https: *; font-src 'self' data: https://fonts.gstatic.com; media-src 'self' blob: data: https: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Date":["Wed, 09 Jul 2025 11:06:09 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"error","ts":1752059217.3989294,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"33162","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeeee2.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeeee2.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002607342,"size":42,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
