2025-07-17 17:15:15.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 17:15:15.2|Warn|Torznab|Unable to connect to indexer

[v5.26.2.10099] NzbDrone.Common.Http.HttpException: HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Radarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Indexers.HttpIndexerBase`1.FetchIndexerResponse(IndexerRequest request) in ./Radarr.Core/Indexers/HttpIndexerBase.cs:line 335
   at NzbDrone.Core.Indexers.HttpIndexerBase`1.FetchPage(IndexerRequest request, IParseIndexerResponse parser) in ./Radarr.Core/Indexers/HttpIndexerBase.cs:line 317
   at NzbDrone.Core.Indexers.HttpIndexerBase`1.TestConnection() in ./Radarr.Core/Indexers/HttpIndexerBase.cs:line 458
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />


2025-07-17 17:16:00.7|Info|RssSyncService|Starting RSS Sync
2025-07-17 17:16:01.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 14:45:01 and 07/17/2025 14:45:01 UTC. Search may be required.
2025-07-17 17:16:01.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 14:45:01 and 07/17/2025 14:45:01 UTC. Search may be required.
2025-07-17 17:16:03.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 17:16:03.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 14:45:05 and 07/17/2025 14:45:05 UTC. Search may be required.
2025-07-17 17:16:03.1|Info|DownloadDecisionMaker|Processing 707 releases
2025-07-17 17:16:43.5|Info|RssSyncService|RSS Sync Completed. Reports found: 707, Reports grabbed: 0
2025-07-17 17:27:51.3|Info|AddMovieService|Adding Movie [Despicable Me 4 (2024)][tt7510222, 519182] Path: [/CONTENIDO/PELIS/Despicable Me 4 (2024)]
2025-07-17 17:27:51.3|Info|RefreshMovieService|Updating info for Despicable Me 4
2025-07-17 17:27:51.4|Info|AddMovieCollectionService|Adding Collection Despicable Me Collection[86066]
2025-07-17 17:27:51.5|Info|RefreshCollectionService|Updating info for Despicable Me Collection
2025-07-17 17:27:51.7|Info|MediaCoverService|Downloading Poster for [Despicable Me 4 (2024)][tt7510222, 519182] https://image.tmdb.org/t/p/original/wWba3TaojhK7NdycRhoQpsG0FaH.jpg
2025-07-17 17:27:51.7|Info|DiskScanService|Scanning disk for Despicable Me 4
2025-07-17 17:27:51.8|Warn|MediaCoverMapper|File /config/MediaCover/76/poster.jpg not found
2025-07-17 17:27:51.8|Info|DiskScanService|Completed scanning disk for Despicable Me 4
2025-07-17 17:27:51.9|Info|MovieScannedHandler|[Despicable Me 4] was recently added, performing post-add actions
2025-07-17 17:27:51.9|Info|MovieSearchService|Performing search for 1 movies
2025-07-17 17:27:51.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:27:52.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:27:52.0|Info|ReleaseSearchService|Searching indexers for [Despicable Me 4]. 11 active indexers
2025-07-17 17:27:52.0|Info|MediaCoverService|Downloading Fanart for [Despicable Me 4 (2024)][tt7510222, 519182] https://image.tmdb.org/t/p/original/lgkPzcOSnTvjeMnuFzozRO5HHw1.jpg
2025-07-17 17:27:52.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:27:52.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:27:52.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=search&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 17:27:52.6|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=search&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024]
2025-07-17 17:27:52.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024: 400.BadRequest (7349 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (1337x): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 17:27:52.9|Warn|Torznab|Jackett 1337x  HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024]
2025-07-17 17:28:02.0|Info|RefreshMovieService|Updating info for Babylon
2025-07-17 17:28:02.1|Info|DiskScanService|Scanning disk for Babylon
2025-07-17 17:28:02.3|Info|DiskScanService|Completed scanning disk for Babylon
2025-07-17 17:28:02.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:02.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:02.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:02.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:02.5|Info|RefreshMovieService|Updating info for Captain America: Brave New World
2025-07-17 17:28:02.6|Info|DiskScanService|Scanning disk for Captain America: Brave New World
2025-07-17 17:28:02.7|Info|DiskScanService|Completed scanning disk for Captain America: Brave New World
2025-07-17 17:28:02.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:02.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:02.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:02.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:02.9|Info|RefreshMovieService|Updating info for How to Train Your Dragon
2025-07-17 17:28:03.0|Info|DiskScanService|Scanning disk for How to Train Your Dragon
2025-07-17 17:28:03.1|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon
2025-07-17 17:28:03.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:03.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:03.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:03.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:03.2|Info|RefreshMovieService|Updating info for How to Train Your Dragon
2025-07-17 17:28:03.3|Info|DiskScanService|Scanning disk for How to Train Your Dragon
2025-07-17 17:28:03.4|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon
2025-07-17 17:28:03.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:03.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:03.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:03.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:03.5|Info|RefreshMovieService|Updating info for How to Train Your Dragon 2
2025-07-17 17:28:03.6|Info|DiskScanService|Scanning disk for How to Train Your Dragon 2
2025-07-17 17:28:03.7|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon 2
2025-07-17 17:28:03.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:03.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:03.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:03.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:03.9|Info|RefreshMovieService|Updating info for How to Train Your Dragon: The Hidden World
2025-07-17 17:28:04.0|Info|DiskScanService|Scanning disk for How to Train Your Dragon: The Hidden World
2025-07-17 17:28:04.1|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon: The Hidden World
2025-07-17 17:28:04.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:04.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:04.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:04.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:04.2|Info|RefreshMovieService|Updating info for Final Destination
2025-07-17 17:28:04.5|Info|DiskScanService|Scanning disk for Final Destination
2025-07-17 17:28:04.6|Info|DiskScanService|Completed scanning disk for Final Destination
2025-07-17 17:28:04.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:04.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:04.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:04.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:04.8|Info|RefreshMovieService|Updating info for Final Destination 2
2025-07-17 17:28:04.9|Info|DiskScanService|Scanning disk for Final Destination 2
2025-07-17 17:28:05.0|Info|DiskScanService|Completed scanning disk for Final Destination 2
2025-07-17 17:28:05.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:05.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:05.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:05.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:05.1|Info|RefreshMovieService|Updating info for Final Destination 3
2025-07-17 17:28:05.2|Info|DiskScanService|Scanning disk for Final Destination 3
2025-07-17 17:28:05.3|Info|DiskScanService|Completed scanning disk for Final Destination 3
2025-07-17 17:28:05.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:05.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:05.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:05.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:05.4|Info|RefreshMovieService|Updating info for The Final Destination
2025-07-17 17:28:05.6|Info|DiskScanService|Scanning disk for The Final Destination
2025-07-17 17:28:05.7|Info|DiskScanService|Completed scanning disk for The Final Destination
2025-07-17 17:28:05.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:05.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:05.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:05.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:05.8|Info|RefreshMovieService|Updating info for Final Destination 5
2025-07-17 17:28:06.0|Info|DiskScanService|Scanning disk for Final Destination 5
2025-07-17 17:28:06.4|Info|DiskScanService|Completed scanning disk for Final Destination 5
2025-07-17 17:28:06.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:06.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:06.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:06.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:06.7|Info|RefreshMovieService|Updating info for Final Destination Bloodlines
2025-07-17 17:28:06.9|Info|DiskScanService|Scanning disk for Final Destination Bloodlines
2025-07-17 17:28:06.9|Info|DiskScanService|Completed scanning disk for Final Destination Bloodlines
2025-07-17 17:28:07.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:07.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:07.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:07.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:07.1|Info|RefreshMovieService|Updating info for Django Unchained
2025-07-17 17:28:07.2|Info|DiskScanService|Scanning disk for Django Unchained
2025-07-17 17:28:07.4|Info|DiskScanService|Completed scanning disk for Django Unchained
2025-07-17 17:28:07.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:07.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:07.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:07.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:07.6|Info|RefreshMovieService|Updating info for The Accountant²
2025-07-17 17:28:07.7|Info|DiskScanService|Scanning disk for The Accountant²
2025-07-17 17:28:07.8|Info|DiskScanService|Completed scanning disk for The Accountant²
2025-07-17 17:28:07.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:07.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:07.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:07.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:07.9|Info|RefreshMovieService|Updating info for The Lost World: Jurassic Park
2025-07-17 17:28:08.0|Info|DiskScanService|Scanning disk for The Lost World: Jurassic Park
2025-07-17 17:28:08.1|Info|DiskScanService|Completed scanning disk for The Lost World: Jurassic Park
2025-07-17 17:28:08.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:08.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:08.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:08.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:08.2|Info|RefreshMovieService|Updating info for The Revenant
2025-07-17 17:28:08.5|Info|DiskScanService|Scanning disk for The Revenant
2025-07-17 17:28:08.6|Info|DiskScanService|Completed scanning disk for The Revenant
2025-07-17 17:28:08.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:08.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:08.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:08.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:08.8|Info|RefreshMovieService|Updating info for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 17:28:09.0|Info|DiskScanService|Scanning disk for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 17:28:09.1|Info|DiskScanService|Completed scanning disk for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 17:28:09.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:09.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:09.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:09.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:09.2|Info|RefreshMovieService|Updating info for Despicable Me 4
2025-07-17 17:28:09.4|Info|DiskScanService|Scanning disk for Despicable Me 4
2025-07-17 17:28:09.4|Info|DiskScanService|Completed scanning disk for Despicable Me 4
2025-07-17 17:28:09.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:09.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:09.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:09.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:09.6|Info|RefreshMovieService|Updating info for Jurassic Park
2025-07-17 17:28:09.8|Info|DiskScanService|Scanning disk for Jurassic Park
2025-07-17 17:28:09.9|Info|DiskScanService|Completed scanning disk for Jurassic Park
2025-07-17 17:28:10.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:10.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:10.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:10.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:10.1|Info|RefreshMovieService|Updating info for Kung Fu Panda
2025-07-17 17:28:10.2|Info|DiskScanService|Scanning disk for Kung Fu Panda
2025-07-17 17:28:10.2|Info|DiskScanService|Completed scanning disk for Kung Fu Panda
2025-07-17 17:28:10.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:10.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:10.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:10.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:10.4|Info|RefreshMovieService|Updating info for Kung Fu Panda 2
2025-07-17 17:28:10.5|Info|DiskScanService|Scanning disk for Kung Fu Panda 2
2025-07-17 17:28:10.6|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 2
2025-07-17 17:28:10.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:10.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:10.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:10.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:10.7|Info|RefreshMovieService|Updating info for Kung Fu Panda 3
2025-07-17 17:28:10.8|Info|DiskScanService|Scanning disk for Kung Fu Panda 3
2025-07-17 17:28:10.9|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 3
2025-07-17 17:28:10.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:11.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:11.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:11.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:11.0|Info|RefreshMovieService|Updating info for Kung Fu Panda 4
2025-07-17 17:28:11.1|Info|DiskScanService|Scanning disk for Kung Fu Panda 4
2025-07-17 17:28:11.2|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 4
2025-07-17 17:28:11.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:11.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:11.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:11.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:11.4|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-17 17:28:11.5|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-17 17:28:11.6|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-17 17:28:11.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:11.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:11.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:11.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:11.8|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: Prince Caspian
2025-07-17 17:28:11.9|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: Prince Caspian
2025-07-17 17:28:12.1|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: Prince Caspian
2025-07-17 17:28:12.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:12.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:12.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:12.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:12.2|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 17:28:12.4|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 17:28:12.6|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 17:28:12.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:12.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:12.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:12.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:12.8|Info|RefreshMovieService|Updating info for The Hateful Eight
2025-07-17 17:28:12.9|Info|DiskScanService|Scanning disk for The Hateful Eight
2025-07-17 17:28:13.0|Info|DiskScanService|Completed scanning disk for The Hateful Eight
2025-07-17 17:28:13.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:13.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:13.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:13.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:13.2|Info|RefreshMovieService|Updating info for Sinners
2025-07-17 17:28:13.3|Info|DiskScanService|Scanning disk for Sinners
2025-07-17 17:28:13.3|Info|DiskScanService|Completed scanning disk for Sinners
2025-07-17 17:28:13.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:13.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:13.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:13.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:13.5|Info|RefreshMovieService|Updating info for Inglourious Basterds
2025-07-17 17:28:13.8|Info|DiskScanService|Scanning disk for Inglourious Basterds
2025-07-17 17:28:13.8|Info|DiskScanService|Completed scanning disk for Inglourious Basterds
2025-07-17 17:28:13.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:13.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:13.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:13.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:14.0|Info|RefreshMovieService|Updating info for Cast Away
2025-07-17 17:28:14.1|Info|DiskScanService|Scanning disk for Cast Away
2025-07-17 17:28:14.2|Info|DiskScanService|Completed scanning disk for Cast Away
2025-07-17 17:28:14.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:14.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:14.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:14.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:14.4|Info|RefreshMovieService|Updating info for Ocean's Eight
2025-07-17 17:28:14.6|Info|DiskScanService|Scanning disk for Ocean's Eight
2025-07-17 17:28:14.7|Info|DiskScanService|Completed scanning disk for Ocean's Eight
2025-07-17 17:28:14.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:14.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:14.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:14.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:14.8|Info|RefreshMovieService|Updating info for Ocean's Eleven
2025-07-17 17:28:15.0|Info|DiskScanService|Scanning disk for Ocean's Eleven
2025-07-17 17:28:15.1|Info|DiskScanService|Completed scanning disk for Ocean's Eleven
2025-07-17 17:28:15.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:15.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:15.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:15.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:15.2|Info|RefreshMovieService|Updating info for Ocean's Thirteen
2025-07-17 17:28:15.4|Info|DiskScanService|Scanning disk for Ocean's Thirteen
2025-07-17 17:28:15.5|Info|DiskScanService|Completed scanning disk for Ocean's Thirteen
2025-07-17 17:28:15.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:15.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:15.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:15.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:15.7|Info|RefreshMovieService|Updating info for Ocean's Twelve
2025-07-17 17:28:15.9|Info|DiskScanService|Scanning disk for Ocean's Twelve
2025-07-17 17:28:16.0|Info|DiskScanService|Completed scanning disk for Ocean's Twelve
2025-07-17 17:28:16.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:16.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:16.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:16.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:16.2|Info|RefreshMovieService|Updating info for Oppenheimer
2025-07-17 17:28:16.3|Info|DiskScanService|Scanning disk for Oppenheimer
2025-07-17 17:28:16.4|Info|DiskScanService|Completed scanning disk for Oppenheimer
2025-07-17 17:28:16.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:16.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:16.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:16.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:16.6|Info|RefreshMovieService|Updating info for Prometheus
2025-07-17 17:28:16.7|Info|DiskScanService|Scanning disk for Prometheus
2025-07-17 17:28:16.8|Info|DiskScanService|Completed scanning disk for Prometheus
2025-07-17 17:28:16.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:16.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:16.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:16.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:17.0|Info|RefreshMovieService|Updating info for Shutter Island
2025-07-17 17:28:17.1|Info|DiskScanService|Scanning disk for Shutter Island
2025-07-17 17:28:17.2|Info|DiskScanService|Completed scanning disk for Shutter Island
2025-07-17 17:28:17.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:17.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:17.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:17.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:17.4|Info|RefreshMovieService|Updating info for A Quiet Place: Day One
2025-07-17 17:28:17.5|Info|DiskScanService|Scanning disk for A Quiet Place: Day One
2025-07-17 17:28:17.6|Info|DiskScanService|Completed scanning disk for A Quiet Place: Day One
2025-07-17 17:28:17.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:17.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:17.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:17.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:17.8|Info|RefreshMovieService|Updating info for A Minecraft Movie
2025-07-17 17:28:17.9|Info|DiskScanService|Scanning disk for A Minecraft Movie
2025-07-17 17:28:18.0|Info|DiskScanService|Completed scanning disk for A Minecraft Movie
2025-07-17 17:28:18.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:18.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:18.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:18.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:18.2|Info|RefreshMovieService|Updating info for Until Dawn
2025-07-17 17:28:18.3|Info|DiskScanService|Scanning disk for Until Dawn
2025-07-17 17:28:18.4|Info|DiskScanService|Completed scanning disk for Until Dawn
2025-07-17 17:28:18.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:18.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:18.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:18.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:18.6|Info|RefreshMovieService|Updating info for Avengers: Infinity War
2025-07-17 17:28:18.8|Info|DiskScanService|Scanning disk for Avengers: Infinity War
2025-07-17 17:28:18.9|Info|DiskScanService|Completed scanning disk for Avengers: Infinity War
2025-07-17 17:28:18.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:28:19.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:28:19.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:28:19.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:28:52.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/bitsearch/results/torznab/api?t=search&cat=2000,8000,8010,5000&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024: 400.BadRequest (1767 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (bitsearch): Request to https://bitsearch.to/search?q=Despicable%20Me%204%202024&amp;sort=date&amp;order=desc&amp;limit=100 failed (Error GatewayTimeout) - The tracker seems to be down.&#xA; ---&gt; System.Exception: Request to https://bitsearch.to/search?q=Despicable%20Me%204%202024&amp;sort=date&amp;order=desc&amp;limit=100 failed (Error GatewayTimeout) - The tracker seems to be down.&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.CheckSiteDown(WebResult response) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 684&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 618&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 17:28:52.7|Warn|Torznab|BitSearch HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/bitsearch/results/torznab/api?t=search&cat=2000,8000,8010,5000&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024]
2025-07-17 17:28:52.8|Info|DownloadDecisionMaker|Processing 6 releases
2025-07-17 17:28:54.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loYlNNMDYxWUUydEZfMy1qU2t5TU92THVBeDlzV3ktdC1qSDdBMi1naktOTmJrZjlMSnB2OUYta1IwcFpnRXFiRHg0LXQ1eWtXMjlqZ29CVGVKbEhpanA1eGE0UVpwTFRKblFNcndWX09qU3dIbGJnTHZtMkdTb040MExMTE1TX1o0M0JNYjBGRERoQTBYd21rYXBKbUtUeVBOcjNlZVdoNEVydC16dkZSYXkzZHZCS1VybTFmQjFKWGRDSmdKTDNj&file=GRU+4+MI+VILLANO+FAVORITO+(2024)+BluRay+1080p-EMUWAREZ.mkv+Spanish: 404.NotFound (0 bytes)
2025-07-17 17:28:54.5|Error|QBittorrent|Downloading torrent file for movie 'GRU 4 MI VILLANO FAVORITO (2024) BluRay 1080p-EMUWAREZ.mkv Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loYlNNMDYxWUUydEZfMy1qU2t5TU92THVBeDlzV3ktdC1qSDdBMi1naktOTmJrZjlMSnB2OUYta1IwcFpnRXFiRHg0LXQ1eWtXMjlqZ29CVGVKbEhpanA1eGE0UVpwTFRKblFNcndWX09qU3dIbGJnTHZtMkdTb040MExMTE1TX1o0M0JNYjBGRERoQTBYd21rYXBKbUtUeVBOcjNlZVdoNEVydC16dkZSYXkzZHZCS1VybTFmQjFKWGRDSmdKTDNj&file=GRU+4+MI+VILLANO+FAVORITO+(2024)+BluRay+1080p-EMUWAREZ.mkv+Spanish)

[v5.26.2.10099] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loYlNNMDYxWUUydEZfMy1qU2t5TU92THVBeDlzV3ktdC1qSDdBMi1naktOTmJrZjlMSnB2OUYta1IwcFpnRXFiRHg0LXQ1eWtXMjlqZ29CVGVKbEhpanA1eGE0UVpwTFRKblFNcndWX09qU3dIbGJnTHZtMkdTb040MExMTE1TX1o0M0JNYjBGRERoQTBYd21rYXBKbUtUeVBOcjNlZVdoNEVydC16dkZSYXkzZHZCS1VybTFmQjFKWGRDSmdKTDNj&file=GRU+4+MI+VILLANO+FAVORITO+(2024)+BluRay+1080p-EMUWAREZ.mkv+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Radarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Radarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteMovie remoteMovie, IIndexer indexer, String torrentUrl) in ./Radarr.Core/Download/TorrentClientBase.cs:line 142



2025-07-17 17:28:54.7|Warn|ProcessDownloadDecisions|Failed to download release 'GRU 4 MI VILLANO FAVORITO (2024) BluRay 1080p-EMUWAREZ.mkv Spanish' from Indexer Emuwarez. Release not available
2025-07-17 17:29:03.5|Info|DownloadService|Report for Despicable Me 4 (2024) sent to qBittorrent from indexer Wolfmax. Gru 4 Mi villano favorito (2024)  [BluRay 1080p] SPANISH
2025-07-17 17:29:03.6|Info|MovieSearchService|Completed search for 1 movies. 1 reports downloaded.
2025-07-17 17:33:12.6|Info|AddMovieService|Adding Movie [John Wick: Chapter 4 (2023)][tt10366206, 603692] Path: [/CONTENIDO/PELIS/John Wick - Chapter 4 (2023)]
2025-07-17 17:33:12.7|Info|RefreshMovieService|Updating info for John Wick: Chapter 4
2025-07-17 17:33:13.0|Info|AddMovieCollectionService|Adding Collection John Wick Collection[404609]
2025-07-17 17:33:13.1|Info|RefreshCollectionService|Updating info for John Wick Collection
2025-07-17 17:33:13.4|Info|MediaCoverService|Downloading Poster for [John Wick: Chapter 4 (2023)][tt10366206, 603692] https://image.tmdb.org/t/p/original/vZloFAK7NmvMGKE7VkF5UHaz0I.jpg
2025-07-17 17:33:13.4|Info|DiskScanService|Scanning disk for John Wick: Chapter 4
2025-07-17 17:33:13.5|Info|DiskScanService|Completed scanning disk for John Wick: Chapter 4
2025-07-17 17:33:13.5|Info|MovieScannedHandler|[John Wick: Chapter 4] was recently added, performing post-add actions
2025-07-17 17:33:13.6|Info|MovieSearchService|Performing search for 1 movies
2025-07-17 17:33:13.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:33:13.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:33:13.7|Info|ReleaseSearchService|Searching indexers for [John Wick: Chapter 4]. 11 active indexers
2025-07-17 17:33:13.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:33:13.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:33:13.9|Info|MediaCoverService|Downloading Fanart for [John Wick: Chapter 4 (2023)][tt10366206, 603692] https://image.tmdb.org/t/p/original/7I6VUdPj6tQECNHdviJkUHD2u89.jpg
2025-07-17 17:33:14.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=search&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100&q=John%20Wick%20Chapter%204%202023: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 17:33:14.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=John%20Wick%3A%20Chapter%204%202023: 400.BadRequest (7349 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (1337x): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 17:33:14.7|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=search&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100&q=John%20Wick%20Chapter%204%202023]
2025-07-17 17:33:14.7|Warn|Torznab|Jackett 1337x  HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=John%20Wick%3A%20Chapter%204%202023]
2025-07-17 17:33:22.9|Info|DownloadDecisionMaker|Processing 17 releases
2025-07-17 17:33:26.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loaFpQTlM3VkFWUDFWanFuYXdLMWw1RDJpcmtxeXZDVU1qbmhCX0xoNGstdjdzZ3JOMzA2dXJOTmJjaEQ0OWZuRVZfSVJ5R3N4VmJxRFZad1VZdXZkMUNtcUo5c3FydU80dy0yVWZaVFhCTWFrTlU5MFhlY0pTVDI3bGdxLXRpYVZfdlZ4MGYwZzF0aGRLa0c2c21TZFhOdzFfcmZUWXdxakw2cE5YWDcwWG1LWTNWVldoSjlkcE1QMVR5R0lZVl9F&file=John+Wick+Chapter+4(2023)+Bluray-1080p+Spanish.mkv+Spanish: 404.NotFound (0 bytes)
2025-07-17 17:33:26.8|Error|QBittorrent|Downloading torrent file for movie 'John Wick Chapter 4(2023) Bluray-1080p Spanish.mkv Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loaFpQTlM3VkFWUDFWanFuYXdLMWw1RDJpcmtxeXZDVU1qbmhCX0xoNGstdjdzZ3JOMzA2dXJOTmJjaEQ0OWZuRVZfSVJ5R3N4VmJxRFZad1VZdXZkMUNtcUo5c3FydU80dy0yVWZaVFhCTWFrTlU5MFhlY0pTVDI3bGdxLXRpYVZfdlZ4MGYwZzF0aGRLa0c2c21TZFhOdzFfcmZUWXdxakw2cE5YWDcwWG1LWTNWVldoSjlkcE1QMVR5R0lZVl9F&file=John+Wick+Chapter+4(2023)+Bluray-1080p+Spanish.mkv+Spanish)

[v5.26.2.10099] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loaFpQTlM3VkFWUDFWanFuYXdLMWw1RDJpcmtxeXZDVU1qbmhCX0xoNGstdjdzZ3JOMzA2dXJOTmJjaEQ0OWZuRVZfSVJ5R3N4VmJxRFZad1VZdXZkMUNtcUo5c3FydU80dy0yVWZaVFhCTWFrTlU5MFhlY0pTVDI3bGdxLXRpYVZfdlZ4MGYwZzF0aGRLa0c2c21TZFhOdzFfcmZUWXdxakw2cE5YWDcwWG1LWTNWVldoSjlkcE1QMVR5R0lZVl9F&file=John+Wick+Chapter+4(2023)+Bluray-1080p+Spanish.mkv+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Radarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Radarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteMovie remoteMovie, IIndexer indexer, String torrentUrl) in ./Radarr.Core/Download/TorrentClientBase.cs:line 142



2025-07-17 17:33:26.9|Warn|ProcessDownloadDecisions|Failed to download release 'John Wick Chapter 4(2023) Bluray-1080p Spanish.mkv Spanish' from Indexer Emuwarez. Release not available
2025-07-17 17:33:35.8|Info|DownloadService|Report for John Wick: Chapter 4 (2023) sent to qBittorrent from indexer Wolfmax. John Wick Chapter 4 (2023)  [BluRay MicroHD] SPANISH
2025-07-17 17:33:35.9|Info|MovieSearchService|Completed search for 1 movies. 1 reports downloaded.
2025-07-17 17:33:42.7|Info|RefreshMovieService|Updating info for Babylon
2025-07-17 17:33:43.1|Info|DiskScanService|Scanning disk for Babylon
2025-07-17 17:33:50.0|Info|DiskScanService|Completed scanning disk for Babylon
2025-07-17 17:33:50.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:33:50.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:33:50.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:33:50.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:33:50.3|Info|RefreshMovieService|Updating info for Captain America: Brave New World
2025-07-17 17:33:50.5|Info|DiskScanService|Scanning disk for Captain America: Brave New World
2025-07-17 17:34:03.0|Info|DiskScanService|Completed scanning disk for Captain America: Brave New World
2025-07-17 17:34:03.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:03.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:03.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:03.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:03.2|Info|RefreshMovieService|Updating info for How to Train Your Dragon
2025-07-17 17:34:03.3|Info|DiskScanService|Scanning disk for How to Train Your Dragon
2025-07-17 17:34:03.4|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon
2025-07-17 17:34:03.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:03.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:03.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:03.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:03.6|Info|RefreshMovieService|Updating info for How to Train Your Dragon
2025-07-17 17:34:03.7|Info|DiskScanService|Scanning disk for How to Train Your Dragon
2025-07-17 17:34:03.8|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon
2025-07-17 17:34:03.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:03.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:03.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:03.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:03.9|Info|RefreshMovieService|Updating info for How to Train Your Dragon 2
2025-07-17 17:34:04.0|Info|DiskScanService|Scanning disk for How to Train Your Dragon 2
2025-07-17 17:34:04.1|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon 2
2025-07-17 17:34:04.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:04.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:04.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:04.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:04.3|Info|RefreshMovieService|Updating info for How to Train Your Dragon: The Hidden World
2025-07-17 17:34:04.4|Info|DiskScanService|Scanning disk for How to Train Your Dragon: The Hidden World
2025-07-17 17:34:04.5|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon: The Hidden World
2025-07-17 17:34:04.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:04.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:04.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:04.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:05.6|Info|RefreshMovieService|Updating info for Final Destination
2025-07-17 17:34:05.7|Info|DiskScanService|Scanning disk for Final Destination
2025-07-17 17:34:13.9|Info|DiskScanService|Completed scanning disk for Final Destination
2025-07-17 17:34:13.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:13.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:14.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:14.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:14.1|Info|RefreshMovieService|Updating info for Final Destination 2
2025-07-17 17:34:14.3|Info|DiskScanService|Scanning disk for Final Destination 2
2025-07-17 17:34:14.4|Info|DiskScanService|Completed scanning disk for Final Destination 2
2025-07-17 17:34:14.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:18.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:18.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:18.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:18.8|Info|RefreshMovieService|Updating info for Final Destination 3
2025-07-17 17:34:19.0|Info|DiskScanService|Scanning disk for Final Destination 3
2025-07-17 17:34:19.1|Info|DiskScanService|Completed scanning disk for Final Destination 3
2025-07-17 17:34:19.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:19.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:19.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:19.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:19.3|Info|RefreshMovieService|Updating info for The Final Destination
2025-07-17 17:34:19.6|Info|DiskScanService|Scanning disk for The Final Destination
2025-07-17 17:34:19.8|Info|DiskScanService|Completed scanning disk for The Final Destination
2025-07-17 17:34:19.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:19.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:20.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:20.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:20.1|Info|RefreshMovieService|Updating info for Final Destination 5
2025-07-17 17:34:20.3|Info|DiskScanService|Scanning disk for Final Destination 5
2025-07-17 17:34:20.4|Info|DiskScanService|Completed scanning disk for Final Destination 5
2025-07-17 17:34:20.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:20.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:20.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:20.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:20.8|Info|RefreshMovieService|Updating info for Final Destination Bloodlines
2025-07-17 17:34:21.1|Info|DiskScanService|Scanning disk for Final Destination Bloodlines
2025-07-17 17:34:21.2|Info|DiskScanService|Completed scanning disk for Final Destination Bloodlines
2025-07-17 17:34:21.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:21.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:21.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:21.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:21.6|Info|RefreshMovieService|Updating info for Django Unchained
2025-07-17 17:34:21.9|Info|DiskScanService|Scanning disk for Django Unchained
2025-07-17 17:34:22.1|Info|DiskScanService|Completed scanning disk for Django Unchained
2025-07-17 17:34:22.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:22.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:22.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:22.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:22.4|Info|RefreshMovieService|Updating info for The Accountant²
2025-07-17 17:34:22.7|Info|DiskScanService|Scanning disk for The Accountant²
2025-07-17 17:34:22.8|Info|DiskScanService|Completed scanning disk for The Accountant²
2025-07-17 17:34:22.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:22.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:23.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:23.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:23.1|Info|RefreshMovieService|Updating info for The Lost World: Jurassic Park
2025-07-17 17:34:23.5|Info|DiskScanService|Scanning disk for The Lost World: Jurassic Park
2025-07-17 17:34:23.6|Info|DiskScanService|Completed scanning disk for The Lost World: Jurassic Park
2025-07-17 17:34:23.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:23.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:23.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:23.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:23.9|Info|RefreshMovieService|Updating info for The Revenant
2025-07-17 17:34:24.0|Info|DiskScanService|Scanning disk for The Revenant
2025-07-17 17:34:24.2|Info|DiskScanService|Completed scanning disk for The Revenant
2025-07-17 17:34:24.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:24.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:24.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:24.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:24.5|Info|RefreshMovieService|Updating info for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 17:34:24.8|Info|DiskScanService|Scanning disk for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 17:34:25.0|Info|DiskScanService|Completed scanning disk for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 17:34:25.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:25.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:25.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:25.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:25.3|Info|RefreshMovieService|Updating info for Despicable Me 4
2025-07-17 17:34:25.6|Info|DiskScanService|Scanning disk for Despicable Me 4
2025-07-17 17:34:25.7|Info|DiskScanService|Completed scanning disk for Despicable Me 4
2025-07-17 17:34:25.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:25.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:25.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:25.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:25.9|Info|RefreshMovieService|Updating info for John Wick: Chapter 4
2025-07-17 17:34:26.1|Info|DiskScanService|Scanning disk for John Wick: Chapter 4
2025-07-17 17:34:26.3|Info|DiskScanService|Completed scanning disk for John Wick: Chapter 4
2025-07-17 17:34:26.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:26.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:26.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:26.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:26.5|Info|RefreshMovieService|Updating info for Jurassic Park
2025-07-17 17:34:26.7|Info|DiskScanService|Scanning disk for Jurassic Park
2025-07-17 17:34:26.8|Info|DiskScanService|Completed scanning disk for Jurassic Park
2025-07-17 17:34:26.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:26.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:27.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:27.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:27.2|Info|RefreshMovieService|Updating info for Kung Fu Panda
2025-07-17 17:34:27.4|Info|DiskScanService|Scanning disk for Kung Fu Panda
2025-07-17 17:34:27.6|Info|DiskScanService|Completed scanning disk for Kung Fu Panda
2025-07-17 17:34:27.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:27.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:27.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:27.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:28.2|Info|RefreshMovieService|Updating info for Kung Fu Panda 2
2025-07-17 17:34:28.6|Info|DiskScanService|Scanning disk for Kung Fu Panda 2
2025-07-17 17:34:28.8|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 2
2025-07-17 17:34:28.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:29.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:29.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:29.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:29.2|Info|RefreshMovieService|Updating info for Kung Fu Panda 3
2025-07-17 17:34:29.5|Info|DiskScanService|Scanning disk for Kung Fu Panda 3
2025-07-17 17:34:29.9|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 3
2025-07-17 17:34:30.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:30.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:30.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:30.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:30.5|Info|RefreshMovieService|Updating info for Kung Fu Panda 4
2025-07-17 17:34:30.8|Info|DiskScanService|Scanning disk for Kung Fu Panda 4
2025-07-17 17:34:31.0|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 4
2025-07-17 17:34:31.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:31.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:31.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:31.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:31.8|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-17 17:34:32.0|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-17 17:34:32.2|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-17 17:34:32.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:32.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:32.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:32.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:32.8|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: Prince Caspian
2025-07-17 17:34:33.0|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: Prince Caspian
2025-07-17 17:34:33.2|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: Prince Caspian
2025-07-17 17:34:33.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:33.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:33.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:33.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:33.5|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 17:34:34.0|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 17:34:34.2|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 17:34:34.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:34.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:34.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:34.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:34.6|Info|RefreshMovieService|Updating info for The Hateful Eight
2025-07-17 17:34:34.9|Info|DiskScanService|Scanning disk for The Hateful Eight
2025-07-17 17:34:35.1|Info|DiskScanService|Completed scanning disk for The Hateful Eight
2025-07-17 17:34:35.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:35.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:35.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:35.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:35.5|Info|RefreshMovieService|Updating info for Sinners
2025-07-17 17:34:35.7|Info|DiskScanService|Scanning disk for Sinners
2025-07-17 17:34:36.0|Info|DiskScanService|Completed scanning disk for Sinners
2025-07-17 17:34:36.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:36.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:36.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:36.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:36.6|Info|RefreshMovieService|Updating info for Inglourious Basterds
2025-07-17 17:34:37.0|Info|DiskScanService|Scanning disk for Inglourious Basterds
2025-07-17 17:34:37.2|Info|DiskScanService|Completed scanning disk for Inglourious Basterds
2025-07-17 17:34:37.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:37.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:37.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:37.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:37.5|Info|RefreshMovieService|Updating info for Cast Away
2025-07-17 17:34:37.8|Info|DiskScanService|Scanning disk for Cast Away
2025-07-17 17:34:38.0|Info|DiskScanService|Completed scanning disk for Cast Away
2025-07-17 17:34:38.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:38.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:38.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:38.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:38.2|Info|RefreshMovieService|Updating info for Ocean's Eight
2025-07-17 17:34:38.8|Info|DiskScanService|Scanning disk for Ocean's Eight
2025-07-17 17:34:39.0|Info|DiskScanService|Completed scanning disk for Ocean's Eight
2025-07-17 17:34:39.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:39.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:39.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:39.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:39.4|Info|RefreshMovieService|Updating info for Ocean's Eleven
2025-07-17 17:34:39.9|Info|DiskScanService|Scanning disk for Ocean's Eleven
2025-07-17 17:34:40.1|Info|DiskScanService|Completed scanning disk for Ocean's Eleven
2025-07-17 17:34:40.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:40.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:40.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:40.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:40.4|Info|RefreshMovieService|Updating info for Ocean's Thirteen
2025-07-17 17:34:40.6|Info|DiskScanService|Scanning disk for Ocean's Thirteen
2025-07-17 17:34:40.8|Info|DiskScanService|Completed scanning disk for Ocean's Thirteen
2025-07-17 17:34:40.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:40.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:40.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:41.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:41.2|Info|RefreshMovieService|Updating info for Ocean's Twelve
2025-07-17 17:34:41.4|Info|DiskScanService|Scanning disk for Ocean's Twelve
2025-07-17 17:34:41.6|Info|DiskScanService|Completed scanning disk for Ocean's Twelve
2025-07-17 17:34:41.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:41.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:41.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:41.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:42.1|Info|RefreshMovieService|Updating info for Oppenheimer
2025-07-17 17:34:42.4|Info|DiskScanService|Scanning disk for Oppenheimer
2025-07-17 17:34:42.6|Info|DiskScanService|Completed scanning disk for Oppenheimer
2025-07-17 17:34:42.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:42.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:42.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:42.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:43.4|Info|RefreshMovieService|Updating info for Prometheus
2025-07-17 17:34:43.7|Info|DiskScanService|Scanning disk for Prometheus
2025-07-17 17:34:43.8|Info|DiskScanService|Completed scanning disk for Prometheus
2025-07-17 17:34:43.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:43.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:44.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:44.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:44.2|Info|RefreshMovieService|Updating info for Shutter Island
2025-07-17 17:34:44.4|Info|DiskScanService|Scanning disk for Shutter Island
2025-07-17 17:34:44.6|Info|DiskScanService|Completed scanning disk for Shutter Island
2025-07-17 17:34:44.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:44.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:44.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:44.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:45.0|Info|RefreshMovieService|Updating info for A Quiet Place: Day One
2025-07-17 17:34:45.2|Info|DiskScanService|Scanning disk for A Quiet Place: Day One
2025-07-17 17:34:45.3|Info|DiskScanService|Completed scanning disk for A Quiet Place: Day One
2025-07-17 17:34:45.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:45.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:45.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:45.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:45.7|Info|RefreshMovieService|Updating info for A Minecraft Movie
2025-07-17 17:34:46.1|Info|DiskScanService|Scanning disk for A Minecraft Movie
2025-07-17 17:34:46.3|Info|DiskScanService|Completed scanning disk for A Minecraft Movie
2025-07-17 17:34:46.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:46.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:46.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:46.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:46.9|Info|RefreshMovieService|Updating info for Until Dawn
2025-07-17 17:34:47.2|Info|DiskScanService|Scanning disk for Until Dawn
2025-07-17 17:34:47.7|Info|DiskScanService|Completed scanning disk for Until Dawn
2025-07-17 17:34:48.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:48.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:48.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:48.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:34:48.5|Info|RefreshMovieService|Updating info for Avengers: Infinity War
2025-07-17 17:34:48.7|Info|DiskScanService|Scanning disk for Avengers: Infinity War
2025-07-17 17:34:48.9|Info|DiskScanService|Completed scanning disk for Avengers: Infinity War
2025-07-17 17:34:49.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:34:49.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:34:49.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:34:49.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:40:02.9|Info|MovieService|Assigning file [John Wick - Chapter 4 (2023) Bluray-1080p.mkv] to movie [[John Wick: Chapter 4 (2023)][tt10366206, 603692]]
2025-07-17 17:47:00.9|Info|RssSyncService|Starting RSS Sync
2025-07-17 17:47:02.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 14:45:01 and 07/17/2025 15:47:02 UTC. Search may be required.
2025-07-17 17:47:02.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 14:45:01 and 07/17/2025 15:47:02 UTC. Search may be required.
2025-07-17 17:47:04.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 17:47:06.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 14:45:05 and 07/17/2025 15:47:06 UTC. Search may be required.
2025-07-17 17:47:12.4|Info|DownloadDecisionMaker|Processing 618 releases
2025-07-17 17:47:52.4|Info|RssSyncService|RSS Sync Completed. Reports found: 618, Reports grabbed: 0
2025-07-17 17:48:10.4|Info|MovieService|Deleted movie [Despicable Me 4 (2024)][tt7510222, 519182]
2025-07-17 17:48:10.4|Info|RecycleBinProvider|Attempting to send '/CONTENIDO/PELIS/Despicable Me 4 (2024)' to recycling bin
2025-07-17 17:48:10.5|Info|RecycleBinProvider|Recycling Bin has not been configured, deleting permanently. /CONTENIDO/PELIS/Despicable Me 4 (2024)
2025-07-17 17:48:21.0|Info|AddMovieService|Adding Movie [Despicable Me 4 (2024)][tt7510222, 519182] Path: [/CONTENIDO/PELIS/Despicable Me 4 (2024)]
2025-07-17 17:48:21.0|Info|RefreshMovieService|Updating info for Despicable Me 4
2025-07-17 17:48:21.1|Info|AddMovieCollectionService|Adding Collection Despicable Me Collection[86066]
2025-07-17 17:48:21.2|Info|RefreshCollectionService|Updating info for Despicable Me Collection
2025-07-17 17:48:21.4|Info|MediaCoverService|Downloading Poster for [Despicable Me 4 (2024)][tt7510222, 519182] https://image.tmdb.org/t/p/original/wWba3TaojhK7NdycRhoQpsG0FaH.jpg
2025-07-17 17:48:21.4|Info|DiskScanService|Scanning disk for Despicable Me 4
2025-07-17 17:48:21.4|Info|DiskScanService|Completed scanning disk for Despicable Me 4
2025-07-17 17:48:21.5|Info|MovieScannedHandler|[Despicable Me 4] was recently added, performing post-add actions
2025-07-17 17:48:21.5|Info|MovieSearchService|Performing search for 1 movies
2025-07-17 17:48:21.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:48:21.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:48:21.6|Info|ReleaseSearchService|Searching indexers for [Despicable Me 4]. 11 active indexers
2025-07-17 17:48:21.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:48:21.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:48:21.7|Info|MediaCoverService|Downloading Fanart for [Despicable Me 4 (2024)][tt7510222, 519182] https://image.tmdb.org/t/p/original/lgkPzcOSnTvjeMnuFzozRO5HHw1.jpg
2025-07-17 17:48:21.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024: 400.BadRequest (7349 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (1337x): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 17:48:22.0|Warn|Torznab|Jackett 1337x  HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024]
2025-07-17 17:48:22.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=search&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 17:48:22.0|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=search&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100&q=Despicable%20Me%204%202024]
2025-07-17 17:48:30.8|Info|DownloadDecisionMaker|Processing 6 releases
2025-07-17 17:48:32.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnb3dCcWlqSlhOYnp1M2JjbVQwUXJzZ2tENEVlRTBWYlpxOTR1cFEwaTN4NjZJVEtFUWZJNXBWa19RUWlxclBYZFFvR3MwLV80UFhBa1JQcFB4Sm1fT3dVSDJTSko0MnJOOWRhS2NrU0RJaGY4c2xjeG1lQTd2VksyeVZnQTM4Z3FPeWlocU5ZZlFCQV9Zc2otTk84S04wUEplQ3AzdDYzSnYyV0lJWV9pc3lQazZ3REo5MGprOWd4bUJWNG9nd2dR&file=GRU+4+MI+VILLANO+FAVORITO+(2024)+BluRay+1080p-EMUWAREZ.mkv+Spanish: 404.NotFound (0 bytes)
2025-07-17 17:48:32.7|Error|QBittorrent|Downloading torrent file for movie 'GRU 4 MI VILLANO FAVORITO (2024) BluRay 1080p-EMUWAREZ.mkv Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnb3dCcWlqSlhOYnp1M2JjbVQwUXJzZ2tENEVlRTBWYlpxOTR1cFEwaTN4NjZJVEtFUWZJNXBWa19RUWlxclBYZFFvR3MwLV80UFhBa1JQcFB4Sm1fT3dVSDJTSko0MnJOOWRhS2NrU0RJaGY4c2xjeG1lQTd2VksyeVZnQTM4Z3FPeWlocU5ZZlFCQV9Zc2otTk84S04wUEplQ3AzdDYzSnYyV0lJWV9pc3lQazZ3REo5MGprOWd4bUJWNG9nd2dR&file=GRU+4+MI+VILLANO+FAVORITO+(2024)+BluRay+1080p-EMUWAREZ.mkv+Spanish)

[v5.26.2.10099] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnb3dCcWlqSlhOYnp1M2JjbVQwUXJzZ2tENEVlRTBWYlpxOTR1cFEwaTN4NjZJVEtFUWZJNXBWa19RUWlxclBYZFFvR3MwLV80UFhBa1JQcFB4Sm1fT3dVSDJTSko0MnJOOWRhS2NrU0RJaGY4c2xjeG1lQTd2VksyeVZnQTM4Z3FPeWlocU5ZZlFCQV9Zc2otTk84S04wUEplQ3AzdDYzSnYyV0lJWV9pc3lQazZ3REo5MGprOWd4bUJWNG9nd2dR&file=GRU+4+MI+VILLANO+FAVORITO+(2024)+BluRay+1080p-EMUWAREZ.mkv+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Radarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Radarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteMovie remoteMovie, IIndexer indexer, String torrentUrl) in ./Radarr.Core/Download/TorrentClientBase.cs:line 215



2025-07-17 17:48:32.7|Warn|ProcessDownloadDecisions|Failed to download release 'GRU 4 MI VILLANO FAVORITO (2024) BluRay 1080p-EMUWAREZ.mkv Spanish' from Indexer Emuwarez. Release not available
2025-07-17 17:48:41.9|Info|DownloadService|Report for Despicable Me 4 (2024) sent to qBittorrent from indexer Wolfmax. Gru 4 Mi villano favorito (2024)  [BluRay 1080p] SPANISH
2025-07-17 17:48:42.0|Info|MovieSearchService|Completed search for 1 movies. 1 reports downloaded.
2025-07-17 17:49:08.1|Info|AddMovieService|Adding Movie [Ballerina (2025)][tt7181546, 541671] Path: [/CONTENIDO/PELIS/Ballerina (2025)]
2025-07-17 17:49:08.2|Info|RefreshMovieService|Updating info for Ballerina
2025-07-17 17:49:08.3|Info|AddMovieCollectionService|Adding Collection Ballerina Collection[1494663]
2025-07-17 17:49:08.4|Info|RefreshCollectionService|Updating info for Ballerina Collection
2025-07-17 17:49:08.7|Info|MediaCoverService|Downloading Poster for [Ballerina (2025)][tt7181546, 541671] https://image.tmdb.org/t/p/original/2VUmvqsHb6cEtdfscEA6fqqVzLg.jpg
2025-07-17 17:49:08.7|Info|DiskScanService|Scanning disk for Ballerina
2025-07-17 17:49:08.8|Info|DiskScanService|Completed scanning disk for Ballerina
2025-07-17 17:49:08.8|Info|MovieScannedHandler|[Ballerina] was recently added, performing post-add actions
2025-07-17 17:49:08.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 17:49:08.9|Info|MovieSearchService|Performing search for 1 movies
2025-07-17 17:49:08.9|Info|MediaCoverService|Downloading Fanart for [Ballerina (2025)][tt7181546, 541671] https://image.tmdb.org/t/p/original/sItIskd5xpiE64bBWYwZintkGf3.jpg
2025-07-17 17:49:08.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 17:49:09.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 17:49:09.0|Info|ReleaseSearchService|Searching indexers for [Ballerina]. 9 active indexers
2025-07-17 17:49:09.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 17:49:22.0|Info|DownloadDecisionMaker|Processing 28 releases
2025-07-17 17:49:25.5|Info|MovieSearchService|Completed search for 1 movies. 0 reports downloaded.
2025-07-17 17:49:54.3|Info|ReleaseSearchService|Searching indexers for [Ballerina]. 9 active indexers
2025-07-17 17:50:04.3|Info|DownloadDecisionMaker|Processing 28 releases
2025-07-17 18:09:03.2|Info|MovieService|Assigning file [Despicable Me 4 (2024) Bluray-1080p.mkv] to movie [[Despicable Me 4 (2024)][tt7510222, 519182]]
2025-07-17 18:18:01.4|Info|RssSyncService|Starting RSS Sync
2025-07-17 18:18:02.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 15:47:02 and 07/17/2025 15:47:02 UTC. Search may be required.
2025-07-17 18:18:02.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 15:47:02 and 07/17/2025 15:47:02 UTC. Search may be required.
2025-07-17 18:18:03.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 18:18:03.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 15:47:06 and 07/17/2025 15:47:06 UTC. Search may be required.
2025-07-17 18:18:03.8|Info|DownloadDecisionMaker|Processing 618 releases
2025-07-17 18:19:03.1|Info|RssSyncService|RSS Sync Completed. Reports found: 618, Reports grabbed: 0
2025-07-17 18:49:31.5|Info|RssSyncService|Starting RSS Sync
2025-07-17 18:49:31.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 18:49:31.8|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 18:49:31.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 15:47:02 and 07/17/2025 16:49:31 UTC. Search may be required.
2025-07-17 18:49:32.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 15:47:02 and 07/17/2025 16:49:32 UTC. Search may be required.
2025-07-17 18:49:34.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 18:49:36.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 15:47:06 and 07/17/2025 16:49:36 UTC. Search may be required.
2025-07-17 18:49:43.1|Info|DownloadDecisionMaker|Processing 698 releases
2025-07-17 18:50:18.4|Info|RssSyncService|RSS Sync Completed. Reports found: 698, Reports grabbed: 0
2025-07-17 19:20:31.7|Info|RssSyncService|Starting RSS Sync
2025-07-17 19:20:32.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 16:49:31 and 07/17/2025 16:49:31 UTC. Search may be required.
2025-07-17 19:20:32.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 16:49:32 and 07/17/2025 16:49:32 UTC. Search may be required.
2025-07-17 19:20:33.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 19:20:34.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 16:49:36 and 07/17/2025 16:49:36 UTC. Search may be required.
2025-07-17 19:20:34.0|Info|DownloadDecisionMaker|Processing 698 releases
2025-07-17 19:21:36.3|Info|RssSyncService|RSS Sync Completed. Reports found: 698, Reports grabbed: 0
2025-07-17 19:52:01.8|Info|RssSyncService|Starting RSS Sync
2025-07-17 19:52:02.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 16:49:31 and 07/17/2025 17:52:02 UTC. Search may be required.
2025-07-17 19:52:02.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 16:49:32 and 07/17/2025 17:52:02 UTC. Search may be required.
2025-07-17 19:52:04.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 19:52:06.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 16:49:36 and 07/17/2025 17:52:06 UTC. Search may be required.
2025-07-17 19:52:11.5|Info|DownloadDecisionMaker|Processing 713 releases
2025-07-17 19:52:46.1|Info|RssSyncService|RSS Sync Completed. Reports found: 713, Reports grabbed: 0
2025-07-17 20:23:01.8|Info|RssSyncService|Starting RSS Sync
2025-07-17 20:23:02.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 17:52:02 and 07/17/2025 17:52:02 UTC. Search may be required.
2025-07-17 20:23:02.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 17:52:02 and 07/17/2025 17:52:02 UTC. Search may be required.
2025-07-17 20:23:03.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 20:23:04.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 17:52:06 and 07/17/2025 17:52:06 UTC. Search may be required.
2025-07-17 20:23:04.0|Info|DownloadDecisionMaker|Processing 713 releases
2025-07-17 20:23:37.7|Info|RssSyncService|RSS Sync Completed. Reports found: 713, Reports grabbed: 0
2025-07-17 20:54:01.9|Info|RssSyncService|Starting RSS Sync
2025-07-17 20:54:02.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 20:54:02.6|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 20:54:02.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 17:52:02 and 07/17/2025 18:54:02 UTC. Search may be required.
2025-07-17 20:54:03.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 17:52:02 and 07/17/2025 18:54:03 UTC. Search may be required.
2025-07-17 20:54:06.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 17:52:06 and 07/17/2025 18:54:06 UTC. Search may be required.
2025-07-17 20:54:07.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 20:54:13.5|Info|DownloadDecisionMaker|Processing 698 releases
2025-07-17 20:54:49.8|Info|RssSyncService|RSS Sync Completed. Reports found: 698, Reports grabbed: 0
2025-07-17 21:16:30.2|Info|AddMovieService|Adding Movie [Die Hard (1988)][tt0095016, 562] Path: [/CONTENIDO/PELIS/Die Hard (1988)]
2025-07-17 21:16:30.3|Info|RefreshMovieService|Updating info for Die Hard
2025-07-17 21:16:30.4|Info|AddMovieCollectionService|Adding Collection Die Hard Collection[1570]
2025-07-17 21:16:30.4|Info|RefreshCollectionService|Updating info for Die Hard Collection
2025-07-17 21:16:30.5|Info|MediaCoverService|Downloading Poster for [Die Hard (1988)][tt0095016, 562] https://image.tmdb.org/t/p/original/aJCpHDC6RoGz7d1Fzayl019xnxX.jpg
2025-07-17 21:16:30.6|Info|DiskScanService|Scanning disk for Die Hard
2025-07-17 21:16:30.7|Info|DiskScanService|Completed scanning disk for Die Hard
2025-07-17 21:16:30.7|Info|MovieScannedHandler|[Die Hard] was recently added, performing post-add actions
2025-07-17 21:16:30.8|Info|MovieSearchService|Performing search for 1 movies
2025-07-17 21:16:30.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:16:30.8|Info|MediaCoverService|Downloading Fanart for [Die Hard (1988)][tt0095016, 562] https://image.tmdb.org/t/p/original/oIwfoUFfWfESn0Y8u8jv9lc8li1.jpg
2025-07-17 21:16:30.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:16:31.0|Info|ReleaseSearchService|Searching indexers for [Die Hard]. 10 active indexers
2025-07-17 21:16:31.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:16:31.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:16:31.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=Die%20Hard%201988: 400.BadRequest (7349 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (1337x): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 21:16:31.4|Warn|Torznab|Jackett 1337x  HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=Die%20Hard%201988]
2025-07-17 21:16:33.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/bitsearch/results/torznab/api?t=search&cat=2000,8000,8010,5000&extended=1&apikey=(removed)&offset=0&limit=100&q=Jungla%20de%20cristal%201988: 400.BadRequest (1763 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (bitsearch): Request to https://bitsearch.to/search?q=Jungla%20de%20cristal%201988&amp;sort=date&amp;order=desc&amp;limit=100 failed (Error BadGateway) - The tracker seems to be down.&#xA; ---&gt; System.Exception: Request to https://bitsearch.to/search?q=Jungla%20de%20cristal%201988&amp;sort=date&amp;order=desc&amp;limit=100 failed (Error BadGateway) - The tracker seems to be down.&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.CheckSiteDown(WebResult response) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 684&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 618&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 21:16:33.2|Warn|Torznab|BitSearch HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/bitsearch/results/torznab/api?t=search&cat=2000,8000,8010,5000&extended=1&apikey=(removed)&offset=0&limit=100&q=Jungla%20de%20cristal%201988]
2025-07-17 21:16:41.4|Info|DownloadDecisionMaker|Processing 64 releases
2025-07-17 21:16:47.2|Info|DownloadService|Report for Die Hard (1988) sent to qBittorrent from indexer MejorTorrent. Jungla de cristal 1988 SPANISH BDremux 1080p
2025-07-17 21:16:47.3|Info|MovieSearchService|Completed search for 1 movies. 1 reports downloaded.
2025-07-17 21:25:02.0|Info|RssSyncService|Starting RSS Sync
2025-07-17 21:25:02.5|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 18:54:02 and 07/17/2025 18:54:02 UTC. Search may be required.
2025-07-17 21:25:02.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 18:54:03 and 07/17/2025 18:54:03 UTC. Search may be required.
2025-07-17 21:25:04.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 21:25:04.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 18:54:06 and 07/17/2025 18:54:06 UTC. Search may be required.
2025-07-17 21:25:04.3|Info|DownloadDecisionMaker|Processing 713 releases
2025-07-17 21:37:11.1|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-07-17 21:37:11.7|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-17 21:37:11.9|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-17 21:37:14.4|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-17 21:37:15.7|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-17 21:37:16.3|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-17 21:37:16.3|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-17 21:37:16.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-17 21:37:17.0|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-17 21:37:17.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.7116117s
2025-07-17 21:37:17.1|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-17 21:37:17.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.7393808s
2025-07-17 21:37:17.3|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-17 21:37:17.4|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-17 21:37:17.4|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-17 21:37:17.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-17 21:37:17.4|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-17 21:37:17.4|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0404832s
2025-07-17 21:37:17.4|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-17 21:37:17.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0578019s
2025-07-17 21:37:18.7|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-07-17 21:37:20.2|Info|CommandExecutor|Starting 2 threads for tasks.
2025-07-17 21:37:21.0|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-07-17 21:37:21.0|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-07-17 21:37:21.1|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-07-17 21:37:22.4|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-07-17 21:37:50.7|Info|RssSyncService|Starting RSS Sync
2025-07-17 21:37:50.8|Info|RecycleBinProvider|Recycle Bin has not been configured, cannot cleanup.
2025-07-17 21:37:50.9|Info|HousekeepingService|Running housecleaning tasks
2025-07-17 21:37:52.0|Info|Database|Vacuuming Log database
2025-07-17 21:37:52.5|Info|Database|Log database compressed
2025-07-17 21:37:52.6|Info|Database|Vacuuming Main database
2025-07-17 21:37:53.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 18:54:03 and 07/17/2025 19:37:53 UTC. Search may be required.
2025-07-17 21:37:53.9|Info|Database|Main database compressed
2025-07-17 21:37:55.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3976 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Network unreachable&#xA; ---&gt; System.Net.Http.HttpRequestException: Network unreachable (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (101): Network unreachable&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 21:37:55.4|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 21:37:55.4|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 18:54:02 and 07/17/2025 19:37:55 UTC. Search may be required.
2025-07-17 21:37:59.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 21:38:01.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 18:54:06 and 07/17/2025 19:38:01 UTC. Search may be required.
2025-07-17 21:38:05.7|Info|DownloadDecisionMaker|Processing 698 releases
2025-07-17 21:38:37.7|Info|RssSyncService|RSS Sync Completed. Reports found: 698, Reports grabbed: 0
2025-07-17 21:38:51.1|Info|DiskScanService|Scanning disk for A Quiet Place: Day One
2025-07-17 21:38:51.5|Info|DiskScanService|Completed scanning disk for A Quiet Place: Day One
2025-07-17 21:38:51.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:51.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:51.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:51.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:51.6|Info|RefreshMovieService|Updating info for Avengers: Infinity War
2025-07-17 21:38:51.8|Info|DiskScanService|Scanning disk for Avengers: Infinity War
2025-07-17 21:38:51.9|Info|DiskScanService|Completed scanning disk for Avengers: Infinity War
2025-07-17 21:38:51.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:51.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:52.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:52.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:52.0|Info|DiskScanService|Scanning disk for Babylon
2025-07-17 21:38:52.1|Info|DiskScanService|Completed scanning disk for Babylon
2025-07-17 21:38:52.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:52.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:52.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:52.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:52.2|Info|DiskScanService|Scanning disk for Captain America: Brave New World
2025-07-17 21:38:52.5|Info|DiskScanService|Completed scanning disk for Captain America: Brave New World
2025-07-17 21:38:52.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:52.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:52.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:52.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:52.6|Info|RefreshMovieService|Updating info for The Final Destination
2025-07-17 21:38:52.8|Info|DiskScanService|Scanning disk for The Final Destination
2025-07-17 21:38:52.9|Info|DiskScanService|Completed scanning disk for The Final Destination
2025-07-17 21:38:52.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:52.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:52.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:52.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:53.0|Info|DiskScanService|Scanning disk for Django Unchained
2025-07-17 21:38:53.0|Info|DiskScanService|Completed scanning disk for Django Unchained
2025-07-17 21:38:53.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:53.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:53.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:53.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:53.1|Info|RefreshMovieService|Updating info for Final Destination
2025-07-17 21:38:53.3|Info|DiskScanService|Scanning disk for Final Destination
2025-07-17 21:38:53.4|Info|DiskScanService|Completed scanning disk for Final Destination
2025-07-17 21:38:53.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:53.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:53.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:53.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:53.5|Info|RefreshMovieService|Updating info for Final Destination 2
2025-07-17 21:38:53.7|Info|DiskScanService|Scanning disk for Final Destination 2
2025-07-17 21:38:53.7|Info|DiskScanService|Completed scanning disk for Final Destination 2
2025-07-17 21:38:53.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:53.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:53.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:53.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:53.8|Info|RefreshMovieService|Updating info for Final Destination 3
2025-07-17 21:38:54.0|Info|DiskScanService|Scanning disk for Final Destination 3
2025-07-17 21:38:54.0|Info|DiskScanService|Completed scanning disk for Final Destination 3
2025-07-17 21:38:54.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:54.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:54.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:54.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:54.2|Info|DiskScanService|Scanning disk for Final Destination 5
2025-07-17 21:38:54.2|Info|DiskScanService|Completed scanning disk for Final Destination 5
2025-07-17 21:38:54.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:54.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:54.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:54.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:54.4|Info|RefreshMovieService|Updating info for Final Destination Bloodlines
2025-07-17 21:38:54.5|Info|DiskScanService|Scanning disk for Final Destination Bloodlines
2025-07-17 21:38:54.5|Info|DiskScanService|Completed scanning disk for Final Destination Bloodlines
2025-07-17 21:38:54.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:54.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:54.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:54.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:54.7|Info|RefreshMovieService|Updating info for Inglourious Basterds
2025-07-17 21:38:54.8|Info|DiskScanService|Scanning disk for Inglourious Basterds
2025-07-17 21:38:54.9|Info|DiskScanService|Completed scanning disk for Inglourious Basterds
2025-07-17 21:38:54.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:54.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:55.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:55.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:55.1|Info|DiskScanService|Scanning disk for Jurassic Park
2025-07-17 21:38:55.2|Info|DiskScanService|Completed scanning disk for Jurassic Park
2025-07-17 21:38:55.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:55.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:55.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:55.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:55.5|Info|DiskScanService|Scanning disk for Ocean's Eight
2025-07-17 21:38:55.7|Info|DiskScanService|Completed scanning disk for Ocean's Eight
2025-07-17 21:38:55.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:55.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:55.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:55.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:55.8|Info|DiskScanService|Scanning disk for Ocean's Eleven
2025-07-17 21:38:55.9|Info|DiskScanService|Completed scanning disk for Ocean's Eleven
2025-07-17 21:38:55.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:56.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:56.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:56.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:56.0|Info|DiskScanService|Scanning disk for Ocean's Thirteen
2025-07-17 21:38:56.1|Info|DiskScanService|Completed scanning disk for Ocean's Thirteen
2025-07-17 21:38:56.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:56.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:56.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:56.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:56.2|Info|DiskScanService|Scanning disk for Ocean's Twelve
2025-07-17 21:38:57.3|Info|DiskScanService|Completed scanning disk for Ocean's Twelve
2025-07-17 21:38:57.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:57.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:57.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:57.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:57.4|Info|RefreshMovieService|Updating info for Oppenheimer
2025-07-17 21:38:57.5|Info|DiskScanService|Scanning disk for Oppenheimer
2025-07-17 21:38:57.6|Info|DiskScanService|Completed scanning disk for Oppenheimer
2025-07-17 21:38:57.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:57.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:57.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:57.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:57.8|Info|RefreshMovieService|Updating info for Prometheus
2025-07-17 21:38:57.9|Info|DiskScanService|Scanning disk for Prometheus
2025-07-17 21:38:58.1|Info|DiskScanService|Completed scanning disk for Prometheus
2025-07-17 21:38:58.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:58.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:58.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:58.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:58.2|Info|RefreshMovieService|Updating info for Shutter Island
2025-07-17 21:38:58.3|Info|DiskScanService|Scanning disk for Shutter Island
2025-07-17 21:38:58.4|Info|DiskScanService|Completed scanning disk for Shutter Island
2025-07-17 21:38:58.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:58.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:58.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:58.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:58.5|Info|DiskScanService|Scanning disk for The Accountant²
2025-07-17 21:38:58.6|Info|DiskScanService|Completed scanning disk for The Accountant²
2025-07-17 21:38:58.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:58.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:58.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:58.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:58.7|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: Prince Caspian
2025-07-17 21:38:58.8|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: Prince Caspian
2025-07-17 21:38:58.9|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: Prince Caspian
2025-07-17 21:38:58.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:58.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:58.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:59.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:59.0|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-17 21:38:59.1|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: The Lion, the Witch and the Wardrobe
2025-07-17 21:38:59.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:59.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:59.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:59.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:59.2|Info|RefreshMovieService|Updating info for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 21:38:59.4|Info|DiskScanService|Scanning disk for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 21:38:59.4|Info|DiskScanService|Completed scanning disk for The Chronicles of Narnia: The Voyage of the Dawn Treader
2025-07-17 21:38:59.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:38:59.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:38:59.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:38:59.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:38:59.6|Info|RefreshMovieService|Updating info for The Hateful Eight
2025-07-17 21:38:59.7|Info|DiskScanService|Scanning disk for The Hateful Eight
2025-07-17 21:38:59.9|Info|DiskScanService|Completed scanning disk for The Hateful Eight
2025-07-17 21:38:59.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:00.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:00.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:00.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:00.1|Info|RefreshMovieService|Updating info for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 21:39:00.3|Info|DiskScanService|Scanning disk for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 21:39:00.3|Info|DiskScanService|Completed scanning disk for The Lord of the Rings: The Fellowship of the Ring
2025-07-17 21:39:00.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:00.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:00.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:00.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:00.5|Info|RefreshMovieService|Updating info for The Revenant
2025-07-17 21:39:00.6|Info|DiskScanService|Scanning disk for The Revenant
2025-07-17 21:39:00.6|Info|DiskScanService|Completed scanning disk for The Revenant
2025-07-17 21:39:00.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:00.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:00.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:00.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:00.7|Info|RefreshMovieService|Updating info for Until Dawn
2025-07-17 21:39:00.9|Info|DiskScanService|Scanning disk for Until Dawn
2025-07-17 21:39:01.5|Info|DiskScanService|Completed scanning disk for Until Dawn
2025-07-17 21:39:01.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:01.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:01.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:01.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:01.6|Info|RefreshMovieService|Updating info for Sinners
2025-07-17 21:39:01.7|Info|DiskScanService|Scanning disk for Sinners
2025-07-17 21:39:01.7|Info|DiskScanService|Completed scanning disk for Sinners
2025-07-17 21:39:01.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:01.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:01.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:01.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:01.8|Info|RefreshMovieService|Updating info for The Lost World: Jurassic Park
2025-07-17 21:39:02.0|Info|DiskScanService|Scanning disk for The Lost World: Jurassic Park
2025-07-17 21:39:02.0|Info|DiskScanService|Completed scanning disk for The Lost World: Jurassic Park
2025-07-17 21:39:02.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:02.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:02.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:02.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:02.2|Info|RefreshMovieService|Updating info for How to Train Your Dragon 2
2025-07-17 21:39:02.3|Info|DiskScanService|Scanning disk for How to Train Your Dragon 2
2025-07-17 21:39:02.3|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon 2
2025-07-17 21:39:02.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:02.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:02.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:02.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:02.4|Info|RefreshMovieService|Updating info for How to Train Your Dragon
2025-07-17 21:39:02.5|Info|DiskScanService|Scanning disk for How to Train Your Dragon
2025-07-17 21:39:02.6|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon
2025-07-17 21:39:02.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:02.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:02.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:02.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:02.7|Info|RefreshMovieService|Updating info for How to Train Your Dragon: The Hidden World
2025-07-17 21:39:02.8|Info|DiskScanService|Scanning disk for How to Train Your Dragon: The Hidden World
2025-07-17 21:39:02.9|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon: The Hidden World
2025-07-17 21:39:02.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:02.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:02.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:02.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:03.0|Info|RefreshMovieService|Updating info for Kung Fu Panda
2025-07-17 21:39:03.1|Info|DiskScanService|Scanning disk for Kung Fu Panda
2025-07-17 21:39:03.1|Info|DiskScanService|Completed scanning disk for Kung Fu Panda
2025-07-17 21:39:03.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:03.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:03.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:03.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:03.3|Info|RefreshMovieService|Updating info for Kung Fu Panda 2
2025-07-17 21:39:03.5|Info|DiskScanService|Scanning disk for Kung Fu Panda 2
2025-07-17 21:39:03.5|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 2
2025-07-17 21:39:03.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:03.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:03.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:03.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:03.6|Info|RefreshMovieService|Updating info for Kung Fu Panda 3
2025-07-17 21:39:03.7|Info|DiskScanService|Scanning disk for Kung Fu Panda 3
2025-07-17 21:39:03.8|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 3
2025-07-17 21:39:03.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:03.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:03.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:03.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:03.9|Info|RefreshMovieService|Updating info for Kung Fu Panda 4
2025-07-17 21:39:04.0|Info|DiskScanService|Scanning disk for Kung Fu Panda 4
2025-07-17 21:39:04.0|Info|DiskScanService|Completed scanning disk for Kung Fu Panda 4
2025-07-17 21:39:04.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:04.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:04.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:04.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:04.1|Info|DiskScanService|Scanning disk for A Minecraft Movie
2025-07-17 21:39:04.2|Info|DiskScanService|Completed scanning disk for A Minecraft Movie
2025-07-17 21:39:04.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:04.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:04.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:04.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:04.3|Info|DiskScanService|Scanning disk for Cast Away
2025-07-17 21:39:04.4|Info|DiskScanService|Completed scanning disk for Cast Away
2025-07-17 21:39:04.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:04.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:04.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:04.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:04.5|Info|RefreshMovieService|Updating info for How to Train Your Dragon
2025-07-17 21:39:04.6|Info|DiskScanService|Scanning disk for How to Train Your Dragon
2025-07-17 21:39:04.6|Info|DiskScanService|Completed scanning disk for How to Train Your Dragon
2025-07-17 21:39:04.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:04.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:04.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:04.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:04.8|Info|RefreshMovieService|Updating info for John Wick: Chapter 4
2025-07-17 21:39:04.9|Info|DiskScanService|Scanning disk for John Wick: Chapter 4
2025-07-17 21:39:05.0|Info|DiskScanService|Completed scanning disk for John Wick: Chapter 4
2025-07-17 21:39:05.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:05.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:05.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:05.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:05.1|Info|DiskScanService|Scanning disk for Despicable Me 4
2025-07-17 21:39:06.5|Info|DiskScanService|Completed scanning disk for Despicable Me 4
2025-07-17 21:39:06.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:06.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:06.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:06.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:06.6|Info|RefreshMovieService|Updating info for Ballerina
2025-07-17 21:39:06.7|Info|DiskScanService|Scanning disk for Ballerina
2025-07-17 21:39:06.8|Info|DiskScanService|Completed scanning disk for Ballerina
2025-07-17 21:39:06.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:06.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:06.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:06.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 21:39:06.9|Info|RefreshMovieService|Updating info for Die Hard
2025-07-17 21:39:07.0|Info|DiskScanService|Scanning disk for Die Hard
2025-07-17 21:39:07.1|Info|DiskScanService|Completed scanning disk for Die Hard
2025-07-17 21:39:07.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-17 21:39:07.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-17 21:39:07.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-17 21:39:07.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-17 22:08:51.1|Info|RssSyncService|Starting RSS Sync
2025-07-17 22:08:52.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 19:37:53 and 07/17/2025 19:37:53 UTC. Search may be required.
2025-07-17 22:08:53.2|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 19:37:55 and 07/17/2025 19:37:55 UTC. Search may be required.
2025-07-17 22:08:53.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 22:08:53.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 19:38:01 and 07/17/2025 19:38:01 UTC. Search may be required.
2025-07-17 22:08:53.9|Info|DownloadDecisionMaker|Processing 713 releases
2025-07-17 22:09:49.3|Info|RssSyncService|RSS Sync Completed. Reports found: 713, Reports grabbed: 0
2025-07-17 22:39:51.4|Info|RssSyncService|Starting RSS Sync
2025-07-17 22:39:52.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 19:37:55 and 07/17/2025 20:39:51 UTC. Search may be required.
2025-07-17 22:39:52.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 19:37:53 and 07/17/2025 20:39:52 UTC. Search may be required.
2025-07-17 22:39:54.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 22:39:56.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 19:38:01 and 07/17/2025 20:39:56 UTC. Search may be required.
2025-07-17 22:40:02.3|Info|DownloadDecisionMaker|Processing 713 releases
2025-07-17 22:40:31.2|Info|RssSyncService|RSS Sync Completed. Reports found: 713, Reports grabbed: 0
2025-07-17 23:10:51.4|Info|RssSyncService|Starting RSS Sync
2025-07-17 23:10:51.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 20:39:52 and 07/17/2025 20:39:52 UTC. Search may be required.
2025-07-17 23:10:51.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 23:10:51.8|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 23:10:53.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 23:10:53.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 20:39:56 and 07/17/2025 20:39:56 UTC. Search may be required.
2025-07-17 23:10:53.6|Info|DownloadDecisionMaker|Processing 698 releases
2025-07-17 23:11:22.6|Info|RssSyncService|RSS Sync Completed. Reports found: 698, Reports grabbed: 0
2025-07-17 23:41:51.5|Info|RssSyncService|Starting RSS Sync
2025-07-17 23:41:51.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 20:39:52 and 07/17/2025 21:41:51 UTC. Search may be required.
2025-07-17 23:41:52.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 20:39:52 and 07/17/2025 21:41:52 UTC. Search may be required.
2025-07-17 23:41:54.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-17 23:41:55.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 20:39:56 and 07/17/2025 21:41:55 UTC. Search may be required.
2025-07-17 23:42:03.9|Info|DownloadDecisionMaker|Processing 713 releases
2025-07-17 23:42:33.4|Info|RssSyncService|RSS Sync Completed. Reports found: 713, Reports grabbed: 0
2025-07-18 00:12:51.6|Info|RssSyncService|Starting RSS Sync
2025-07-18 00:12:51.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 21:41:52 and 07/17/2025 21:41:52 UTC. Search may be required.
2025-07-18 00:12:52.0|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 21:41:51 and 07/17/2025 21:41:51 UTC. Search may be required.
2025-07-18 00:12:53.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 00:12:53.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 21:41:55 and 07/17/2025 21:41:55 UTC. Search may be required.
2025-07-18 00:12:53.9|Info|DownloadDecisionMaker|Processing 713 releases
2025-07-18 00:13:52.1|Info|RssSyncService|RSS Sync Completed. Reports found: 713, Reports grabbed: 0
2025-07-18 00:44:21.7|Info|RssSyncService|Starting RSS Sync
2025-07-18 00:44:22.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 00:44:22.6|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 00:44:22.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 21:41:51 and 07/17/2025 22:44:22 UTC. Search may be required.
2025-07-18 00:44:23.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 21:41:52 and 07/17/2025 22:44:23 UTC. Search may be required.
2025-07-18 00:44:25.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 00:44:27.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 21:41:55 and 07/17/2025 22:44:27 UTC. Search may be required.
2025-07-18 00:44:31.1|Info|DownloadDecisionMaker|Processing 698 releases
2025-07-18 00:45:24.0|Info|RssSyncService|RSS Sync Completed. Reports found: 698, Reports grabbed: 0
2025-07-18 00:48:52.9|Info|ReleaseSearchService|Searching indexers for [How to Train Your Dragon]. 11 active indexers
2025-07-18 00:48:53.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=search&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100&q=How%20to%20Train%20Your%20Dragon%202025: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 00:48:53.2|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=search&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100&q=How%20to%20Train%20Your%20Dragon%202025]
2025-07-18 00:48:53.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=How%20to%20Train%20Your%20Dragon%202025: 400.BadRequest (7349 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (1337x): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 00:48:53.5|Warn|Torznab|Jackett 1337x  HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=How%20to%20Train%20Your%20Dragon%202025]
2025-07-18 00:49:01.7|Info|DownloadDecisionMaker|Processing 35 releases
2025-07-18 00:50:02.3|Info|MovieService|Deleted movie [Ballerina (2025)][tt7181546, 541671]
2025-07-18 00:50:06.0|Info|ReleaseSearchService|Searching indexers for [Die Hard]. 10 active indexers
2025-07-18 00:50:06.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=Die%20Hard%201988: 400.BadRequest (7349 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (1337x): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Connection refused (localhost:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (111): Connection refused&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 00:50:06.3|Warn|Torznab|Jackett 1337x  HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=search&cat=2000,2010,2030,2040,2045,2060,2070,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,5000,5030,5040,5070,5080&extended=1&apikey=(removed)&offset=0&limit=100&q=Die%20Hard%201988]
2025-07-18 00:51:08.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/bitsearch/results/torznab/api?t=search&cat=2000,8000,8010,5000&extended=1&apikey=(removed)&offset=0&limit=100&q=Jungla%20de%20cristal%201988: 400.BadRequest (1771 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (bitsearch): Request to https://bitsearch.to/search?q=Jungla%20de%20cristal%201988&amp;sort=date&amp;order=desc&amp;limit=100 failed (Error GatewayTimeout) - The tracker seems to be down.&#xA; ---&gt; System.Exception: Request to https://bitsearch.to/search?q=Jungla%20de%20cristal%201988&amp;sort=date&amp;order=desc&amp;limit=100 failed (Error GatewayTimeout) - The tracker seems to be down.&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.CheckSiteDown(WebResult response) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 684&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 618&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 00:51:08.2|Warn|Torznab|BitSearch HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/bitsearch/results/torznab/api?t=search&cat=2000,8000,8010,5000&extended=1&apikey=(removed)&offset=0&limit=100&q=Jungla%20de%20cristal%201988]
2025-07-18 00:51:08.3|Info|DownloadDecisionMaker|Processing 64 releases
2025-07-18 00:51:38.3|Info|ReleaseSearchService|Searching indexers for [Die Hard]. 8 active indexers
2025-07-18 00:51:45.2|Info|ReleaseSearchService|Searching indexers for [Die Hard]. 8 active indexers
2025-07-18 00:51:51.9|Info|DownloadDecisionMaker|Processing 64 releases
2025-07-18 00:52:01.2|Info|DownloadDecisionMaker|Processing 64 releases
2025-07-18 00:52:47.8|Info|DownloadService|Report for Die Hard (1988) sent to qBittorrent from indexer MejorTorrent. Jungla de cristal 1988 SPANISH BDremux 1080p
2025-07-18 00:53:17.1|Info|DownloadService|Report for Die Hard (1988) sent to qBittorrent from indexer Wolfmax. La jungla de cristal [BDremux 1080p][DTS 5.1 Castellano-DTS 5.1 Ingles+Subs][ES-EN] [BDremux] SPANISH
2025-07-18 01:15:51.9|Info|RssSyncService|Starting RSS Sync
2025-07-18 01:15:52.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 01:15:52.1|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 01:15:52.2|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 22:44:22 and 07/17/2025 23:15:52 UTC. Search may be required.
2025-07-18 01:15:52.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 22:44:23 and 07/17/2025 23:15:52 UTC. Search may be required.
2025-07-18 01:15:54.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 01:15:56.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 22:44:27 and 07/17/2025 23:15:56 UTC. Search may be required.
2025-07-18 01:16:00.9|Info|DownloadDecisionMaker|Processing 698 releases
2025-07-18 01:16:30.6|Info|RssSyncService|RSS Sync Completed. Reports found: 698, Reports grabbed: 0
2025-07-18 01:46:52.0|Info|RssSyncService|Starting RSS Sync
2025-07-18 01:46:52.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 23:15:52 and 07/17/2025 23:15:52 UTC. Search may be required.
2025-07-18 01:46:52.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 23:15:52 and 07/17/2025 23:15:52 UTC. Search may be required.
2025-07-18 01:46:54.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 01:46:54.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 23:15:56 and 07/17/2025 23:15:56 UTC. Search may be required.
2025-07-18 01:46:54.2|Info|DownloadDecisionMaker|Processing 713 releases
2025-07-18 01:47:30.5|Info|RssSyncService|RSS Sync Completed. Reports found: 713, Reports grabbed: 0
2025-07-18 02:17:52.0|Info|RssSyncService|Starting RSS Sync
2025-07-18 02:17:52.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/17/2025 23:15:52 and 07/18/2025 00:17:52 UTC. Search may be required.
2025-07-18 02:17:53.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 23:15:52 and 07/18/2025 00:17:53 UTC. Search may be required.
2025-07-18 02:17:55.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 02:17:57.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 23:15:56 and 07/18/2025 00:17:57 UTC. Search may be required.
2025-07-18 02:17:59.2|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 02:18:31.0|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 02:48:52.1|Info|RssSyncService|Starting RSS Sync
2025-07-18 02:48:52.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 00:17:53 and 07/18/2025 00:17:53 UTC. Search may be required.
2025-07-18 02:48:52.4|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 00:17:52 and 07/18/2025 00:17:52 UTC. Search may be required.
2025-07-18 02:48:54.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 02:48:54.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 00:17:57 and 07/18/2025 00:17:57 UTC. Search may be required.
2025-07-18 02:48:54.4|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 02:49:28.9|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 03:19:52.2|Info|RssSyncService|Starting RSS Sync
2025-07-18 03:19:52.6|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 00:17:52 and 07/18/2025 01:19:52 UTC. Search may be required.
2025-07-18 03:19:52.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 00:17:53 and 07/18/2025 01:19:52 UTC. Search may be required.
2025-07-18 03:19:54.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 03:19:56.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 00:17:57 and 07/18/2025 01:19:56 UTC. Search may be required.
2025-07-18 03:19:58.8|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 03:20:32.8|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 03:50:52.5|Info|RssSyncService|Starting RSS Sync
2025-07-18 03:50:52.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 01:19:52 and 07/18/2025 01:19:52 UTC. Search may be required.
2025-07-18 03:50:52.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 03:50:52.8|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 03:50:52.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 01:19:52 and 07/18/2025 01:19:52 UTC. Search may be required.
2025-07-18 03:50:54.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 03:50:54.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 01:19:56 and 07/18/2025 01:19:56 UTC. Search may be required.
2025-07-18 03:50:54.7|Info|DownloadDecisionMaker|Processing 695 releases
2025-07-18 03:51:24.9|Info|RssSyncService|RSS Sync Completed. Reports found: 695, Reports grabbed: 0
2025-07-18 04:21:52.6|Info|RssSyncService|Starting RSS Sync
2025-07-18 04:21:52.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 04:21:52.8|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 04:21:52.9|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 01:19:52 and 07/18/2025 02:21:52 UTC. Search may be required.
2025-07-18 04:21:53.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 01:19:52 and 07/18/2025 02:21:53 UTC. Search may be required.
2025-07-18 04:21:55.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 04:21:57.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 01:19:56 and 07/18/2025 02:21:57 UTC. Search may be required.
2025-07-18 04:21:59.4|Info|DownloadDecisionMaker|Processing 695 releases
2025-07-18 04:22:28.9|Info|RssSyncService|RSS Sync Completed. Reports found: 695, Reports grabbed: 0
2025-07-18 04:52:52.7|Info|RssSyncService|Starting RSS Sync
2025-07-18 04:52:52.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 02:21:53 and 07/18/2025 02:21:53 UTC. Search may be required.
2025-07-18 04:52:53.2|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 02:21:52 and 07/18/2025 02:21:52 UTC. Search may be required.
2025-07-18 04:52:54.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 04:52:54.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 02:21:57 and 07/18/2025 02:21:57 UTC. Search may be required.
2025-07-18 04:52:54.9|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 04:53:35.0|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 05:23:53.0|Info|RssSyncService|Starting RSS Sync
2025-07-18 05:23:53.3|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 05:23:53.4|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 05:23:53.6|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 02:21:52 and 07/18/2025 03:23:53 UTC. Search may be required.
2025-07-18 05:23:53.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 02:21:53 and 07/18/2025 03:23:53 UTC. Search may be required.
2025-07-18 05:23:56.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 05:23:57.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 02:21:57 and 07/18/2025 03:23:57 UTC. Search may be required.
2025-07-18 05:23:59.5|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 05:24:30.9|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 05:54:53.2|Info|RssSyncService|Starting RSS Sync
2025-07-18 05:54:53.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 03:23:53 and 07/18/2025 03:23:53 UTC. Search may be required.
2025-07-18 05:54:53.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 05:54:53.6|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 05:54:53.6|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 03:23:53 and 07/18/2025 03:23:53 UTC. Search may be required.
2025-07-18 05:54:55.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 05:54:55.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 03:23:57 and 07/18/2025 03:23:57 UTC. Search may be required.
2025-07-18 05:54:55.5|Info|DownloadDecisionMaker|Processing 695 releases
2025-07-18 05:55:24.1|Info|RssSyncService|RSS Sync Completed. Reports found: 695, Reports grabbed: 0
2025-07-18 06:25:53.3|Info|RssSyncService|Starting RSS Sync
2025-07-18 06:25:54.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 03:23:53 and 07/18/2025 04:25:54 UTC. Search may be required.
2025-07-18 06:25:54.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 03:23:53 and 07/18/2025 04:25:54 UTC. Search may be required.
2025-07-18 06:25:57.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 06:25:58.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 03:23:57 and 07/18/2025 04:25:58 UTC. Search may be required.
2025-07-18 06:26:02.7|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 06:26:35.4|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 06:56:53.5|Info|RssSyncService|Starting RSS Sync
2025-07-18 06:56:53.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 04:25:54 and 07/18/2025 04:25:54 UTC. Search may be required.
2025-07-18 06:56:53.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 04:25:54 and 07/18/2025 04:25:54 UTC. Search may be required.
2025-07-18 06:56:55.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 06:56:55.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 04:25:58 and 07/18/2025 04:25:58 UTC. Search may be required.
2025-07-18 06:56:55.7|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 06:57:26.6|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 07:27:53.5|Info|RssSyncService|Starting RSS Sync
2025-07-18 07:27:54.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 07:27:54.0|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 07:27:54.2|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 04:25:54 and 07/18/2025 05:27:54 UTC. Search may be required.
2025-07-18 07:27:54.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 04:25:54 and 07/18/2025 05:27:54 UTC. Search may be required.
2025-07-18 07:27:57.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 07:27:58.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 04:25:58 and 07/18/2025 05:27:58 UTC. Search may be required.
2025-07-18 07:28:02.1|Info|DownloadDecisionMaker|Processing 695 releases
2025-07-18 07:28:31.7|Info|RssSyncService|RSS Sync Completed. Reports found: 695, Reports grabbed: 0
2025-07-18 07:58:53.6|Info|RssSyncService|Starting RSS Sync
2025-07-18 07:58:53.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 05:27:54 and 07/18/2025 05:27:54 UTC. Search may be required.
2025-07-18 07:58:54.0|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 05:27:54 and 07/18/2025 05:27:54 UTC. Search may be required.
2025-07-18 07:58:55.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 07:58:55.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 05:27:58 and 07/18/2025 05:27:58 UTC. Search may be required.
2025-07-18 07:58:55.8|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 07:59:25.9|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 08:29:53.7|Info|RssSyncService|Starting RSS Sync
2025-07-18 08:29:54.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 05:27:54 and 07/18/2025 06:29:54 UTC. Search may be required.
2025-07-18 08:29:54.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 05:27:54 and 07/18/2025 06:29:54 UTC. Search may be required.
2025-07-18 08:29:56.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 08:29:58.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 05:27:58 and 07/18/2025 06:29:58 UTC. Search may be required.
2025-07-18 08:30:01.6|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 08:30:33.0|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 09:00:53.8|Info|RssSyncService|Starting RSS Sync
2025-07-18 09:00:53.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 06:29:54 and 07/18/2025 06:29:54 UTC. Search may be required.
2025-07-18 09:00:54.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 06:29:54 and 07/18/2025 06:29:54 UTC. Search may be required.
2025-07-18 09:00:54.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 09:00:54.1|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 09:00:55.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 09:00:56.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 06:29:58 and 07/18/2025 06:29:58 UTC. Search may be required.
2025-07-18 09:00:56.0|Info|DownloadDecisionMaker|Processing 695 releases
2025-07-18 09:01:24.7|Info|RssSyncService|RSS Sync Completed. Reports found: 695, Reports grabbed: 0
2025-07-18 09:31:53.8|Info|RssSyncService|Starting RSS Sync
2025-07-18 09:31:54.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 09:31:54.2|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 09:31:54.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 06:29:54 and 07/18/2025 07:31:54 UTC. Search may be required.
2025-07-18 09:31:55.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 06:29:54 and 07/18/2025 07:31:55 UTC. Search may be required.
2025-07-18 09:31:57.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 09:31:59.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 06:29:58 and 07/18/2025 07:31:59 UTC. Search may be required.
2025-07-18 09:32:02.5|Info|DownloadDecisionMaker|Processing 695 releases
2025-07-18 09:32:32.3|Info|RssSyncService|RSS Sync Completed. Reports found: 695, Reports grabbed: 0
2025-07-18 10:02:54.1|Info|RssSyncService|Starting RSS Sync
2025-07-18 10:02:54.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 07:31:55 and 07/18/2025 07:31:55 UTC. Search may be required.
2025-07-18 10:02:54.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-18 10:02:54.6|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=movie&cat=2000,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-18 10:02:54.6|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 07:31:54 and 07/18/2025 07:31:54 UTC. Search may be required.
2025-07-18 10:02:56.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 10:02:56.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 07:31:59 and 07/18/2025 07:31:59 UTC. Search may be required.
2025-07-18 10:02:56.6|Info|DownloadDecisionMaker|Processing 695 releases
2025-07-18 10:03:48.8|Info|RssSyncService|RSS Sync Completed. Reports found: 695, Reports grabbed: 0
2025-07-18 10:33:54.3|Info|RssSyncService|Starting RSS Sync
2025-07-18 10:33:54.7|Warn|Torznab|Indexer Ilcorsaronero rss sync didn't cover the period between 07/18/2025 05:45:56 and 07/18/2025 07:14:34 UTC. Search may be required.
2025-07-18 10:33:54.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 07:31:55 and 07/18/2025 08:33:54 UTC. Search may be required.
2025-07-18 10:33:54.8|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 07:31:54 and 07/18/2025 08:33:54 UTC. Search may be required.
2025-07-18 10:33:56.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 10:33:58.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 07:31:59 and 07/18/2025 08:33:58 UTC. Search may be required.
2025-07-18 10:34:01.9|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 10:34:37.4|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
2025-07-18 11:04:54.5|Info|RssSyncService|Starting RSS Sync
2025-07-18 11:04:54.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/18/2025 08:33:54 and 07/18/2025 08:33:54 UTC. Search may be required.
2025-07-18 11:04:55.1|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/18/2025 08:33:54 and 07/18/2025 08:33:54 UTC. Search may be required.
2025-07-18 11:04:56.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/15/2025 22:00:00 and 07/15/2025 22:00:00 UTC. Search may be required.
2025-07-18 11:04:56.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/18/2025 08:33:58 and 07/18/2025 08:33:58 UTC. Search may be required.
2025-07-18 11:04:56.8|Info|DownloadDecisionMaker|Processing 710 releases
2025-07-18 11:05:33.6|Info|RssSyncService|RSS Sync Completed. Reports found: 710, Reports grabbed: 0
