[2025-07-17 01:59:56.694 +02:00] [INF] [33] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-17 01:59:56.786 +02:00] [INF] [33] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-17 01:59:58.442 +02:00] [INF] [33] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-17 02:00:00.000 +02:00, which is 00:00:01.5576638 from now.
[2025-07-17 02:00:00.137 +02:00] [INF] [31] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-17 02:00:00.225 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-17 02:00:01.003 +02:00] [INF] [33] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-18 02:00:00.000 +02:00, which is 23:59:58.9963315 from now.
[2025-07-17 02:59:56.548 +02:00] [INF] [60] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-17 02:59:57.370 +02:00] [INF] [60] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-17 03:00:00.000 +02:00, which is 00:00:02.6292550 from now.
[2025-07-17 03:00:00.034 +02:00] [INF] [46] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-17 03:00:01.006 +02:00] [INF] [56] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-18 03:00:00.000 +02:00, which is 23:59:58.9936999 from now.
[2025-07-17 06:06:53.705 +02:00] [INF] [32] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-17 06:06:53.708 +02:00] [INF] [32] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-17 06:06:53.711 +02:00] [INF] [32] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-17 06:06:53.714 +02:00] [INF] [32] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-17 06:06:54.681 +02:00] [WRN] [32] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-17 06:06:55.477 +02:00] [WRN] [32] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-17 06:06:55.974 +02:00] [INF] [32] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "La Patrulla Canina: La superpelícula", Path: "/CONTENIDO/PELIS/PAW Patrol - The Mighty Movie (2023)/PAW Patrol - The Mighty Movie (2023) Bluray-1080p.mkv", Id: 0148a18f-ab6d-1257-5c74-0c41639756b6
[2025-07-17 06:06:56.093 +02:00] [INF] [32] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Folder", Name: "Kung Fu Panda 3 (2016)", Path: "/CONTENIDO/PELIS/Kung Fu Panda 3 (2016)", Id: 7a512fdb-2d94-c46d-88e7-fb51d289e358
[2025-07-17 06:06:56.466 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 2 seconds
[2025-07-17 06:06:57.692 +02:00] [INF] [56] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-17 06:06:58.488 +02:00] [ERR] [50] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-17 06:07:01.284 +02:00] [INF] [56] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda 3 (2016)/Kung Fu Panda 3 (2016) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-07-17 06:07:01.735 +02:00] [INF] [60] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-17 06:07:01.735 +02:00] [INF] [60] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-17 06:07:06.294 +02:00] [INF] [50] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-17 06:07:06.294 +02:00] [INF] [50] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-07-17 06:07:17.391 +02:00] [INF] [60] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-17 06:07:17.396 +02:00] [INF] [60] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-07-17 06:07:17.397 +02:00] [INF] [60] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-07-17 06:07:17.397 +02:00] [INF] [60] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 movies with event type Update
[2025-07-17 06:07:17.711 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-07-17 06:07:17.712 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-07-17 06:07:17.712 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Update to process
[2025-07-17 06:07:17.715 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-07-17 06:07:17.716 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-07-17 06:07:17.716 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-07-17 06:07:22.315 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "a847210e366cd5a65e82007f706004c3" "Mikros Animation"
[2025-07-17 06:07:22.315 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Mikros Animation", Path: "/config/metadata/Studio/Mikros Animation", Id: a847210e-366c-d5a6-5e82-007f706004c3
[2025-07-17 06:07:22.341 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "403f01dab8ec502cefd4cc27aba8d825" "Mikros Image"
[2025-07-17 06:07:22.341 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Mikros Image", Path: "/config/metadata/Studio/Mikros Image", Id: 403f01da-b8ec-502c-efd4-cc27aba8d825
[2025-07-17 06:07:22.350 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "dc94c71ab0e1ab8e4da10a02a1119e0d" "Nickelodeon Movies"
[2025-07-17 06:07:22.350 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Nickelodeon Movies", Path: "/config/metadata/Studio/Nickelodeon Movies", Id: dc94c71a-b0e1-ab8e-4da1-0a02a1119e0d
[2025-07-17 06:07:22.519 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "9d0fbf15b055e2f26308b3dca496fa90" "Spin Master"
[2025-07-17 06:07:22.519 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Spin Master", Path: "/config/metadata/Studio/Spin Master", Id: 9d0fbf15-b055-e2f2-6308-b3dca496fa90
[2025-07-17 06:07:22.528 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "1ecda6c169461160218c3e19647b8a38" "adrian150 Animation"
[2025-07-17 06:07:22.528 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "adrian150 Animation", Path: "/config/metadata/Studio/adrian150 Animation", Id: 1ecda6c1-6946-1160-218c-3e19647b8a38
[2025-07-17 06:07:23.445 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 29 seconds
[2025-07-17 06:07:23.449 +02:00] [INF] [33] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-17 06:07:23.612 +02:00] [INF] [50] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-17 06:07:23.776 +02:00] [INF] [55] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-17 14:42:40.122 +02:00] [INF] [176] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-17 14:42:40.123 +02:00] [INF] [176] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-17 14:42:40.127 +02:00] [INF] [176] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-17 14:43:13.829 +02:00] [INF] [179] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-17 14:43:13.830 +02:00] [INF] [179] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-17 14:43:13.830 +02:00] [INF] [179] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-17 16:00:42.528 +02:00] [INF] [184] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 16:02:44.246 +02:00] [INF] [163] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 16:03:40.866 +02:00] [INF] [63] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-07-17 16:03:40.876 +02:00] [INF] [63] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 4/0
[2025-07-17 16:03:40.909 +02:00] [INF] [63] Emby.Server.Implementations.Session.SessionManager: Logging out access token "a27d76c080a943ba8879c891f1b5f2e6"
[2025-07-17 16:03:40.993 +02:00] [INF] [63] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64
[2025-07-17 16:21:42.157 +02:00] [INF] [138] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-07-17 16:21:42.159 +02:00] [INF] [138] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 5/0
[2025-07-17 16:21:42.159 +02:00] [INF] [138] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64
[2025-07-17 16:23:47.901 +02:00] [INF] [172] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-07-17 16:23:47.901 +02:00] [INF] [172] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 6/0
[2025-07-17 16:23:47.901 +02:00] [INF] [172] Emby.Server.Implementations.Session.SessionManager: Logging out access token "94579c467ee9433e8063caf78832a9a8"
[2025-07-17 16:23:47.926 +02:00] [INF] [172] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64
[2025-07-17 17:00:28.512 +02:00] [INF] [53] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 17:01:59.480 +02:00] [INF] [171] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "carva" has succeeded.
[2025-07-17 17:01:59.480 +02:00] [INF] [171] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "carva": 0/0
[2025-07-17 17:01:59.480 +02:00] [INF] [171] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user 324327e7-0585-4b5c-af15-d07e63238ec8
[2025-07-17 17:01:59.582 +02:00] [INF] [173] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 17:02:20.559 +02:00] [INF] [22] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "carva". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-17 17:02:21.420 +02:00] [INF] [171] Jellyfin.Api.Controllers.DynamicHlsController: Current HLS implementation doesn't support non-keyframe breaks but one is requested, ignoring that request
[2025-07-17 17:02:21.433 +02:00] [INF] [171] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/PELIS/Kung Fu Panda (2008)/Kung Fu Panda (2008) Bluray-720p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 libfdk_aac -ac 2 -ab 256000 -af \"volume=2\" -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename \"32f18cff521dc993f6d9f21eb81fcb03-1.mp4\" -start_number 0 -hls_segment_filename \"/cache/transcodes/32f18cff521dc993f6d9f21eb81fcb03%d.mp4\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/32f18cff521dc993f6d9f21eb81fcb03.m3u8\""
[2025-07-17 17:02:25.035 +02:00] [INF] [180] MediaBrowser.MediaEncoding.Subtitles.SubtitleEncoder: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-i file:\"/CONTENIDO/PELIS/Kung Fu Panda (2008)/Kung Fu Panda (2008) Bluray-720p.mkv\" -copyts -map 0:6 -an -vn -c:s copy \"/config/data/subtitles/5/5ead447f-0fbb-3c9f-2fd1-3a95556a9dac.srt\""
[2025-07-17 17:04:33.607 +02:00] [INF] [169] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 17:04:35.443 +02:00] [INF] [169] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "carva" has succeeded.
[2025-07-17 17:04:35.444 +02:00] [INF] [169] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "carva": 1/0
[2025-07-17 17:04:35.445 +02:00] [INF] [169] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user 324327e7-0585-4b5c-af15-d07e63238ec8
[2025-07-17 17:04:35.645 +02:00] [INF] [171] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 17:04:41.823 +02:00] [INF] [180] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 17:05:13.086 +02:00] [INF] [186] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "carva" has succeeded.
[2025-07-17 17:05:13.086 +02:00] [INF] [186] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "carva": 1/0
[2025-07-17 17:05:13.086 +02:00] [INF] [186] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user 324327e7-0585-4b5c-af15-d07e63238ec8
[2025-07-17 17:06:16.808 +02:00] [INF] [183] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 17:06:20.126 +02:00] [INF] [138] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 17:06:50.342 +02:00] [INF] [138] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/32f18cff521dc993f6d9f21eb81fcb03.m3u8"
[2025-07-17 17:06:52.377 +02:00] [INF] [138] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-07-17 17:06:52.378 +02:00] [INF] [138] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/32f18cff521dc993f6d9f21eb81fcb03.m3u8"
[2025-07-17 17:06:59.234 +02:00] [INF] [180] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Web" "10.10.7" playing "Kung Fu Panda". Stopped at "109237" ms
[2025-07-17 17:07:14.873 +02:00] [INF] [22] MediaBrowser.MediaEncoding.Subtitles.SubtitleEncoder: ffmpeg subtitle extraction completed for "file:\"/CONTENIDO/PELIS/Kung Fu Panda (2008)/Kung Fu Panda (2008) Bluray-720p.mkv\"" to "/config/data/subtitles/5/5ead447f-0fbb-3c9f-2fd1-3a95556a9dac.srt"
[2025-07-17 17:08:56.359 +02:00] [INF] [52] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 17:12:44.339 +02:00] [INF] [179] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 17:16:32.309 +02:00] [INF] [174] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 17:20:56.279 +02:00] [INF] [54] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 17:24:44.263 +02:00] [INF] [173] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 17:28:32.252 +02:00] [INF] [80] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 17:32:56.233 +02:00] [INF] [160] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 17:36:44.220 +02:00] [INF] [53] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 17:40:33.788 +02:00] [INF] [52] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 17:47:15.378 +02:00] [INF] [162] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "carva" has succeeded.
[2025-07-17 17:47:15.392 +02:00] [INF] [162] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "carva": 1/0
[2025-07-17 17:47:15.395 +02:00] [INF] [162] Emby.Server.Implementations.Session.SessionManager: Logging out access token "11ec57da5e0e4230bc9d76c456d3c035"
[2025-07-17 17:47:15.443 +02:00] [INF] [162] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user 324327e7-0585-4b5c-af15-d07e63238ec8
[2025-07-17 18:05:50.359 +02:00] [INF] [52] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar los subtítulos que faltan" Completed after 0 minute(s) and 0 seconds
[2025-07-17 18:06:11.433 +02:00] [INF] [80] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar letras faltantes" Completed after 0 minute(s) and 21 seconds
[2025-07-17 18:06:13.005 +02:00] [INF] [162] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 18:06:13.552 +02:00] [INF] [162] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-17 18:06:13.926 +02:00] [INF] [173] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar registros" Completed after 0 minute(s) and 0 seconds
[2025-07-17 18:06:14.320 +02:00] [INF] [162] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: Optimizing and vacuuming jellyfin.db...
[2025-07-17 18:06:14.323 +02:00] [INF] [173] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 18:06:15.455 +02:00] [INF] [173] Emby.Server.Implementations.ScheduledTasks.TaskManager: "TasksRefreshChannels" Completed after 0 minute(s) and 0 seconds
[2025-07-17 18:06:17.486 +02:00] [INF] [162] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: jellyfin.db optimized successfully!
[2025-07-17 18:06:17.487 +02:00] [INF] [162] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Optimizar la base de datos" Completed after 0 minute(s) and 3 seconds
[2025-07-17 18:06:25.399 +02:00] [INF] [19] Jellyfin.LiveTv.Guide.GuideManager: Refreshing guide with 7 days of guide data
[2025-07-17 18:06:25.562 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Refresh Guide" Completed after 0 minute(s) and 11 seconds
[2025-07-17 18:06:26.156 +02:00] [INF] [52] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Normalización de audio" Completed after 0 minute(s) and 14 seconds
[2025-07-17 18:06:31.120 +02:00] [INF] [80] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Eliminar archivos temporales" Completed after 0 minute(s) and 17 seconds
[2025-07-17 18:06:32.613 +02:00] [INF] [173] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 20 seconds
[2025-07-17 18:07:54.431 +02:00] [INF] [52] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-17 18:07:54.442 +02:00] [INF] [52] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-17 18:07:54.531 +02:00] [INF] [52] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-17 18:07:54.599 +02:00] [INF] [52] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-17 18:07:59.904 +02:00] [WRN] [52] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-17 18:08:05.187 +02:00] [WRN] [52] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-17 18:08:06.507 +02:00] [INF] [80] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 12 seconds
[2025-07-17 18:08:11.589 +02:00] [INF] [80] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-17 18:08:13.245 +02:00] [ERR] [39] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-17 18:08:18.398 +02:00] [INF] [53] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-17 18:08:18.406 +02:00] [INF] [53] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-07-17 18:08:21.966 +02:00] [INF] [58] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/John Wick - Chapter 4 (2023)/John Wick - Chapter 4 (2023) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-07-17 18:08:23.290 +02:00] [INF] [39] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-17 18:08:23.290 +02:00] [INF] [39] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-17 18:08:41.253 +02:00] [INF] [52] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-17 18:08:41.254 +02:00] [INF] [52] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-07-17 18:08:41.257 +02:00] [INF] [52] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-07-17 18:08:41.257 +02:00] [INF] [52] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 movies with event type Update
[2025-07-17 18:08:41.520 +02:00] [INF] [38] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-07-17 18:08:41.520 +02:00] [INF] [38] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-07-17 18:08:41.520 +02:00] [INF] [38] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Update to process
[2025-07-17 18:08:41.520 +02:00] [INF] [38] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-07-17 18:08:41.520 +02:00] [INF] [38] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-07-17 18:08:41.520 +02:00] [INF] [38] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-07-17 18:09:06.286 +02:00] [INF] [39] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/fee1ceac64ef4e7fbe899a8200e1dcd2.png"
[2025-07-17 18:09:06.743 +02:00] [INF] [39] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/fee1ceac64ef4e7fbe899a8200e1dcd2.png"
[2025-07-17 18:09:11.464 +02:00] [INF] [39] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 1 minute(s) and 17 seconds
[2025-07-17 18:09:11.564 +02:00] [INF] [162] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-17 18:09:11.819 +02:00] [INF] [52] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-17 18:09:12.108 +02:00] [INF] [38] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-17 18:57:05.808 +02:00] [INF] [163] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 18:57:10.890 +02:00] [INF] [33] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-17 18:57:13.162 +02:00] [WRN] [33] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-07-17 18:58:19.219 +02:00] [INF] [60] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 18:58:19.303 +02:00] [ERR] [60] Jellyfin.Api.Middleware.ExceptionMiddleware: Error processing request: "Unexpected end of request content". URL "POST" "/Sessions/Playing/Stopped".
[2025-07-17 19:37:47.966 +02:00] [INF] [175] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 19:41:35.063 +02:00] [INF] [152] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 19:45:23.057 +02:00] [INF] [150] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 19:49:47.043 +02:00] [INF] [64] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 19:50:44.753 +02:00] [WRN] [56] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-07-17 19:50:44.792 +02:00] [INF] [56] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 19:53:35.035 +02:00] [INF] [159] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 19:57:23.027 +02:00] [INF] [56] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:01:47.015 +02:00] [INF] [64] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:05:35.009 +02:00] [INF] [22] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:09:22.999 +02:00] [INF] [174] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:13:46.986 +02:00] [INF] [174] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:17:34.977 +02:00] [INF] [172] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:21:22.972 +02:00] [INF] [166] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:25:46.959 +02:00] [INF] [169] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:29:34.950 +02:00] [INF] [32] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:33:22.944 +02:00] [INF] [172] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:37:46.932 +02:00] [INF] [154] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:41:34.924 +02:00] [INF] [162] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:45:22.916 +02:00] [INF] [61] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:49:46.901 +02:00] [INF] [63] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:53:34.895 +02:00] [INF] [144] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 20:57:22.889 +02:00] [INF] [144] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 21:01:46.878 +02:00] [INF] [155] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 21:05:34.868 +02:00] [INF] [186] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 21:09:22.864 +02:00] [INF] [22] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 21:13:46.855 +02:00] [INF] [20] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 21:17:34.843 +02:00] [INF] [20] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 21:21:22.835 +02:00] [INF] [138] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 21:37:06.868 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-17 21:37:07.003 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_DATA_DIR, /config]"]
[2025-07-17 21:37:07.014 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-17 21:37:07.014 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-17 21:37:07.015 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-17 21:37:07.028 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-17 21:37:07.028 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-17 21:37:07.030 +02:00] [INF] [1] Main: Processor count: 2
[2025-07-17 21:37:07.036 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-17 21:37:07.045 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-17 21:37:07.045 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-17 21:37:07.046 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-17 21:37:07.046 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-17 21:37:07.046 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-17 21:37:07.046 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-17 21:37:08.258 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-17 21:37:08.764 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-17 21:37:08.927 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-17 21:37:08.974 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-17 21:37:09.011 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-17 21:37:09.399 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-17 21:37:09.402 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-17 21:37:09.402 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-17 21:37:09.403 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-17 21:37:09.403 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-17 21:37:09.403 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-17 21:37:09.403 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-17 21:37:29.292 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-17 21:37:29.300 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-17 21:37:29.301 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-17 21:37:29.303 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-17 21:37:29.303 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-17 21:37:29.361 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-17 21:37:29.362 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-17 21:37:29.589 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-17 21:37:31.042 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-17 21:37:31.138 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-17 21:37:31.173 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-18 03:00:00.000 +02:00, which is 05:22:28.8262451 from now.
[2025-07-17 21:37:31.230 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-18 02:00:00.000 +02:00, which is 04:22:28.7698753 from now.
[2025-07-17 21:37:31.514 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-17 21:37:31.601 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-17 21:37:31.665 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-17 21:37:31.694 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-17 21:37:31.883 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-17 21:37:34.268 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-17 21:37:34.375 +02:00] [INF] [15] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-17 21:37:39.309 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 4 seconds
[2025-07-17 21:37:43.491 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-17 21:37:43.501 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-17 21:37:43.501 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-17 21:37:43.502 +02:00] [INF] [1] Main: Startup complete 0:00:37.9499778
[2025-07-17 21:37:48.818 +02:00] [INF] [16] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 21:37:59.168 +02:00] [INF] [16] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-17 21:38:11.166 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-17 21:38:11.507 +02:00] [INF] [13] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-17 21:38:24.675 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-17 22:26:03.838 +02:00] [INF] [42] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-07-17 22:26:03.849 +02:00] [INF] [42] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 1/0
[2025-07-17 22:26:03.855 +02:00] [INF] [42] Emby.Server.Implementations.Session.SessionManager: Logging out access token "7faa1fd6d86647bbaf193afe2301d927"
[2025-07-17 22:26:03.911 +02:00] [INF] [42] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64
[2025-07-17 22:26:04.290 +02:00] [INF] [37] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 22:30:12.627 +02:00] [INF] [34] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 22:30:23.438 +02:00] [WRN] [28] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-07-17 22:30:23.472 +02:00] [INF] [28] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 22:42:35.869 +02:00] [INF] [27] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Secreto de psiquiatra". Stopped at "3196821" ms
[2025-07-17 22:43:49.701 +02:00] [INF] [41] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 22:44:01.791 +02:00] [INF] [64] Emby.Server.Implementations.Session.SessionManager: Logging out access token "bf1aacaa568b4d3aa1b528a8c52862e7"
[2025-07-17 22:45:36.942 +02:00] [INF] [26] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-07-17 22:45:36.942 +02:00] [INF] [26] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 2/0
[2025-07-17 22:45:36.943 +02:00] [INF] [26] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64
[2025-07-17 22:45:37.061 +02:00] [INF] [26] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 22:49:24.527 +02:00] [INF] [69] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 22:49:36.524 +02:00] [INF] [69] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-17 22:49:43.640 +02:00] [WRN] [76] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-07-17 22:49:43.646 +02:00] [INF] [76] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 23:19:48.255 +02:00] [INF] [77] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-17 23:19:48.572 +02:00] [INF] [77] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-17 23:19:49.287 +02:00] [INF] [77] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
