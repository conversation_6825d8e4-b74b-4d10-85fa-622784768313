[2025-07-17 01:59:56.694 +02:00] [INF] [33] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-17 01:59:56.786 +02:00] [INF] [33] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-17 01:59:58.442 +02:00] [INF] [33] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-17 02:00:00.000 +02:00, which is 00:00:01.5576638 from now.
[2025-07-17 02:00:00.137 +02:00] [INF] [31] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-17 02:00:00.225 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-17 02:00:01.003 +02:00] [INF] [33] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-18 02:00:00.000 +02:00, which is 23:59:58.9963315 from now.
[2025-07-17 02:59:56.548 +02:00] [INF] [60] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-17 02:59:57.370 +02:00] [INF] [60] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-17 03:00:00.000 +02:00, which is 00:00:02.6292550 from now.
[2025-07-17 03:00:00.034 +02:00] [INF] [46] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-17 03:00:01.006 +02:00] [INF] [56] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-18 03:00:00.000 +02:00, which is 23:59:58.9936999 from now.
[2025-07-17 06:06:53.705 +02:00] [INF] [32] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-17 06:06:53.708 +02:00] [INF] [32] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-17 06:06:53.711 +02:00] [INF] [32] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-17 06:06:53.714 +02:00] [INF] [32] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-17 06:06:54.681 +02:00] [WRN] [32] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-17 06:06:55.477 +02:00] [WRN] [32] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-17 06:06:55.974 +02:00] [INF] [32] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "La Patrulla Canina: La superpelícula", Path: "/CONTENIDO/PELIS/PAW Patrol - The Mighty Movie (2023)/PAW Patrol - The Mighty Movie (2023) Bluray-1080p.mkv", Id: 0148a18f-ab6d-1257-5c74-0c41639756b6
[2025-07-17 06:06:56.093 +02:00] [INF] [32] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Folder", Name: "Kung Fu Panda 3 (2016)", Path: "/CONTENIDO/PELIS/Kung Fu Panda 3 (2016)", Id: 7a512fdb-2d94-c46d-88e7-fb51d289e358
[2025-07-17 06:06:56.466 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 2 seconds
[2025-07-17 06:06:57.692 +02:00] [INF] [56] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-17 06:06:58.488 +02:00] [ERR] [50] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-17 06:07:01.284 +02:00] [INF] [56] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda 3 (2016)/Kung Fu Panda 3 (2016) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-07-17 06:07:01.735 +02:00] [INF] [60] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-17 06:07:01.735 +02:00] [INF] [60] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-17 06:07:06.294 +02:00] [INF] [50] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-17 06:07:06.294 +02:00] [INF] [50] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-07-17 06:07:17.391 +02:00] [INF] [60] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-17 06:07:17.396 +02:00] [INF] [60] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-07-17 06:07:17.397 +02:00] [INF] [60] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-07-17 06:07:17.397 +02:00] [INF] [60] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 movies with event type Update
[2025-07-17 06:07:17.711 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-07-17 06:07:17.712 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-07-17 06:07:17.712 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Update to process
[2025-07-17 06:07:17.715 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-07-17 06:07:17.716 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-07-17 06:07:17.716 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-07-17 06:07:22.315 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "a847210e366cd5a65e82007f706004c3" "Mikros Animation"
[2025-07-17 06:07:22.315 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Mikros Animation", Path: "/config/metadata/Studio/Mikros Animation", Id: a847210e-366c-d5a6-5e82-007f706004c3
[2025-07-17 06:07:22.341 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "403f01dab8ec502cefd4cc27aba8d825" "Mikros Image"
[2025-07-17 06:07:22.341 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Mikros Image", Path: "/config/metadata/Studio/Mikros Image", Id: 403f01da-b8ec-502c-efd4-cc27aba8d825
[2025-07-17 06:07:22.350 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "dc94c71ab0e1ab8e4da10a02a1119e0d" "Nickelodeon Movies"
[2025-07-17 06:07:22.350 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Nickelodeon Movies", Path: "/config/metadata/Studio/Nickelodeon Movies", Id: dc94c71a-b0e1-ab8e-4da1-0a02a1119e0d
[2025-07-17 06:07:22.519 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "9d0fbf15b055e2f26308b3dca496fa90" "Spin Master"
[2025-07-17 06:07:22.519 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Spin Master", Path: "/config/metadata/Studio/Spin Master", Id: 9d0fbf15-b055-e2f2-6308-b3dca496fa90
[2025-07-17 06:07:22.528 +02:00] [INF] [31] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "1ecda6c169461160218c3e19647b8a38" "adrian150 Animation"
[2025-07-17 06:07:22.528 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "adrian150 Animation", Path: "/config/metadata/Studio/adrian150 Animation", Id: 1ecda6c1-6946-1160-218c-3e19647b8a38
[2025-07-17 06:07:23.445 +02:00] [INF] [31] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 29 seconds
[2025-07-17 06:07:23.449 +02:00] [INF] [33] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-17 06:07:23.612 +02:00] [INF] [50] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-17 06:07:23.776 +02:00] [INF] [55] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-17 14:42:40.122 +02:00] [INF] [176] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-17 14:42:40.123 +02:00] [INF] [176] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-17 14:42:40.127 +02:00] [INF] [176] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-17 14:43:13.829 +02:00] [INF] [179] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-17 14:43:13.830 +02:00] [INF] [179] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-17 14:43:13.830 +02:00] [INF] [179] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
