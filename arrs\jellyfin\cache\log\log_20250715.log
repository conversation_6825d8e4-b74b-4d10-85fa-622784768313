[2025-07-15 00:40:15.399 +02:00] [INF] [22] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Cocodrilo". Stopped at "2248262" ms
[2025-07-15 00:40:18.556 +02:00] [INF] [10] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-15 00:58:31.449 +02:00] [INF] [32] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Cocodrilo". Stopped at "3256964" ms
[2025-07-15 00:59:37.365 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-15 00:59:37.365 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-15 00:59:37.366 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-15 01:59:59.825 +02:00] [INF] [46] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-15 01:59:59.908 +02:00] [INF] [46] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-15 02:00:00.452 +02:00] [INF] [45] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-16 02:00:00.000 +02:00, which is 23:59:59.5480409 from now.
[2025-07-15 02:59:59.533 +02:00] [INF] [84] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-15 03:00:00.326 +02:00] [INF] [76] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-16 03:00:00.000 +02:00, which is 23:59:59.6733838 from now.
[2025-07-15 10:04:54.415 +02:00] [INF] [44] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-15 10:04:54.432 +02:00] [INF] [44] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-15 10:04:54.432 +02:00] [INF] [44] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-15 10:04:54.442 +02:00] [INF] [44] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-15 10:04:56.023 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 1 seconds
[2025-07-15 10:04:57.268 +02:00] [WRN] [25] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-15 10:04:57.534 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/2ce3fc0d482c4c81ae662a8997a61287.png"
[2025-07-15 10:04:57.962 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/2ce3fc0d482c4c81ae662a8997a61287.png"
[2025-07-15 10:04:58.535 +02:00] [WRN] [25] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-15 10:04:59.152 +02:00] [INF] [44] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Folder", Name: "Kung Fu Panda (2008)", Path: "/CONTENIDO/PELIS/Kung Fu Panda (2008)", Id: 35995ffd-8b5d-8705-c32f-a61c4c8cffcf
[2025-07-15 10:04:59.185 +02:00] [INF] [44] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Folder", Name: "Kung Fu Panda 2 (2011)", Path: "/CONTENIDO/PELIS/Kung Fu Panda 2 (2011)", Id: 2ba79f91-d4ec-8598-233a-74ac626f2c91
[2025-07-15 10:04:59.201 +02:00] [INF] [44] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Folder", Name: "Kung Fu Panda 4 (2024)", Path: "/CONTENIDO/PELIS/Kung Fu Panda 4 (2024)", Id: 580d7734-fa61-24c0-fa59-117acc20b052
[2025-07-15 10:05:00.646 +02:00] [INF] [21] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-15 10:05:00.682 +02:00] [INF] [21] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-15 10:05:02.097 +02:00] [ERR] [34] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-15 10:05:04.080 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda (2008)/Kung Fu Panda (2008) Bluray-720p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-07-15 10:05:04.088 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda 2 (2011)/Kung Fu Panda 2 (2011) Bluray-720p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-07-15 10:05:04.796 +02:00] [INF] [28] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-15 10:05:04.796 +02:00] [INF] [28] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-15 10:05:09.340 +02:00] [INF] [3] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-15 10:05:09.340 +02:00] [INF] [3] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-07-15 10:05:11.472 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Kung Fu Panda 4 (2024)/Kung Fu Panda 4 (2024) Bluray-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-07-15 10:05:25.501 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-15 10:05:25.505 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-07-15 10:05:25.505 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-07-15 10:05:25.505 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: Processing 3 movies with event type Update
[2025-07-15 10:05:25.806 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-07-15 10:05:25.806 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-07-15 10:05:25.807 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Update to process
[2025-07-15 10:05:25.809 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-07-15 10:05:25.809 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-07-15 10:05:25.809 +02:00] [INF] [25] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-07-15 10:05:29.065 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/16d2eee4d01e490e9ed5128bdedff555.png"
[2025-07-15 10:05:29.850 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/16d2eee4d01e490e9ed5128bdedff555.png"
[2025-07-15 10:05:29.914 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/624bea0bc59b4c8085d4d7ee0b033774.png"
[2025-07-15 10:05:30.252 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/624bea0bc59b4c8085d4d7ee0b033774.png"
[2025-07-15 10:05:30.357 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/cc1b1cbab5e541bca693e51c8e32ab4c.png"
[2025-07-15 10:05:30.683 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/cc1b1cbab5e541bca693e51c8e32ab4c.png"
[2025-07-15 10:05:30.787 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/ac03fbe0d5a94a86a2269b80ad440691.png"
[2025-07-15 10:05:31.177 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/ac03fbe0d5a94a86a2269b80ad440691.png"
[2025-07-15 10:05:31.253 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/0aca5c311cb34cf8aec716db327b27d2.png"
[2025-07-15 10:05:31.606 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/0aca5c311cb34cf8aec716db327b27d2.png"
[2025-07-15 10:05:31.697 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/01234ebde14b40f7b266cab0da146467.png"
[2025-07-15 10:05:32.429 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/01234ebde14b40f7b266cab0da146467.png"
[2025-07-15 10:05:32.522 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/9507a93c7b54462cbbe31085c1a2a71f.png"
[2025-07-15 10:05:33.063 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/9507a93c7b54462cbbe31085c1a2a71f.png"
[2025-07-15 10:05:33.134 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/1974fe75e0094dc2a26703c78541a4ea.png"
[2025-07-15 10:05:33.886 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/1974fe75e0094dc2a26703c78541a4ea.png"
[2025-07-15 10:05:33.959 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/ab4093f3dc7141c89e01224e219560c9.png"
[2025-07-15 10:05:34.764 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/ab4093f3dc7141c89e01224e219560c9.png"
[2025-07-15 10:05:34.825 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/41932609e99746f2b58d562f339c558a.png"
[2025-07-15 10:05:35.165 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/41932609e99746f2b58d562f339c558a.png"
[2025-07-15 10:05:35.258 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/3344621415824df5aedd5194546d505c.png"
[2025-07-15 10:05:35.562 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/3344621415824df5aedd5194546d505c.png"
[2025-07-15 10:05:35.662 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/837030ddb6ee4fa7a4a36c3e38907dfc.png"
[2025-07-15 10:05:36.309 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/837030ddb6ee4fa7a4a36c3e38907dfc.png"
[2025-07-15 10:05:36.402 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/c9d4df3f6d544028b0d3bd940a67042b.png"
[2025-07-15 10:05:36.768 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/c9d4df3f6d544028b0d3bd940a67042b.png"
[2025-07-15 10:05:36.865 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/d2dacd9ba46f4d55822a82cc6fc199dc.png"
[2025-07-15 10:05:37.728 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/d2dacd9ba46f4d55822a82cc6fc199dc.png"
[2025-07-15 10:05:37.842 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/8b8483317b0c45bdbb7309d075e01eed.png"
[2025-07-15 10:05:38.660 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/8b8483317b0c45bdbb7309d075e01eed.png"
[2025-07-15 10:05:38.772 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/4979ce8341d54de6a5099d9d92c48ac2.png"
[2025-07-15 10:05:39.094 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/4979ce8341d54de6a5099d9d92c48ac2.png"
[2025-07-15 10:05:39.203 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/3115a22bbcfa4b89afb3bdb378c4f48b.png"
[2025-07-15 10:05:40.253 +02:00] [INF] [29] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/3115a22bbcfa4b89afb3bdb378c4f48b.png"
[2025-07-15 10:05:40.330 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/c60562baef304b8487aa93bc9721aaf4.png"
[2025-07-15 10:05:40.676 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/c60562baef304b8487aa93bc9721aaf4.png"
[2025-07-15 10:05:40.798 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/36d1b7d71c8440fcad81a01d44603486.png"
[2025-07-15 10:05:41.095 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/36d1b7d71c8440fcad81a01d44603486.png"
[2025-07-15 10:05:41.169 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/72427c8f032c489396e24169050661f5.png"
[2025-07-15 10:05:41.863 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/72427c8f032c489396e24169050661f5.png"
[2025-07-15 10:05:41.944 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/a9474c08fffc4aeab31654bba8b86d61.png"
[2025-07-15 10:05:42.265 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/a9474c08fffc4aeab31654bba8b86d61.png"
[2025-07-15 10:05:42.361 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/9c7d7f7f806d4f2c8acc5811641230b1.png"
[2025-07-15 10:05:42.682 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/9c7d7f7f806d4f2c8acc5811641230b1.png"
[2025-07-15 10:05:42.906 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/bc29c634bc9a4eb796ab6591994b7d21.png"
[2025-07-15 10:05:43.306 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/bc29c634bc9a4eb796ab6591994b7d21.png"
[2025-07-15 10:05:43.411 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/cb0006c1f6e743719d14eecdf568deb5.png"
[2025-07-15 10:05:43.962 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/cb0006c1f6e743719d14eecdf568deb5.png"
[2025-07-15 10:05:44.067 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/fe754c0d2de34628bf1b0e2cc5bd9007.png"
[2025-07-15 10:05:44.929 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/fe754c0d2de34628bf1b0e2cc5bd9007.png"
[2025-07-15 10:05:45.004 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/019ede9d4b4e46f5ab4a5acdb2f56ad6.png"
[2025-07-15 10:05:45.353 +02:00] [INF] [25] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/019ede9d4b4e46f5ab4a5acdb2f56ad6.png"
[2025-07-15 10:05:45.475 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/d153186c2ab74e13a557a43c96065c11.png"
[2025-07-15 10:05:46.167 +02:00] [INF] [28] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/d153186c2ab74e13a557a43c96065c11.png"
[2025-07-15 10:05:47.771 +02:00] [INF] [29] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 53 seconds
[2025-07-15 10:05:47.774 +02:00] [INF] [3] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-15 10:05:47.926 +02:00] [INF] [3] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-15 10:05:48.084 +02:00] [INF] [29] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-15 10:18:48.901 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-15 10:18:49.204 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]"]
[2025-07-15 10:18:49.236 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-15 10:18:49.248 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-15 10:18:49.248 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-15 10:18:49.279 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-15 10:18:49.280 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-15 10:18:49.280 +02:00] [INF] [1] Main: Processor count: 2
[2025-07-15 10:18:49.282 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-15 10:18:49.283 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-15 10:18:49.283 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-15 10:18:49.283 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-15 10:18:49.283 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-15 10:18:49.283 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-15 10:18:49.283 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-15 10:18:50.405 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-15 10:18:50.855 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-15 10:18:51.002 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-15 10:18:51.071 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-15 10:18:51.109 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-15 10:18:51.360 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 10:18:51.360 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-15 10:18:51.361 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 10:18:51.362 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-15 10:18:51.363 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-15 10:18:51.364 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-15 10:18:51.367 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-15 10:19:09.804 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-15 10:19:09.810 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-15 10:19:09.811 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-15 10:19:09.812 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-15 10:19:09.814 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-15 10:19:09.852 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-15 10:19:09.853 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-15 10:19:10.009 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-15 10:19:10.767 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-15 10:19:10.840 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-15 10:19:10.849 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-15 10:19:10.887 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-16 03:00:00.000 +02:00, which is 16:40:49.1131746 from now.
[2025-07-15 10:19:10.964 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-16 02:00:00.000 +02:00, which is 15:40:49.0355817 from now.
[2025-07-15 10:19:11.064 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-15 10:19:11.274 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-15 10:19:11.359 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-15 10:19:11.390 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-15 10:19:11.417 +02:00] [INF] [13] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-15 10:19:11.426 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-15 10:19:11.639 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-15 10:19:14.001 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-15 10:19:14.040 +02:00] [INF] [13] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-15 10:19:15.773 +02:00] [INF] [13] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-07-15 10:19:22.869 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-15 10:19:22.883 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-15 10:19:22.883 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-15 10:19:22.883 +02:00] [INF] [1] Main: Startup complete 0:00:34.6705475
[2025-07-15 11:43:00.158 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-15 11:43:00.272 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]"]
[2025-07-15 11:43:00.280 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-15 11:43:00.281 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-15 11:43:00.282 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-15 11:43:00.284 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-15 11:43:00.284 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-15 11:43:00.284 +02:00] [INF] [1] Main: Processor count: 2
[2025-07-15 11:43:00.285 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-15 11:43:00.285 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-15 11:43:00.286 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-15 11:43:00.286 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-15 11:43:00.287 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-15 11:43:00.288 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-15 11:43:00.288 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-15 11:43:00.895 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-15 11:43:01.431 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-15 11:43:01.594 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-15 11:43:01.710 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-15 11:43:01.791 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-15 11:43:02.231 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 11:43:02.246 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-15 11:43:02.254 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 11:43:02.259 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-15 11:43:02.265 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-15 11:43:02.265 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-15 11:43:02.266 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-15 11:43:19.078 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-15 11:43:19.083 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-15 11:43:19.084 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-15 11:43:19.085 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-15 11:43:19.085 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-15 11:43:19.119 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-15 11:43:19.119 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-15 11:43:19.246 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-15 11:43:19.854 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-15 11:43:19.915 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-15 11:43:19.925 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-15 11:43:19.953 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-16 03:00:00.000 +02:00, which is 15:16:40.0463072 from now.
[2025-07-15 11:43:20.020 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-16 02:00:00.000 +02:00, which is 14:16:39.9799235 from now.
[2025-07-15 11:43:20.113 +02:00] [INF] [12] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-15 11:43:20.349 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-15 11:43:20.441 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-15 11:43:20.473 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-15 11:43:20.510 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-15 11:43:20.700 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-15 11:43:21.376 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-15 11:43:23.072 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-15 11:43:23.112 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-15 11:43:27.114 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 3 seconds
[2025-07-15 11:43:31.347 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-15 11:43:31.359 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-15 11:43:31.359 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-15 11:43:31.359 +02:00] [INF] [1] Main: Startup complete 0:00:32.1083372
[2025-07-15 11:49:25.978 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-15 11:49:26.043 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_CACHE_DIR, /cache]"]
[2025-07-15 11:49:26.059 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-15 11:49:26.061 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-15 11:49:26.062 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-15 11:49:26.065 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-15 11:49:26.065 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-15 11:49:26.065 +02:00] [INF] [1] Main: Processor count: 2
[2025-07-15 11:49:26.065 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-15 11:49:26.066 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-15 11:49:26.067 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-15 11:49:26.067 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-15 11:49:26.067 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-15 11:49:26.067 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-15 11:49:26.067 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-15 11:49:26.548 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-15 11:49:27.400 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-15 11:49:27.740 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-15 11:49:27.792 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-15 11:49:27.898 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-15 11:49:28.543 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 11:49:28.559 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-15 11:49:28.566 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 11:49:28.567 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-15 11:49:28.579 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-15 11:49:28.580 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-15 11:49:28.580 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-15 11:51:11.405 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-15 11:51:11.513 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_LOG_DIR, /config/log]"]
[2025-07-15 11:51:11.535 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-15 11:51:11.536 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-15 11:51:11.536 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-15 11:51:11.540 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-15 11:51:11.540 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-15 11:51:11.540 +02:00] [INF] [1] Main: Processor count: 2
[2025-07-15 11:51:11.541 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-15 11:51:11.543 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-15 11:51:11.543 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-15 11:51:11.544 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-15 11:51:11.548 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-15 11:51:11.548 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-15 11:51:11.549 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-15 11:51:12.478 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-15 11:51:13.106 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-15 11:51:13.297 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-15 11:51:13.347 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-15 11:51:13.383 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-15 11:51:13.716 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 11:51:13.721 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-15 11:51:13.724 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 11:51:13.729 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-15 11:51:13.731 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-15 11:51:13.731 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-15 11:51:13.732 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-15 11:51:34.050 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-15 11:51:34.056 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-15 11:51:34.057 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-15 11:51:34.057 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-15 11:51:34.057 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-15 11:51:34.092 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-15 11:51:34.092 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-15 11:51:34.251 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-15 11:51:35.055 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-15 11:51:35.116 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-15 11:51:35.126 +02:00] [INF] [12] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-15 11:51:35.163 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-16 03:00:00.000 +02:00, which is 15:08:24.8368614 from now.
[2025-07-15 11:51:35.249 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-16 02:00:00.000 +02:00, which is 14:08:24.7505418 from now.
[2025-07-15 11:51:35.404 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-15 11:51:35.737 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-15 11:51:35.855 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-15 11:51:35.914 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-15 11:51:35.988 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-15 11:51:36.364 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-15 11:51:37.617 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-15 11:51:38.340 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-15 11:51:38.372 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-15 11:51:42.618 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 4 seconds
[2025-07-15 11:51:50.549 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-15 11:51:50.562 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-15 11:51:50.562 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-15 11:51:50.562 +02:00] [INF] [1] Main: Startup complete 0:00:39.8978332
[2025-07-15 11:56:44.297 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-15 11:56:44.342 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_DATA_DIR, /config]"]
[2025-07-15 11:56:44.344 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-15 11:56:44.346 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-15 11:56:44.346 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-15 11:56:44.348 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-15 11:56:44.348 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-15 11:56:44.348 +02:00] [INF] [1] Main: Processor count: 2
[2025-07-15 11:56:44.349 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-15 11:56:44.349 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-15 11:56:44.349 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-15 11:56:44.350 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-15 11:56:44.350 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-15 11:56:44.351 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-15 11:56:44.351 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-15 11:56:44.868 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-15 11:56:45.273 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-15 11:56:45.414 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-15 11:56:45.475 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-15 11:56:45.497 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-15 11:56:46.078 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 11:56:46.080 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-15 11:56:46.081 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 11:56:46.086 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-15 11:56:46.098 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-15 11:56:46.100 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-15 11:56:46.103 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-15 11:57:03.278 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-15 11:57:03.285 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-15 11:57:03.287 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-15 11:57:03.287 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-15 11:57:03.287 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-15 11:57:03.313 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-15 11:57:03.313 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-15 11:57:03.451 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-15 11:57:04.031 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-15 11:57:04.075 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-15 11:57:04.083 +02:00] [INF] [12] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-15 11:57:04.103 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-16 03:00:00.000 +02:00, which is 15:02:55.8961507 from now.
[2025-07-15 11:57:04.126 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-16 02:00:00.000 +02:00, which is 14:02:55.8739256 from now.
[2025-07-15 11:57:04.287 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-15 11:57:04.362 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-15 11:57:04.430 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-15 11:57:04.461 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-15 11:57:04.493 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-15 11:57:04.682 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-15 11:57:05.516 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-15 11:57:07.141 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-15 11:57:07.176 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-15 11:57:10.124 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 2 seconds
[2025-07-15 11:57:15.114 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-15 11:57:15.117 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-15 11:57:15.117 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-15 11:57:15.117 +02:00] [INF] [1] Main: Startup complete 0:00:31.3662836
[2025-07-15 14:32:13.617 +02:00] [INF] [33] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-15 14:34:02.307 +02:00] [INF] [41] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-15 14:37:26.294 +02:00] [INF] [44] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-15 14:37:38.294 +02:00] [INF] [45] Emby.Server.Implementations.Session.SessionWebSocketListener: Lost 1 WebSockets.
[2025-07-15 14:38:41.823 +02:00] [WRN] [45] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-07-15 14:38:41.838 +02:00] [INF] [45] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-15 18:03:16.667 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-15 18:03:17.006 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]"]
[2025-07-15 18:03:17.025 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-15 18:03:17.035 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-15 18:03:17.037 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-15 18:03:17.046 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-15 18:03:17.047 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-15 18:03:17.049 +02:00] [INF] [1] Main: Processor count: 2
[2025-07-15 18:03:17.049 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-15 18:03:17.051 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-15 18:03:17.054 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-15 18:03:17.055 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-15 18:03:17.058 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-15 18:03:17.058 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-15 18:03:17.058 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-15 18:03:17.984 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-15 18:03:19.017 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-15 18:03:19.291 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-15 18:03:19.329 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-15 18:03:19.394 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-15 18:03:20.298 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 18:03:20.305 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-15 18:03:20.306 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-15 18:03:20.308 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-15 18:03:20.313 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-15 18:03:20.315 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-15 18:03:20.315 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-15 18:03:54.153 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-15 18:03:54.160 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-15 18:03:54.162 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-15 18:03:54.163 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-15 18:03:54.164 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-15 18:03:54.214 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-15 18:03:54.215 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-15 18:03:54.600 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-15 18:03:55.831 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-15 18:03:55.953 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-15 18:03:55.963 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-15 18:03:56.046 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-16 03:00:00.000 +02:00, which is 08:56:03.9535986 from now.
[2025-07-15 18:03:56.152 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-16 02:00:00.000 +02:00, which is 07:56:03.8471600 from now.
[2025-07-15 18:03:56.321 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-15 18:03:56.678 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-15 18:03:56.849 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-15 18:03:56.889 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-15 18:03:56.954 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-15 18:03:57.274 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-15 18:03:57.849 +02:00] [INF] [12] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-15 18:03:59.245 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-15 18:03:59.365 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-15 18:04:02.494 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 3 seconds
[2025-07-15 18:04:20.802 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-15 18:04:20.823 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-15 18:04:20.824 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-15 18:04:20.824 +02:00] [INF] [1] Main: Startup complete 0:01:06.6762529
[2025-07-15 21:57:04.243 +02:00] [INF] [37] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has been denied (IP: "**********").
[2025-07-15 21:57:04.342 +02:00] [ERR] [37] Jellyfin.Api.Middleware.ExceptionMiddleware: Error processing request: "Invalid username or password entered". URL "POST" "/Users/<USER>".
[2025-07-15 21:57:08.512 +02:00] [INF] [42] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-07-15 21:57:08.513 +02:00] [INF] [42] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 0/0
[2025-07-15 21:57:08.515 +02:00] [INF] [42] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64
[2025-07-15 21:57:08.671 +02:00] [INF] [37] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-15 21:57:15.456 +02:00] [INF] [37] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-15 21:57:16.142 +02:00] [INF] [43] Jellyfin.Api.Controllers.DynamicHlsController: Current HLS implementation doesn't support non-keyframe breaks but one is requested, ignoring that request
[2025-07-15 21:57:16.163 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/PELIS/Kung Fu Panda 2 (2011)/Kung Fu Panda 2 (2011) Bluray-720p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 libfdk_aac -ac 2 -ab 256000 -af \"volume=2\" -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename \"fc1b5fe744b01a3986f8873273595c26-1.mp4\" -start_number 0 -hls_segment_filename \"/cache/transcodes/fc1b5fe744b01a3986f8873273595c26%d.mp4\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/fc1b5fe744b01a3986f8873273595c26.m3u8\""
[2025-07-15 21:57:18.999 +02:00] [WRN] [42] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-07-15 21:57:25.457 +02:00] [INF] [43] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/fc1b5fe744b01a3986f8873273595c26.m3u8"
[2025-07-15 21:57:26.027 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-07-15 21:57:26.029 +02:00] [INF] [43] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/fc1b5fe744b01a3986f8873273595c26.m3u8"
[2025-07-15 21:57:26.288 +02:00] [INF] [43] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-15 21:57:27.584 +02:00] [INF] [41] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Web" "10.10.7" playing "Kung Fu Panda 2". Stopped at "6568" ms
[2025-07-15 23:14:17.130 +02:00] [INF] [37] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
