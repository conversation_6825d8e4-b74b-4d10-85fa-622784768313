@echo off
echo ========================================
echo Configurando AdGuard Home como DNS
echo ========================================
echo.

echo Configurando DNS para adaptador Ethernet...
netsh interface ip set dns "Ethernet" static ************* primary
netsh interface ip add dns "Ethernet" ******* index=2

echo.
echo Limpiando cache DNS...
ipconfig /flushdns

echo.
echo ========================================
echo Configuracion completada!
echo DNS Primario: ************* (AdGuard Home)
echo DNS Secundario: ******* (Cloudflare)
echo ========================================
echo.
echo Para verificar la configuracion, ejecuta: ipconfig /all
pause
