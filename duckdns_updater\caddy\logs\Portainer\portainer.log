{"level":"info","ts":1751913515.2401388,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"56434","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Accept-Encoding":["gzip, deflate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Connection":["keep-alive"],"Cache-Control":["max-age=0"],"Upgrade-Insecure-Requests":["1"]}},"bytes_read":0,"user_id":"","duration":0.000015901,"size":0,"status":308,"resp_headers":{"Location":["https://tankeportainer.duckdns.org/"],"Content-Type":[],"Server":["Caddy"],"Connection":["close"]}}
{"level":"info","ts":1751913515.270381,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"56444","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Accept-Encoding":["gzip, deflate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Connection":["keep-alive"],"Cache-Control":["max-age=0"],"Upgrade-Insecure-Requests":["1"]}},"bytes_read":0,"user_id":"","duration":0.000017101,"size":0,"status":308,"resp_headers":{"Server":["Caddy"],"Connection":["close"],"Location":["https://tankeportainer.duckdns.org/"],"Content-Type":[]}}
{"level":"info","ts":1751913518.65666,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"35640","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/LAicC5aQX-NGPFyZG0LWnMEUDQx49lOaDGsDcsvKUGI","headers":{"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"]}},"bytes_read":0,"user_id":"","duration":0.000100855,"size":87,"status":200,"resp_headers":{"Content-Type":["text/plain"],"Server":["Caddy"]}}
{"level":"info","ts":1751913519.4608896,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"35654","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/LAicC5aQX-NGPFyZG0LWnMEUDQx49lOaDGsDcsvKUGI","headers":{"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"]}},"bytes_read":0,"user_id":"","duration":0.000049912,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
