{"level":"info","ts":1751913515.2401388,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"56434","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Accept-Encoding":["gzip, deflate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Connection":["keep-alive"],"Cache-Control":["max-age=0"],"Upgrade-Insecure-Requests":["1"]}},"bytes_read":0,"user_id":"","duration":0.000015901,"size":0,"status":308,"resp_headers":{"Location":["https://tankeportainer.duckdns.org/"],"Content-Type":[],"Server":["Caddy"],"Connection":["close"]}}
{"level":"info","ts":1751913515.270381,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"56444","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Accept-Encoding":["gzip, deflate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Connection":["keep-alive"],"Cache-Control":["max-age=0"],"Upgrade-Insecure-Requests":["1"]}},"bytes_read":0,"user_id":"","duration":0.000017101,"size":0,"status":308,"resp_headers":{"Server":["Caddy"],"Connection":["close"],"Location":["https://tankeportainer.duckdns.org/"],"Content-Type":[]}}
{"level":"info","ts":1751913518.65666,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"35640","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/LAicC5aQX-NGPFyZG0LWnMEUDQx49lOaDGsDcsvKUGI","headers":{"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"]}},"bytes_read":0,"user_id":"","duration":0.000100855,"size":87,"status":200,"resp_headers":{"Content-Type":["text/plain"],"Server":["Caddy"]}}
{"level":"info","ts":1751913519.4608896,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"35654","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/LAicC5aQX-NGPFyZG0LWnMEUDQx49lOaDGsDcsvKUGI","headers":{"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"]}},"bytes_read":0,"user_id":"","duration":0.000049912,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1751913529.3774755,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"43872","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/LAicC5aQX-NGPFyZG0LWnMEUDQx49lOaDGsDcsvKUGI","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000045822,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1751913534.3527606,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"43874","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/LAicC5aQX-NGPFyZG0LWnMEUDQx49lOaDGsDcsvKUGI","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000046652,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1751913548.993973,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"52538","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/LAicC5aQX-NGPFyZG0LWnMEUDQx49lOaDGsDcsvKUGI","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.*********,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1751913637.041823,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"37968","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/T_bCHdk9ruWT8Us-CirEZAkHV5kTfnsPYgcI6kecFSk","headers":{"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["acme.zerossl.com/v2/DV90"],"Cache-Control":["no-cache"]}},"bytes_read":0,"user_id":"","duration":0.*********,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1751913646.3871176,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"58926","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/T_bCHdk9ruWT8Us-CirEZAkHV5kTfnsPYgcI6kecFSk","headers":{"Cache-Control":["no-cache"],"User-Agent":["acme.zerossl.com/v2/DV90"],"X-Forwarded-Proto":["http"],"X-B3-Traceid":["41d298fb6646023b96ea46432ae2a81b"],"X-B3-Sampled":["0"],"Accept-Encoding":["gzip, deflate"],"X-Request-Id":["0c718e43-d081-4569-8cc5-e685123ef084"],"X-Envoy-Peer-Metadata-Id":["sidecar~**************~dcv-checker-698f9c4db5-z54vq.mpic~mpic.svc.cluster.local"],"X-Envoy-Peer-Metadata":["Ch8KDkFQUF9DT05UQUlORVJTEg0aC2Rjdi1jaGVja2VyCkQKCkNMVVNURVJfSUQSNho0Y24tc2N0LXNjbS1na2UtcC05NTY5LWV1cm9wZS13ZXN0My1zY20tZXUtcHJvZHVjdGlvbgogCgxJTlNUQU5DRV9JUFMSEBoOMTAuMjAxLjEzMC4xMzYKIAoNSVNUSU9fVkVSU0lPThIPGg0xLjIwLjgtYXNtLjMzCrwBCgZMQUJFTFMSsQEqrgEKIwoSa3ViZXJuZXRlcy5pby9uYW1lEg0aC2Rjdi1jaGVja2VyCiQKGXNlY3VyaXR5LmlzdGlvLmlvL3Rsc01vZGUSBxoFaXN0aW8KMAofc2VydmljZS5pc3Rpby5pby9jYW5vbmljYWwtbmFtZRINGgtkY3YtY2hlY2tlcgovCiNzZXJ2aWNlLmlzdGlvLmlvL2Nhbm9uaWNhbC1yZXZpc2lvbhIIGgZsYXRlc3QKHgoHTUVTSF9JRBITGhFwcm9qLTcyMjI3NTU5ODgwMgomCgROQU1FEh4aHGRjdi1jaGVja2VyLTY5OGY5YzRkYjUtejU0dnEKEwoJTkFNRVNQQUNFEgYaBG1waWMKTAoFT1dORVISQxpBa3ViZXJuZXRlczovL2FwaXMvYXBwcy92MS9uYW1lc3BhY2VzL21waWMvZGVwbG95bWVudHMvZGN2LWNoZWNrZXIKvgIKEVBMQVRGT1JNX01FVEFEQVRBEqgCKqUCCisKFGdjcF9na2VfY2x1c3Rlcl9uYW1lEhMaEXNjbS1ldS1wcm9kdWN0aW9uCooBChNnY3BfZ2tlX2NsdXN0ZXJfdXJsEnMacWh0dHBzOi8vY29udGFpbmVyLmdvb2dsZWFwaXMuY29tL3YxL3Byb2plY3RzL3NjdC1zY20tZ2tlLXAtOTU2OS9sb2NhdGlvbnMvZXVyb3BlLXdlc3QzL2NsdXN0ZXJzL3NjbS1ldS1wcm9kdWN0aW9uCh4KDGdjcF9sb2NhdGlvbhIOGgxldXJvcGUtd2VzdDMKIwoLZ2NwX3Byb2plY3QSFBoSc2N0LXNjbS1na2UtcC05NTY5CiQKEmdjcF9wcm9qZWN0X251bWJlchIOGgw3MjIyNzU1OTg4MDIKHgoNV09SS0xPQURfTkFNRRINGgtkY3YtY2hlY2tlcg=="],"Accept":["*/*"],"X-Envoy-Attempt-Count":["1"],"X-B3-Spanid":["96ea46432ae2a81b"]}},"bytes_read":0,"user_id":"","duration":0.*********,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1751913662.9727087,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"39678","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/.well-known/acme-challenge/T_bCHdk9ruWT8Us-CirEZAkHV5kTfnsPYgcI6kecFSk","headers":{"X-Request-Id":["9a3be8dc-ea13-4287-8557-b66c6ac431b4"],"X-Envoy-Peer-Metadata-Id":["sidecar~************~dcv-checker-85f6cc7d7b-qj95d.mpic~mpic.svc.cluster.local"],"Accept":["*/*"],"Accept-Encoding":["gzip, deflate"],"Cache-Control":["no-cache"],"User-Agent":["acme.zerossl.com/v2/DV90"],"X-Envoy-Attempt-Count":["1"],"X-Envoy-Peer-Metadata":["Ch8KDkFQUF9DT05UQUlORVJTEg0aC2Rjdi1jaGVja2VyClsKCkNMVVNURVJfSUQSTRpLY24tcHJqLXAtbXQtdXMtY2VudHJhbDEtOThlYS11cy1jZW50cmFsMS1tdWx0aXRlbmFudC11cy1jZW50cmFsMS1wcm9kdWN0aW9uCh4KDElOU1RBTkNFX0lQUxIOGgwxMC4yMDIuMTI5LjkKIAoNSVNUSU9fVkVSU0lPThIPGg0xLjIwLjgtYXNtLjMzCrwBCgZMQUJFTFMSsQEqrgEKIwoSa3ViZXJuZXRlcy5pby9uYW1lEg0aC2Rjdi1jaGVja2VyCiQKGXNlY3VyaXR5LmlzdGlvLmlvL3Rsc01vZGUSBxoFaXN0aW8KMAofc2VydmljZS5pc3Rpby5pby9jYW5vbmljYWwtbmFtZRINGgtkY3YtY2hlY2tlcgovCiNzZXJ2aWNlLmlzdGlvLmlvL2Nhbm9uaWNhbC1yZXZpc2lvbhIIGgZsYXRlc3QKHgoHTUVTSF9JRBITGhFwcm9qLTY5MDMxNzk0NzgxNAomCgROQU1FEh4aHGRjdi1jaGVja2VyLTg1ZjZjYzdkN2ItcWo5NWQKEwoJTkFNRVNQQUNFEgYaBG1waWMKTAoFT1dORVISQxpBa3ViZXJuZXRlczovL2FwaXMvYXBwcy92MS9uYW1lc3BhY2VzL21waWMvZGVwbG95bWVudHMvZGN2LWNoZWNrZXIK7gIKEVBMQVRGT1JNX01FVEFEQVRBEtgCKtUCCjwKFGdjcF9na2VfY2x1c3Rlcl9uYW1lEiQaIm11bHRpdGVuYW50LXVzLWNlbnRyYWwxLXByb2R1Y3Rpb24KowEKE2djcF9na2VfY2x1c3Rlcl91cmwSiwEaiAFodHRwczovL2NvbnRhaW5lci5nb29nbGVhcGlzLmNvbS92MS9wcm9qZWN0cy9wcmotcC1tdC11cy1jZW50cmFsMS05OGVhL2xvY2F0aW9ucy91cy1jZW50cmFsMS9jbHVzdGVycy9tdWx0aXRlbmFudC11cy1jZW50cmFsMS1wcm9kdWN0aW9uCh0KDGdjcF9sb2NhdGlvbhINGgt1cy1jZW50cmFsMQoqCgtnY3BfcHJvamVjdBIbGhlwcmotcC1tdC11cy1jZW50cmFsMS05OGVhCiQKEmdjcF9wcm9qZWN0X251bWJlchIOGgw1MjA2MDQ4Mjk4MzcKHgoNV09SS0xPQURfTkFNRRINGgtkY3YtY2hlY2tlcg=="],"X-Forwarded-Proto":["http"]}},"bytes_read":0,"user_id":"","duration":0.000050162,"size":87,"status":200,"resp_headers":{"Content-Type":["text/plain"],"Server":["Caddy"]}}
{"level":"info","ts":1751913722.6061857,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"34714","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"Sec-Fetch-Mode":["navigate"],"Accept-Encoding":["gzip, deflate, br"],"Connection":["Keep-Alive"],"User-Agent":["Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"],"Accept-Language":["en-US"],"Sec-Fetch-User":["?1"],"Sec-Fetch-Dest":["document"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Site":["same-origin"]}},"bytes_read":0,"user_id":"","duration":0.00001277,"size":0,"status":308,"resp_headers":{"Server":["Caddy"],"Connection":["close"],"Location":["https://tankeportainer.duckdns.org/"],"Content-Type":[]}}
{"level":"info","ts":1751913730.662053,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"44424","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"],"Accept-Encoding":["gzip, deflate, br"],"Accept-Language":["en-US"],"Connection":["Keep-Alive"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["document"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-User":["?1"],"Sec-Fetch-Mode":["navigate"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.004894337,"size":8705,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/html; charset=utf-8"],"Date":["Mon, 07 Jul 2025 18:42:10 GMT"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"X-Permitted-Cross-Domain-Policies":["none"],"Permissions-Policy":["geolocation=(), microphone=(), camera=()"],"Cache-Control":["no-cache, no-store, must-revalidate"],"Last-Modified":["Wed, 02 Jul 2025 04:03:21 GMT"],"X-Csrf-Token":[""],"Content-Encoding":["gzip"]}}
{"level":"info","ts":1751913731.3304522,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"44424","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/runtime.942fa683274b3d3c26cf.js","headers":{"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-User":["?1"],"Sec-Fetch-Dest":["document"],"Accept-Language":["en-US"],"Connection":["Keep-Alive"],"User-Agent":["Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"],"Accept-Encoding":["gzip, deflate, br"],"Accept":["*/*"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeportainer.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002306896,"size":1355,"status":200,"resp_headers":{"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Date":["Mon, 07 Jul 2025 18:42:11 GMT"],"X-Csrf-Token":[""],"X-Permitted-Cross-Domain-Policies":["none"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Cache-Control":["max-age=31536000"],"Content-Type":["text/javascript; charset=utf-8"],"Content-Length":["1355"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Last-Modified":["Wed, 02 Jul 2025 04:03:21 GMT"],"Permissions-Policy":["geolocation=(), microphone=(), camera=()"]}}
{"level":"info","ts":1751913731.5811515,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"44424","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/vendor.8dec768a1ba43dbbd246.js","headers":{"Sec-Fetch-User":["?1"],"Sec-Fetch-Dest":["document"],"Referer":["https://tankeportainer.duckdns.org/"],"Connection":["Keep-Alive"],"User-Agent":["Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"],"Accept-Language":["en-US"],"Sec-Fetch-Mode":["navigate"],"Upgrade-Insecure-Requests":["1"],"Accept":["*/*"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.105194737,"size":1211148,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Content-Encoding":["gzip"],"Via":["1.1 Caddy"],"Cache-Control":["max-age=31536000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Date":["Mon, 07 Jul 2025 18:42:11 GMT"],"Content-Type":["text/javascript; charset=utf-8"],"Last-Modified":["Wed, 02 Jul 2025 04:03:21 GMT"],"X-Permitted-Cross-Domain-Policies":["none"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"X-Content-Type-Options":["nosniff"],"X-Csrf-Token":[""],"Permissions-Policy":["geolocation=(), microphone=(), camera=()"]}}
{"level":"info","ts":1751913735.872795,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"44436","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeportainer.duckdns.org","uri":"/main.4639f6e2fc6f35858349.js","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"],"Referer":["https://tankeportainer.duckdns.org/"],"Accept-Language":["en-US"],"Sec-Fetch-Dest":["document"],"Upgrade-Insecure-Requests":["1"],"Accept":["*/*"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["navigate"],"Accept-Encoding":["gzip, deflate, br"],"Connection":["Keep-Alive"],"Sec-Fetch-User":["?1"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"","server_name":"tankeportainer.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.111933257,"size":975740,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Last-Modified":["Wed, 02 Jul 2025 04:03:21 GMT"],"X-Permitted-Cross-Domain-Policies":["none"],"Permissions-Policy":["geolocation=(), microphone=(), camera=()"],"Cache-Control":["max-age=31536000"],"Date":["Mon, 07 Jul 2025 18:42:15 GMT"],"Content-Security-Policy":["default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: *; connect-src 'self' ws: wss:; font-src 'self' data:"],"Via":["1.1 Caddy"],"Content-Encoding":["gzip"],"X-Csrf-Token":[""],"Content-Type":["text/javascript; charset=utf-8"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
