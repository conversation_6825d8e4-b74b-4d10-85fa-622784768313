<?xml version="1.0" encoding="utf-8"?>
<PluginConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <TraktUsers>
    <TraktUser>
      <AccessToken>9fd26f1026280934325d397903bc2a0b7da6c9709ac3845655d6a1667ebc032c</AccessToken>
      <RefreshToken>04d0f3fdc71e4d7426ab5dd51896735beb4482c02474cf703775edba78389516</RefreshToken>
      <LinkedMbUserId>a8d66ec8-8909-40ea-aa34-a5a7c5e10f69</LinkedMbUserId>
      <SkipUnwatchedImportFromTrakt>true</SkipUnwatchedImportFromTrakt>
      <SkipPlaybackProgressImportFromTrakt>false</SkipPlaybackProgressImportFromTrakt>
      <SkipWatchedImportFromTrakt>false</SkipWatchedImportFromTrakt>
      <PostWatchedHistory>true</PostWatchedHistory>
      <PostUnwatchedHistory>false</PostUnwatchedHistory>
      <PostSetWatched>true</PostSetWatched>
      <PostSetUnwatched>false</PostSetUnwatched>
      <ExtraLogging>false</ExtraLogging>
      <ExportMediaInfo>true</ExportMediaInfo>
      <SynchronizeCollections>true</SynchronizeCollections>
      <Scrobble>true</Scrobble>
      <LocationsExcluded />
      <AccessTokenExpiration>2025-07-23T11:41:19.0275349+02:00</AccessTokenExpiration>
      <DontRemoveItemFromTrakt>true</DontRemoveItemFromTrakt>
    </TraktUser>
  </TraktUsers>
</PluginConfiguration>