<?xml version="1.0" encoding="utf-8"?>
<PluginConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <TraktUsers>
    <TraktUser>
      <AccessToken>6b818ed9b4be28c0d113549950a87fa8b41079ce2fc53f70607e2d48bce518d0</AccessToken>
      <RefreshToken>b1fbb83e9debc41566e02238e4ba0e7a3fbf17bb672b9f16d3a71d98711ee462</RefreshToken>
      <LinkedMbUserId>d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64</LinkedMbUserId>
      <SkipUnwatchedImportFromTrakt>false</SkipUnwatchedImportFromTrakt>
      <SkipPlaybackProgressImportFromTrakt>false</SkipPlaybackProgressImportFromTrakt>
      <SkipWatchedImportFromTrakt>false</SkipWatchedImportFromTrakt>
      <PostWatchedHistory>true</PostWatchedHistory>
      <PostUnwatchedHistory>true</PostUnwatchedHistory>
      <PostSetWatched>true</PostSetWatched>
      <PostSetUnwatched>true</PostSetUnwatched>
      <ExtraLogging>false</ExtraLogging>
      <ExportMediaInfo>false</ExportMediaInfo>
      <SynchronizeCollections>true</SynchronizeCollections>
      <Scrobble>true</Scrobble>
      <LocationsExcluded />
      <AccessTokenExpiration>2025-08-04T23:51:47.9918582+02:00</AccessTokenExpiration>
      <DontRemoveItemFromTrakt>true</DontRemoveItemFromTrakt>
    </TraktUser>
  </TraktUsers>
</PluginConfiguration>