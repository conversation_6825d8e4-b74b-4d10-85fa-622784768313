{"version": 1, "minor_version": 18, "key": "core.entity_registry", "data": {"entities": [{"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.148552+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.sun_solar_rising", "hidden_by": null, "icon": null, "id": "8fe853589a705f95727acd85e2856157", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.993204+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": null, "original_name": "Salida del sol", "platform": "sun", "suggested_object_id": "sun_solar_rising", "supported_features": 0, "translation_key": "solar_rising", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-solar_rising", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.149229+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.sun_next_dawn", "hidden_by": null, "icon": null, "id": "ff59f96ac621b28e90b6db1d8f95ccd1", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.993958+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "platform": "sun", "suggested_object_id": "sun_next_dawn", "supported_features": 0, "translation_key": "next_dawn", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-next_dawn", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.149690+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.sun_next_dusk", "hidden_by": null, "icon": null, "id": "3d31bdac0063903b370098392836493a", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.994164+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Próximo <PERSON>", "platform": "sun", "suggested_object_id": "sun_next_dusk", "supported_features": 0, "translation_key": "next_dusk", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-next_dusk", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.149842+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.sun_next_midnight", "hidden_by": null, "icon": null, "id": "2f3364e9331d90b715a8bdddd3a40e15", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.994298+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "<PERSON>ró<PERSON><PERSON>", "platform": "sun", "suggested_object_id": "sun_next_midnight", "supported_features": 0, "translation_key": "next_midnight", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-next_midnight", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.149976+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.sun_next_noon", "hidden_by": null, "icon": null, "id": "d139fded79b18784592adccb3cd494c8", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.994429+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Próximo mediodía", "platform": "sun", "suggested_object_id": "sun_next_noon", "supported_features": 0, "translation_key": "next_noon", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-next_noon", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.150098+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.sun_next_rising", "hidden_by": null, "icon": null, "id": "0d5af0d7e8356ca813429ee5c1687ce2", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.994553+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Próxima salida del sol", "platform": "sun", "suggested_object_id": "sun_next_rising", "supported_features": 0, "translation_key": "next_rising", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-next_rising", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.150219+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.sun_next_setting", "hidden_by": null, "icon": null, "id": "6072979aac803cf9f72604325763de4f", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.994673+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Próxima puesta de sol", "platform": "sun", "suggested_object_id": "sun_next_setting", "supported_features": 0, "translation_key": "next_setting", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-next_setting", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.150337+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.sun_solar_elevation", "hidden_by": null, "icon": null, "id": "a7f5f0972e75afd6c28c9652f68bd592", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.994788+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": null, "original_name": "Elevación solar", "platform": "sun", "suggested_object_id": "sun_solar_elevation", "supported_features": 0, "translation_key": "solar_elevation", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-solar_elevation", "previous_unique_id": null, "unit_of_measurement": "°"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.150413+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.sun_solar_azimuth", "hidden_by": null, "icon": null, "id": "0731034eb08c4cd7c692b63deb795e1c", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.994856+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": null, "original_name": "Acimut solar", "platform": "sun", "suggested_object_id": "sun_solar_azimuth", "supported_features": 0, "translation_key": "solar_azimuth", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-solar_azimuth", "previous_unique_id": null, "unit_of_measurement": "°"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ439CPNGR69Y62QKXBA5K1", "config_subentry_id": null, "created_at": "2025-07-09T08:16:09.150478+00:00", "device_class": null, "device_id": "44603cbe9e62b1f01385df9df5093530", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.sun_solar_rising", "hidden_by": null, "icon": null, "id": "cc0540cc6a067e8ec25d0b3c0a8ae8f4", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.994924+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": null, "original_name": "Salida del sol", "platform": "sun", "suggested_object_id": "sun_solar_rising", "supported_features": 0, "translation_key": "solar_rising", "unique_id": "01JZQ439CPNGR69Y62QKXBA5K1-solar_rising", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"event_types": ["completed", "failed", "in_progress"]}, "config_entry_id": "01JZQ43CD30F1MQJ730E76GS6E", "config_subentry_id": null, "created_at": "2025-07-09T08:16:12.257771+00:00", "device_class": null, "device_id": "3e85ceddf7918ed2fc8365aad8343fa1", "disabled_by": null, "entity_category": null, "entity_id": "event.backup_automatic_backup", "hidden_by": null, "icon": null, "id": "e6127db8a55f92bf59c7184b26b12451", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.385064+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Copia de seguridad automática", "platform": "backup", "suggested_object_id": null, "supported_features": 0, "translation_key": "automatic_backup_event", "unique_id": "automatic_backup_event", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"options": ["idle", "create_backup", "blocked", "receive_backup", "restore_backup"]}, "config_entry_id": "01JZQ43CD30F1MQJ730E76GS6E", "config_subentry_id": null, "created_at": "2025-07-09T08:16:12.258236+00:00", "device_class": null, "device_id": "3e85ceddf7918ed2fc8365aad8343fa1", "disabled_by": null, "entity_category": null, "entity_id": "sensor.backup_backup_manager_state", "hidden_by": null, "icon": null, "id": "389ce950ecfcd98350263d688aa2cdee", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.385926+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "enum", "original_icon": null, "original_name": "Estado del administrador de copias de seguridad", "platform": "backup", "suggested_object_id": null, "supported_features": 0, "translation_key": "backup_manager_state", "unique_id": "backup_manager_state", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ43CD30F1MQJ730E76GS6E", "config_subentry_id": null, "created_at": "2025-07-09T08:16:12.258530+00:00", "device_class": null, "device_id": "3e85ceddf7918ed2fc8365aad8343fa1", "disabled_by": null, "entity_category": null, "entity_id": "sensor.backup_next_scheduled_automatic_backup", "hidden_by": null, "icon": null, "id": "b79b3065b9033013f71d9a3a2bea1efa", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.386113+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Próxima copia de seguridad automática programada", "platform": "backup", "suggested_object_id": null, "supported_features": 0, "translation_key": "next_scheduled_automatic_backup", "unique_id": "next_scheduled_automatic_backup", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ43CD30F1MQJ730E76GS6E", "config_subentry_id": null, "created_at": "2025-07-09T08:16:12.258741+00:00", "device_class": null, "device_id": "3e85ceddf7918ed2fc8365aad8343fa1", "disabled_by": null, "entity_category": null, "entity_id": "sensor.backup_last_successful_automatic_backup", "hidden_by": null, "icon": null, "id": "2fec74e45b88f37e9b6a69578cee1d0b", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.386349+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Última copia de seguridad automática realizada correctamente", "platform": "backup", "suggested_object_id": null, "supported_features": 0, "translation_key": "last_successful_automatic_backup", "unique_id": "last_successful_automatic_backup", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ43CD30F1MQJ730E76GS6E", "config_subentry_id": null, "created_at": "2025-07-09T08:16:12.258932+00:00", "device_class": null, "device_id": "3e85ceddf7918ed2fc8365aad8343fa1", "disabled_by": null, "entity_category": null, "entity_id": "sensor.backup_last_attempted_automatic_backup", "hidden_by": null, "icon": null, "id": "b0d0d415dfca59017d9044d6f16649f9", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:22:06.386485+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Último intento de copia de seguridad automática", "platform": "backup", "suggested_object_id": null, "supported_features": 0, "translation_key": "last_attempted_automatic_backup", "unique_id": "last_attempted_automatic_backup", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": null, "config_subentry_id": null, "created_at": "2025-07-09T08:21:37.378901+00:00", "device_class": null, "device_id": null, "disabled_by": null, "entity_category": null, "entity_id": "person.juan_jose_canete_gomez", "hidden_by": null, "icon": null, "id": "b7f100c51c40f2a1075ebdab447c6375", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:31.307633+00:00", "name": null, "options": {"conversation": {"should_expose": false}, "collection": {"hash": "dcc6031a4ce65fca04ece6d5e2235b8e"}}, "original_device_class": null, "original_icon": null, "original_name": "<PERSON>", "platform": "person", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "juan_jose_cane<PERSON>_gomez", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4EE9P032N58K5TSXZAVGK", "config_subentry_id": null, "created_at": "2025-07-09T08:22:14.666860+00:00", "device_class": null, "device_id": null, "disabled_by": null, "entity_category": null, "entity_id": "todo.lista_de_la_compra", "hidden_by": null, "icon": null, "id": "ad0e3ad5732826b8c74b6ce0526187be", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:22:14.667245+00:00", "name": null, "options": {"conversation": {"should_expose": true}}, "original_device_class": null, "original_icon": null, "original_name": "Lista de la compra", "platform": "shopping_list", "suggested_object_id": null, "supported_features": 15, "translation_key": "shopping_list", "unique_id": "01JZQ4EE9P032N58K5TSXZAVGK", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4EEFWA0DJ244J8MDE8AR1", "config_subentry_id": null, "created_at": "2025-07-09T08:22:14.795762+00:00", "device_class": null, "device_id": null, "disabled_by": null, "entity_category": null, "entity_id": "tts.google_translate_en_com", "hidden_by": null, "icon": null, "id": "5bdb19562f8e268ff45f03bcdbfdc0de", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T08:22:14.796035+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Google Translate en com", "platform": "google_translate", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "01JZQ4EEFWA0DJ244J8MDE8AR1", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4EEBV0YK27KNSVNMA8HYP", "config_subentry_id": null, "created_at": "2025-07-09T08:22:14.941154+00:00", "device_class": null, "device_id": "ce53d0d15afe4c1141d162ef8773e1cd", "disabled_by": null, "entity_category": null, "entity_id": "weather.forecast_casa", "hidden_by": null, "icon": null, "id": "97ec59dd2aa4cc0d143f10be4a368bba", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:22:14.941675+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Casa", "platform": "met", "suggested_object_id": null, "supported_features": 3, "translation_key": null, "unique_id": "home", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.912499+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_production_today", "hidden_by": null, "icon": null, "id": "afca7c1ba23819d017ea0cacef0ef46d", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.913330+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - hoy", "platform": "forecast_solar", "suggested_object_id": "energy_production_today", "supported_features": 0, "translation_key": "energy_production_today", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_energy_production_today", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.913554+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_production_today_remaining", "hidden_by": null, "icon": null, "id": "c59c469a76d296bcb530fbe287206847", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.913771+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - restante hoy", "platform": "forecast_solar", "suggested_object_id": "energy_production_today_remaining", "supported_features": 0, "translation_key": "energy_production_today_remaining", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_energy_production_today_remaining", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.913993+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_production_tomorrow", "hidden_by": null, "icon": null, "id": "05269907162b9622b3a80805dc9c959d", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.914158+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - mañana", "platform": "forecast_solar", "suggested_object_id": "energy_production_tomorrow", "supported_features": 0, "translation_key": "energy_production_tomorrow", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_energy_production_tomorrow", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.914309+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": null, "entity_category": null, "entity_id": "sensor.power_highest_peak_time_today", "hidden_by": null, "icon": null, "id": "0f64a546c221a8f7bf1795ba81a12d8f", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.914433+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Hora pico de mayor poten<PERSON> - hoy", "platform": "forecast_solar", "suggested_object_id": "power_highest_peak_time_today", "supported_features": 0, "translation_key": "power_highest_peak_time_today", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_power_highest_peak_time_today", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.914567+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": null, "entity_category": null, "entity_id": "sensor.power_highest_peak_time_tomorrow", "hidden_by": null, "icon": null, "id": "a70a35ab60bfd6b2d671ecbb5bfe59e3", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.914702+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Hora pico de mayor pot<PERSON><PERSON> - maña<PERSON>", "platform": "forecast_solar", "suggested_object_id": "power_highest_peak_time_tomorrow", "supported_features": 0, "translation_key": "power_highest_peak_time_tomorrow", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_power_highest_peak_time_tomorrow", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.914827+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": null, "entity_category": null, "entity_id": "sensor.power_production_now", "hidden_by": null, "icon": null, "id": "329c250ca0362bc426488dbbda0ff364", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.915031+00:00", "name": null, "options": {"sensor": {"suggested_display_precision": 0}, "conversation": {"should_expose": false}}, "original_device_class": "power", "original_icon": null, "original_name": "Producción de energía estimada - ahora", "platform": "forecast_solar", "suggested_object_id": "power_production_now", "supported_features": 0, "translation_key": "power_production_now", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_power_production_now", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.915229+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.power_production_next_hour", "hidden_by": null, "icon": null, "id": "e05324b0e066d9af20a33220e85d703b", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.915258+00:00", "name": null, "options": {}, "original_device_class": "power", "original_icon": null, "original_name": "Producción de energía estimada - en 1 hora", "platform": "forecast_solar", "suggested_object_id": "power_production_next_hour", "supported_features": 0, "translation_key": "power_production_next_hour", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_power_production_next_hour", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.915335+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.power_production_next_12hours", "hidden_by": null, "icon": null, "id": "ea2d3b10e0d28bc8160120979655178d", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.915359+00:00", "name": null, "options": {}, "original_device_class": "power", "original_icon": null, "original_name": "Producción de energía estimada - en 12 horas", "platform": "forecast_solar", "suggested_object_id": "power_production_next_12hours", "supported_features": 0, "translation_key": "power_production_next_12hours", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_power_production_next_12hours", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.915424+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.power_production_next_24hours", "hidden_by": null, "icon": null, "id": "2efac841dba4c8a4ceade1c456fe6e78", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.915445+00:00", "name": null, "options": {}, "original_device_class": "power", "original_icon": null, "original_name": "Producción de energía estimada - en 24 horas", "platform": "forecast_solar", "suggested_object_id": "power_production_next_24hours", "supported_features": 0, "translation_key": "power_production_next_24hours", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_power_production_next_24hours", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.915537+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_current_hour", "hidden_by": null, "icon": null, "id": "a02dc85e6604396e57d2bf87b26e9751", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.915737+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - esta hora", "platform": "forecast_solar", "suggested_object_id": "energy_current_hour", "supported_features": 0, "translation_key": "energy_current_hour", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_energy_current_hour", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4HFF5EJ386F7VTQD1PXGA", "config_subentry_id": null, "created_at": "2025-07-09T08:23:54.915906+00:00", "device_class": null, "device_id": "30b5c18e5073c6374ff61f1ecb366e5b", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_next_hour", "hidden_by": null, "icon": null, "id": "6b0dd6b3bc156ac2edeb1e0261c6218e", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:23:54.916100+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - próxima hora", "platform": "forecast_solar", "suggested_object_id": "energy_next_hour", "supported_features": 0, "translation_key": "energy_next_hour", "unique_id": "01JZQ4HFF5EJ386F7VTQD1PXGA_energy_next_hour", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.200297+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_production_today_2", "hidden_by": null, "icon": null, "id": "252ff5769a991a4bd9ab2d611be644db", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.200654+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - hoy", "platform": "forecast_solar", "suggested_object_id": "energy_production_today", "supported_features": 0, "translation_key": "energy_production_today", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_energy_production_today", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.200839+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_production_today_remaining_2", "hidden_by": null, "icon": null, "id": "5601d022eb496d9c362848f9bc9d38de", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.201114+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - restante hoy", "platform": "forecast_solar", "suggested_object_id": "energy_production_today_remaining", "supported_features": 0, "translation_key": "energy_production_today_remaining", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_energy_production_today_remaining", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.201386+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_production_tomorrow_2", "hidden_by": null, "icon": null, "id": "2ac77b88b50952e2fd638e70779f344b", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.201556+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - mañana", "platform": "forecast_solar", "suggested_object_id": "energy_production_tomorrow", "supported_features": 0, "translation_key": "energy_production_tomorrow", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_energy_production_tomorrow", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.201752+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": null, "entity_category": null, "entity_id": "sensor.power_highest_peak_time_today_2", "hidden_by": null, "icon": null, "id": "75fb33dfa5c778b8919e3e0e2f6896a2", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.201881+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Hora pico de mayor poten<PERSON> - hoy", "platform": "forecast_solar", "suggested_object_id": "power_highest_peak_time_today", "supported_features": 0, "translation_key": "power_highest_peak_time_today", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_power_highest_peak_time_today", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.202005+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": null, "entity_category": null, "entity_id": "sensor.power_highest_peak_time_tomorrow_2", "hidden_by": null, "icon": null, "id": "9e8bddc4d9e5c26db8c34033d04266c5", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.202141+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "timestamp", "original_icon": null, "original_name": "Hora pico de mayor pot<PERSON><PERSON> - maña<PERSON>", "platform": "forecast_solar", "suggested_object_id": "power_highest_peak_time_tomorrow", "supported_features": 0, "translation_key": "power_highest_peak_time_tomorrow", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_power_highest_peak_time_tomorrow", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.202322+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": null, "entity_category": null, "entity_id": "sensor.power_production_now_2", "hidden_by": null, "icon": null, "id": "a92425a7f427568230006c28b034ab9b", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.202505+00:00", "name": null, "options": {"sensor": {"suggested_display_precision": 0}, "conversation": {"should_expose": false}}, "original_device_class": "power", "original_icon": null, "original_name": "Producción de energía estimada - ahora", "platform": "forecast_solar", "suggested_object_id": "power_production_now", "supported_features": 0, "translation_key": "power_production_now", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_power_production_now", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.202702+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.power_production_next_hour_2", "hidden_by": null, "icon": null, "id": "c03903386530838f7bd25bb602f1794a", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.202756+00:00", "name": null, "options": {}, "original_device_class": "power", "original_icon": null, "original_name": "Producción de energía estimada - en 1 hora", "platform": "forecast_solar", "suggested_object_id": "power_production_next_hour", "supported_features": 0, "translation_key": "power_production_next_hour", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_power_production_next_hour", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.202837+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.power_production_next_12hours_2", "hidden_by": null, "icon": null, "id": "40f39d1d86d01967e648d8baae74c0ec", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.202864+00:00", "name": null, "options": {}, "original_device_class": "power", "original_icon": null, "original_name": "Producción de energía estimada - en 12 horas", "platform": "forecast_solar", "suggested_object_id": "power_production_next_12hours", "supported_features": 0, "translation_key": "power_production_next_12hours", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_power_production_next_12hours", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.202960+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.power_production_next_24hours_2", "hidden_by": null, "icon": null, "id": "ac9755dc97a7ccdd3c27dca2c9500f73", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.202989+00:00", "name": null, "options": {}, "original_device_class": "power", "original_icon": null, "original_name": "Producción de energía estimada - en 24 horas", "platform": "forecast_solar", "suggested_object_id": "power_production_next_24hours", "supported_features": 0, "translation_key": "power_production_next_24hours", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_power_production_next_24hours", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.203059+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_current_hour_2", "hidden_by": null, "icon": null, "id": "6df92e5d2654343f269d60d769bbd117", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.203224+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - esta hora", "platform": "forecast_solar", "suggested_object_id": "energy_current_hour", "supported_features": 0, "translation_key": "energy_current_hour", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_energy_current_hour", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQ4J6Q67936F3RMKMBQQFS3", "config_subentry_id": null, "created_at": "2025-07-09T08:24:18.203467+00:00", "device_class": null, "device_id": "06782bf6d13eda3955dd1c44125b36d7", "disabled_by": null, "entity_category": null, "entity_id": "sensor.energy_next_hour_2", "hidden_by": null, "icon": null, "id": "58bffa8893bfaab4680c45f5fe8c50f7", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T08:24:18.203657+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "kWh"}, "sensor": {"suggested_display_precision": 1}, "conversation": {"should_expose": false}}, "original_device_class": "energy", "original_icon": null, "original_name": "Producción de energía estimada - próxima hora", "platform": "forecast_solar", "suggested_object_id": "energy_next_hour", "supported_features": 0, "translation_key": "energy_next_hour", "unique_id": "01JZQ4J6Q67936F3RMKMBQQFS3_energy_next_hour", "previous_unique_id": null, "unit_of_measurement": "kWh"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQCZNSEDEPQGW72JVYRCYJH", "config_subentry_id": null, "created_at": "2025-07-09T10:51:28.017714+00:00", "device_class": null, "device_id": "a34ba6cee7167d10d1fa65ac9014fd75", "disabled_by": null, "entity_category": null, "entity_id": "calendar.epic_games_store_juegos_gratuitos", "hidden_by": null, "icon": null, "id": "145063e10a6091e6864ea9bd769d5166", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:51:28.018366+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Ju<PERSON>s gratuit<PERSON>", "platform": "epic_games_store", "suggested_object_id": null, "supported_features": 0, "translation_key": "free_games", "unique_id": "01JZQCZNSEDEPQGW72JVYRCYJH-free", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQCZNSEDEPQGW72JVYRCYJH", "config_subentry_id": null, "created_at": "2025-07-09T10:51:28.018893+00:00", "device_class": null, "device_id": "a34ba6cee7167d10d1fa65ac9014fd75", "disabled_by": null, "entity_category": null, "entity_id": "calendar.epic_games_store_juegos_con_descuento", "hidden_by": null, "icon": null, "id": "aed76527adac022e824e805a75858374", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T10:51:28.019130+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Juegos con descuento", "platform": "epic_games_store", "suggested_object_id": null, "supported_features": 0, "translation_key": "discount_games", "unique_id": "01JZQCZNSEDEPQGW72JVYRCYJH-discount", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"options": ["idle", "up_down", "seeding", "downloading"]}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.813597+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_estado", "hidden_by": null, "icon": null, "id": "a342013bb02e46a59c0d86e6a4cd2975", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.813877+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "enum", "original_icon": null, "original_name": "Estado", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "current_status", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-current_status", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"options": ["connected", "firewalled", "disconnected"]}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.814049+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_estado_de_conexion", "hidden_by": null, "icon": null, "id": "b247bf1f9bb74c11aa0a74f2bca209e1", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.814702+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "enum", "original_icon": null, "original_name": "Estado de conexión", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "connection_status", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-connection_status", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.814847+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_velocidad_de_descarga", "hidden_by": null, "icon": null, "id": "f5704cbfe532f7dbc03c363c0399a00d", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.815043+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "MB/s"}, "sensor": {"suggested_display_precision": 2}, "conversation": {"should_expose": false}}, "original_device_class": "data_rate", "original_icon": null, "original_name": "Velocidad de descarga", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "download_speed", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-download_speed", "previous_unique_id": null, "unit_of_measurement": "MB/s"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.815216+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_velocidad_de_subida", "hidden_by": null, "icon": null, "id": "60dc2b6f004b3df0097de4682d13728b", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.815403+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "MB/s"}, "sensor": {"suggested_display_precision": 2}, "conversation": {"should_expose": false}}, "original_device_class": "data_rate", "original_icon": null, "original_name": "Velocidad de subida", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "upload_speed", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-upload_speed", "previous_unique_id": null, "unit_of_measurement": "MB/s"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.815566+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.qbittorrent_limite_de_velocidad_de_descarga", "hidden_by": null, "icon": null, "id": "e3097ceaf86b74126b4d5c6029528159", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.815599+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "MB/s"}}, "original_device_class": "data_rate", "original_icon": null, "original_name": "Límite de velocidad de descarga", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "download_speed_limit", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-download_speed_limit", "previous_unique_id": null, "unit_of_measurement": "MB/s"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.815690+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.qbittorrent_limite_de_velocidad_de_subida", "hidden_by": null, "icon": null, "id": "fb4c7dd6d2df88b0df99f50874ca214c", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.815718+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "MB/s"}}, "original_device_class": "data_rate", "original_icon": null, "original_name": "Límite de velocidad de subida", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "upload_speed_limit", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-upload_speed_limit", "previous_unique_id": null, "unit_of_measurement": "MB/s"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.815793+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_descarga_a_lo_largo_del_tiempo", "hidden_by": null, "icon": null, "id": "1fa619f4eca506fbcf0f7901a09293ff", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.815979+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "TiB"}, "sensor": {"suggested_display_precision": 2}, "conversation": {"should_expose": false}}, "original_device_class": "data_size", "original_icon": null, "original_name": "Descarga a lo largo del tiempo", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "alltime_download", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-alltime_download", "previous_unique_id": null, "unit_of_measurement": "TiB"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.816143+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_subida_a_lo_largo_del_tiempo", "hidden_by": null, "icon": null, "id": "e1407e4269bf21819c7fe2d6040f9c0f", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.816304+00:00", "name": null, "options": {"sensor.private": {"suggested_unit_of_measurement": "TiB"}, "sensor": {"suggested_display_precision": 2}, "conversation": {"should_expose": false}}, "original_device_class": "data_size", "original_icon": null, "original_name": "Subida a lo largo del tiempo", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "alltime_upload", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-alltime_upload", "previous_unique_id": null, "unit_of_measurement": "TiB"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.816461+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.qbittorrent_ratio_global", "hidden_by": null, "icon": null, "id": "1ce25b7a15c811e4d9ab6aa2f290d4b1", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.816488+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": null, "original_name": "Ratio global", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "global_ratio", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-global_ratio", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.816560+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_todos_los_torrents", "hidden_by": null, "icon": null, "id": "a53c82cf8e8a54d25dbce3be58e07a77", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.816706+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Todos los torrents", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "all_torrents", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-all_torrents", "previous_unique_id": null, "unit_of_measurement": "torrents"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.816854+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_torrents_activos", "hidden_by": null, "icon": null, "id": "6ffe6994123ab3fb876d489eb41a480a", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.817003+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Torrents activos", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "active_torrents", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-active_torrents", "previous_unique_id": null, "unit_of_measurement": "torrents"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.817131+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_torrents_inactivos", "hidden_by": null, "icon": null, "id": "49b5fbff3b5774dfaea377cf32bdc143", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.817252+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Torrents inactivos", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "inactive_torrents", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-inactive_torrents", "previous_unique_id": null, "unit_of_measurement": "torrents"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.817374+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "sensor.qbittorrent_torrents_en_pausa", "hidden_by": null, "icon": null, "id": "8f8d6e013c3234c5ff5e3ae2d40b23c7", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.817486+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Torrents en pausa", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "paused_torrents", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-paused_torrents", "previous_unique_id": null, "unit_of_measurement": "torrents"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQENPZ66MK2WE0REGQ2Y8WF", "config_subentry_id": null, "created_at": "2025-07-09T11:20:58.818147+00:00", "device_class": null, "device_id": "3f9c93aae182e0133b2e25432a45b345", "disabled_by": null, "entity_category": null, "entity_id": "switch.qbittorrent_velocidad_alternativa", "hidden_by": null, "icon": null, "id": "73c3e4c52c36b97923c9432039b66f15", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:20:58.818284+00:00", "name": null, "options": {"conversation": {"should_expose": true}}, "original_device_class": null, "original_icon": "mdi:speedometer-slow", "original_name": "Velocidad alternativa", "platform": "qbittorrent", "suggested_object_id": null, "supported_features": 0, "translation_key": "alternative_speed", "unique_id": "01JZQENPZ66MK2WE0REGQ2Y8WF-alternative_speed", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQEXH59PFPR06XBREFG8JS6", "config_subentry_id": null, "created_at": "2025-07-09T11:25:15.014919+00:00", "device_class": null, "device_id": "f68aecab592546abe0ff0afc58788cc0", "disabled_by": null, "entity_category": null, "entity_id": "sensor.pc_clientes_activos", "hidden_by": null, "icon": null, "id": "f46303331c01b4f13bcd093aed207a6a", "has_entity_name": true, "labels": [], "modified_at": "2025-07-09T11:25:15.015332+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Clientes activos", "platform": "jellyfin", "suggested_object_id": null, "supported_features": 0, "translation_key": "watching", "unique_id": "5dde4890c3d64a0bab1177202c4e3645-watching", "previous_unique_id": null, "unit_of_measurement": "clients"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {}, "config_entry_id": "01JZQEXH59PFPR06XBREFG8JS6", "config_subentry_id": null, "created_at": "2025-07-09T11:25:24.658992+00:00", "device_class": null, "device_id": null, "disabled_by": null, "entity_category": null, "entity_id": "media_player.chrome", "hidden_by": null, "icon": null, "id": "5f00f8ed86f88ca381ad46f2186b3229", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T11:25:24.659606+00:00", "name": null, "options": {"conversation": {"should_expose": true}}, "original_device_class": null, "original_icon": null, "original_name": "Chrome", "platform": "jellyfin", "suggested_object_id": null, "supported_features": 152075, "translation_key": null, "unique_id": "5dde4890c3d64a0bab1177202c4e3645-c75a0a0c5044fac1934687837d9132b5", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZQEXH59PFPR06XBREFG8JS6", "config_subentry_id": null, "created_at": "2025-07-09T11:25:24.659909+00:00", "device_class": null, "device_id": null, "disabled_by": null, "entity_category": null, "entity_id": "remote.chrome", "hidden_by": null, "icon": null, "id": "ee87a41dc99ac85df7ca2107fb3db572", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T11:25:24.660104+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Chrome", "platform": "jellyfin", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "5dde4890c3d64a0bab1177202c4e3645-c75a0a0c5044fac1934687837d9132b5", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:30.837035+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "device_tracker.tablet_juanjo", "hidden_by": null, "icon": null, "id": "3d5fb39a15cca19a25d2400f025b75fb", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:33.020116+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": null, "original_icon": null, "original_name": "Tablet <PERSON>", "platform": "mobile_app", "suggested_object_id": "Tablet <PERSON>", "supported_features": 0, "translation_key": null, "unique_id": "c1245df62b25856a", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:36.279638+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_detected_activity", "hidden_by": null, "icon": null, "id": "e21a1dc26a8c6eb402723f487f107fe1", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:36.299644+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:walk", "original_name": "Tablet Juanjo Detected activity", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_detected_activity", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.051084+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_sleep_confidence", "hidden_by": null, "icon": null, "id": "a0e8688aae1868acc22ca903638352f0", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.051156+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:sleep", "original_name": "Tablet <PERSON><PERSON> confidence", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_sleep_confidence", "previous_unique_id": null, "unit_of_measurement": "%"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.135138+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_sleep_segment", "hidden_by": null, "icon": null, "id": "e5be722a4996bb7e22054bcca1720e9d", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.135222+00:00", "name": null, "options": {}, "original_device_class": "duration", "original_icon": "mdi:sleep", "original_name": "Tablet Juanjo Sleep segment", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_sleep_segment", "previous_unique_id": null, "unit_of_measurement": "ms"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.212153+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "binary_sensor.tablet_juanjo_android_auto", "hidden_by": null, "icon": null, "id": "7a9c99be68a60c507ac0e9d1ed7a7bef", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.224004+00:00", "name": null, "options": {}, "original_device_class": "connectivity", "original_icon": "mdi:car", "original_name": "Tablet Juanjo Android Auto", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_android_auto", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.290186+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_os_version", "hidden_by": null, "icon": null, "id": "abe60b63053b6462f5d08ce4394f6c11", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.290263+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:android", "original_name": "Tablet Juanjo OS version", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_android_os_version", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.320436+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_security_patch", "hidden_by": null, "icon": null, "id": "86939c409fd8eb1b082f04e180b3bf44", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.320500+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:security", "original_name": "Tablet Juanjo Security patch", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_android_os_security_patch", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.351415+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_current_version", "hidden_by": null, "icon": null, "id": "23685c2feff59087c84e54fd3e284396", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.351484+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:android", "original_name": "Tablet Juanjo Current version", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_current_version", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.368749+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_app_rx_gb", "hidden_by": null, "icon": null, "id": "a12e9498990bcb94d639b55697646b3b", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.368814+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:radio-tower", "original_name": "Tablet Juanjo App Rx GB", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_app_rx_gb", "previous_unique_id": null, "unit_of_measurement": "GB"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.447231+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_app_tx_gb", "hidden_by": null, "icon": null, "id": "084c99e71f19d7e6426da68be8118d16", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.447292+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:radio-tower", "original_name": "Tablet Juanjo App Tx GB", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_app_tx_gb", "previous_unique_id": null, "unit_of_measurement": "GB"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.492738+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_app_memory", "hidden_by": null, "icon": null, "id": "f60412da440e960ab7283433e92a51d9", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.492866+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:memory", "original_name": "Tablet <PERSON><PERSON> App memory", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_app_memory", "previous_unique_id": null, "unit_of_measurement": "GB"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.515905+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_app_inactive", "hidden_by": null, "icon": null, "id": "27181db13bcaa1903fc3daf8063b6d63", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.515965+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:timer-outline", "original_name": "Tablet <PERSON><PERSON> inactive", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_app_inactive", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.533291+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_app_standby_bucket", "hidden_by": null, "icon": null, "id": "b34442fe706c69c14a018bc224aa8eb1", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.533356+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:android", "original_name": "Tablet <PERSON><PERSON> App standby bucket", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_app_standby_bucket", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.560563+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_app_importance", "hidden_by": null, "icon": null, "id": "931bbe8c7e7780654e942bec68103a93", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.560634+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:android", "original_name": "Tablet <PERSON><PERSON> App importance", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_app_importance", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.584449+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_ringer_mode", "hidden_by": null, "icon": null, "id": "c0701ecb60683151876ddc8e85ebd517", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.584512+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:volume-high", "original_name": "Tablet <PERSON><PERSON> mode", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_audio_sensor", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.600640+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_audio_mode", "hidden_by": null, "icon": null, "id": "989088a9003aa997565d4f9355852b22", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.600698+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:volume-high", "original_name": "Tablet Juanjo Audio mode", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_audio_mode", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.621640+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "binary_sensor.tablet_juanjo_headphones", "hidden_by": null, "icon": null, "id": "70cf4a1e82f2f87de2b750d67c13d282", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.621729+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:headphones", "original_name": "Tablet Juanjo Headphones", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_headphone_state", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.644897+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "binary_sensor.tablet_juanjo_mic_muted", "hidden_by": null, "icon": null, "id": "6b7f7f735d8cb6480cf3f0141433bf61", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.644958+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:microphone-off", "original_name": "Tablet <PERSON><PERSON>", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_mic_muted", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.669772+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "binary_sensor.tablet_juanjo_speakerphone", "hidden_by": null, "icon": null, "id": "6a5690142f5973f3f92670457e8c70a7", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.669844+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:volume-high", "original_name": "Tablet Juanjo Speakerphone", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_speakerphone_state", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.690367+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "binary_sensor.tablet_juanjo_music_active", "hidden_by": null, "icon": null, "id": "1e6988fe7c4ae3672d11f3b6c2ff9fff", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.690426+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:music", "original_name": "Tablet Juanjo Music active", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_music_active", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.712689+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_volume_level_alarm", "hidden_by": null, "icon": null, "id": "6db12fa1dea71ad57e80f93ba8dc6f7c", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.712760+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:alarm", "original_name": "Tablet Juanjo Volume level alarm", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_volume_alarm", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.745530+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_volume_level_call", "hidden_by": null, "icon": null, "id": "86710820422855a6c9106d0d94593e0d", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.745593+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:phone", "original_name": "Tablet Juanjo Volume level call", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_volume_call", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.775742+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_volume_level_music", "hidden_by": null, "icon": null, "id": "d00f1db68c70ac75dd13c3f329291fe1", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.775815+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:music", "original_name": "Tablet Juanjo Volume level music", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_volume_music", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.797608+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_volume_level_ringer", "hidden_by": null, "icon": null, "id": "fcda038b639e00aecf3031b5f280091c", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.797677+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:phone-ring", "original_name": "Tablet Juanjo Volume level ringer", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_volume_ring", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.817628+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_volume_level_notification", "hidden_by": null, "icon": null, "id": "af88553857bbfc5c9b3032a9da4a0976", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.817727+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:bell-ring", "original_name": "Tablet Juanjo Volume level notification", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_volume_notification", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.858805+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_volume_level_system", "hidden_by": null, "icon": null, "id": "9814b9409db613a61ba9e2c0a1094ee6", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.858886+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:cellphone-sound", "original_name": "Tablet Juanjo Volume level system", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_volume_system", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.903774+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_volume_level_dtmf", "hidden_by": null, "icon": null, "id": "feb605af2660b2eba2f7e1035436cad8", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.903844+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:volume-high", "original_name": "Tablet Juanjo Volume level DTMF", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_volume_dtmf", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.921739+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_volume_level_accessibility", "hidden_by": null, "icon": null, "id": "14f1d5a5298eabddc7905aae2daaa4f5", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.921815+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:human", "original_name": "Tablet Juanjo Volume level accessibility", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_volume_accessibility", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.951190+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_battery_level", "hidden_by": null, "icon": null, "id": "8e3b2e1ff3f02c4515bd0e55a55af6bc", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.961326+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "battery", "original_icon": "mdi:battery-outline", "original_name": "Tablet Juanjo Battery level", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_battery_level", "previous_unique_id": null, "unit_of_measurement": "%"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.977121+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_battery_state", "hidden_by": null, "icon": null, "id": "31a2c2d74529b09769745cb0d4765508", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.977452+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "enum", "original_icon": "mdi:battery-minus", "original_name": "Tablet Juanjo Battery state", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_battery_state", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:37.991511+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_is_charging", "hidden_by": null, "icon": null, "id": "92b536096e1319f6b6cab411f6c83364", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:37.991568+00:00", "name": null, "options": {}, "original_device_class": "plug", "original_icon": "mdi:power-plug", "original_name": "Tablet <PERSON><PERSON> charging", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_is_charging", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.008266+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": null, "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_charger_type", "hidden_by": null, "icon": null, "id": "192bf0bf48a7548e93a6f6f63acee313", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.008564+00:00", "name": null, "options": {"conversation": {"should_expose": false}}, "original_device_class": "enum", "original_icon": "mdi:battery", "original_name": "Tablet Juanjo Charger type", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_charger_type", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.027082+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_battery_health", "hidden_by": null, "icon": null, "id": "ee1e7c51ba856fd17fe1607598068a84", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.027145+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:battery-heart-variant", "original_name": "Tablet Juanjo Battery health", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_battery_health", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.041389+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_battery_temperature", "hidden_by": null, "icon": null, "id": "04508ca4823d4abf05123944c87f46ee", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.041450+00:00", "name": null, "options": {}, "original_device_class": "temperature", "original_icon": "mdi:battery", "original_name": "Tablet Juanjo Battery temperature", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_battery_temperature", "previous_unique_id": null, "unit_of_measurement": "°C"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.057196+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_battery_power", "hidden_by": null, "icon": null, "id": "f93d5271cd2ceb57a8ce1a17a4dc316f", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.057257+00:00", "name": null, "options": {}, "original_device_class": "power", "original_icon": "mdi:battery-plus", "original_name": "Tablet Juanjo Battery power", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_battery_power", "previous_unique_id": null, "unit_of_measurement": "W"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.079342+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_remaining_charge_time", "hidden_by": null, "icon": null, "id": "b8a7ce54847d0af3e4db4247475183f6", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.079405+00:00", "name": null, "options": {}, "original_device_class": "duration", "original_icon": "mdi:battery-clock", "original_name": "Tablet <PERSON><PERSON> charge time", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_remaining_charge_time", "previous_unique_id": null, "unit_of_measurement": "min"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.112904+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_battery_cycle_count", "hidden_by": null, "icon": null, "id": "36bc8e8e79aae4911642af4cf5365a25", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.112968+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:battery-sync", "original_name": "Tablet Juanjo Battery cycle count", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_battery_cycles", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.126732+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_bluetooth_connection", "hidden_by": null, "icon": null, "id": "8532f02dfa8d93055572b53bfda1ac3c", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.126795+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:bluetooth", "original_name": "Tablet Juanjo Bluetooth connection", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_bluetooth_connection", "previous_unique_id": null, "unit_of_measurement": "connection(s)"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.140404+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_bluetooth_state", "hidden_by": null, "icon": null, "id": "281ac86048804bb59c497579f393ee0d", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.140463+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:bluetooth", "original_name": "Tablet Juanjo Bluetooth state", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_bluetooth_state", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.155039+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_ble_transmitter", "hidden_by": null, "icon": null, "id": "b9f41ad878b584e3c76bbaa7dc494cb3", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.155098+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:bluetooth", "original_name": "Tablet Juanjo BLE transmitter", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_ble_emitter", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.170505+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_beacon_monitor", "hidden_by": null, "icon": null, "id": "706cced283c4dfc2cbd4807a3ba66759", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.170563+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:bluetooth", "original_name": "Tablet Juanjo Beacon monitor", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_beacon_monitor", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.196125+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_car_battery", "hidden_by": null, "icon": null, "id": "b68025f2b1064808e16f0b36e517805e", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.196186+00:00", "name": null, "options": {}, "original_device_class": "battery", "original_icon": "mdi:car-battery", "original_name": "Tablet Juanjo Car battery", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_battery", "previous_unique_id": null, "unit_of_measurement": "%"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.215849+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_car_name", "hidden_by": null, "icon": null, "id": "8944b7a64e188228c7f092d2a727c41a", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.215908+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:car-info", "original_name": "Tablet Juanjo Car name", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_name", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.231210+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_car_charging_status", "hidden_by": null, "icon": null, "id": "dd7f6f9ccf50e38974f4bfc77e6dff68", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.231280+00:00", "name": null, "options": {}, "original_device_class": "plug", "original_icon": "mdi:ev-station", "original_name": "Tablet Juanjo Car charging status", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_charging_status", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.254854+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_car_ev_connector_type", "hidden_by": null, "icon": null, "id": "226a979dbc8cf769560b9cb932034ea1", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.254917+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:car-electric", "original_name": "Tablet Juanjo Car EV connector type", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_ev_connector", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.270095+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_car_fuel", "hidden_by": null, "icon": null, "id": "b87a79bc44ca4c7461b66612c39ef3d1", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.270187+00:00", "name": null, "options": {}, "original_device_class": "battery", "original_icon": "mdi:barrel", "original_name": "Tablet Juanjo Car fuel", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_fuel", "previous_unique_id": null, "unit_of_measurement": "%"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.285336+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_car_fuel_type", "hidden_by": null, "icon": null, "id": "cc0e0886e8697980e81de11c9b1bc53c", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.285396+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:gas-station", "original_name": "Tablet Juanjo Car fuel type", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_fuel_type", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.315687+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_car_odometer", "hidden_by": null, "icon": null, "id": "21ee468a5e046c272fbc3f3a7b6b1faf", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.315748+00:00", "name": null, "options": {}, "original_device_class": "distance", "original_icon": "mdi:map-marker-distance", "original_name": "Tablet <PERSON><PERSON> odometer", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_odometer", "previous_unique_id": null, "unit_of_measurement": "m"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.342733+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_car_speed", "hidden_by": null, "icon": null, "id": "ccd34e4436e4a4e7bdbf1ab944615f23", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.342794+00:00", "name": null, "options": {}, "original_device_class": "speed", "original_icon": "mdi:speedometer", "original_name": "Tablet <PERSON>jo Car speed", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_speed", "previous_unique_id": null, "unit_of_measurement": "m/s"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.371574+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_car_range_remaining", "hidden_by": null, "icon": null, "id": "cfbc2d6fcba354f3fdb90fbaac8f0159", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.371634+00:00", "name": null, "options": {}, "original_device_class": "distance", "original_icon": "mdi:map-marker-distance", "original_name": "Tablet Juanjo Car range remaining", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_car_range_remaining", "previous_unique_id": null, "unit_of_measurement": "m"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.396797+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_screen_brightness", "hidden_by": null, "icon": null, "id": "13d90782e5db07c28422f32c826ebc62", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.396864+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:brightness-6", "original_name": "Tablet Juanjo Screen brightness", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_screen_brightness", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.412340+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_screen_off_timeout", "hidden_by": null, "icon": null, "id": "bb52797a591109839b9f56bf1b6e929c", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.412429+00:00", "name": null, "options": {}, "original_device_class": "duration", "original_icon": "mdi:cellphone-off", "original_name": "Tablet <PERSON> Screen off timeout", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_screen_off_timeout", "previous_unique_id": null, "unit_of_measurement": "ms"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.442056+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_screen_orientation", "hidden_by": null, "icon": null, "id": "534280a68ad282f73a07a1e6ae3f3334", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.442120+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:screen-rotation", "original_name": "Tablet Juanjo Screen orientation", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_screen_orientation", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.473781+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_screen_rotation", "hidden_by": null, "icon": null, "id": "3c64698c9209b6ebf5383b0f2ed21922", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.473843+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:screen-rotation", "original_name": "Tablet <PERSON><PERSON> Screen rotation", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_screen_rotation", "previous_unique_id": null, "unit_of_measurement": "°"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.489079+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_do_not_disturb_sensor", "hidden_by": null, "icon": null, "id": "e3d7638fbb756e250e1e88e1fcf1c095", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.489138+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:minus-circle", "original_name": "Tablet <PERSON><PERSON> Disturb sensor", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_dnd_sensor", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.514021+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_accent_color", "hidden_by": null, "icon": null, "id": "2e0c1117a8b33fa88df887efbbcb1070", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.514082+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:palette", "original_name": "Tablet Juanjo Accent color", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_accent_color", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.540221+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "binary_sensor.tablet_juanjo_work_profile", "hidden_by": null, "icon": null, "id": "df2aa6fb3263de7001d6735e7003b9a9", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.540299+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:briefcase", "original_name": "Tablet <PERSON><PERSON> profile", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_is_work_profile", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.563733+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_geocoded_location", "hidden_by": null, "icon": null, "id": "50435add6aa55e8abafec112b06393fb", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.563793+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:map", "original_name": "Tablet Juanjo Geocoded location", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_geocoded_location", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.581268+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_active_calories_burned", "hidden_by": null, "icon": null, "id": "949475f85f54ee8b65143c3d9b156870", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.581326+00:00", "name": null, "options": {}, "original_device_class": "energy", "original_icon": "mdi:fire", "original_name": "Tablet Juanjo Active calories burned", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_active_calories_burned", "previous_unique_id": null, "unit_of_measurement": "kcal"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.606860+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_blood_glucose", "hidden_by": null, "icon": null, "id": "f2ee7274f163cb8f4c3362745fc411da", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.606933+00:00", "name": null, "options": {}, "original_device_class": "blood_glucose_concentration", "original_icon": "mdi:diabetes", "original_name": "Tablet <PERSON><PERSON> Blood glucose", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_blood_glucose", "previous_unique_id": null, "unit_of_measurement": "mg/dL"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.620462+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_body_fat", "hidden_by": null, "icon": null, "id": "8879d577b4cf26089d87f87358b15ec1", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.620524+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:scale-bathroom", "original_name": "Tablet <PERSON>jo Body fat", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_body_fat", "previous_unique_id": null, "unit_of_measurement": "%"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.639340+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_diastolic_blood_pressure", "hidden_by": null, "icon": null, "id": "c009aaac08e1a29253623bcbfb2ead63", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.639396+00:00", "name": null, "options": {}, "original_device_class": "pressure", "original_icon": "mdi:heart-pulse", "original_name": "Tablet <PERSON>jo Diastolic blood pressure", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_diastolic_blood_pressure", "previous_unique_id": null, "unit_of_measurement": "mmHg"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.654689+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_daily_distance", "hidden_by": null, "icon": null, "id": "25852bf4030e257294b6cd450c7a10f2", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.654745+00:00", "name": null, "options": {}, "original_device_class": "distance", "original_icon": "mdi:map-marker-distance", "original_name": "Tablet Juanjo Daily distance", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_distance", "previous_unique_id": null, "unit_of_measurement": "m"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.684178+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_daily_elevation_gained", "hidden_by": null, "icon": null, "id": "34b38445c25c7a9bc15425c06201da7b", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.684235+00:00", "name": null, "options": {}, "original_device_class": "distance", "original_icon": "mdi:elevation-rise", "original_name": "Tablet Juanjo Daily elevation gained", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_elevation_gained", "previous_unique_id": null, "unit_of_measurement": "m"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.696595+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_daily_floors", "hidden_by": null, "icon": null, "id": "20494c5ac29dccc33827252d52078e23", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.696656+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:stairs", "original_name": "Tablet Juanjo Daily floors", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_floors_climbed", "previous_unique_id": null, "unit_of_measurement": "floors"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.709853+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_heart_rate", "hidden_by": null, "icon": null, "id": "ca5ea33dd220ed11e51580cb1a22d24b", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.709967+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:heart-pulse", "original_name": "Tablet Juanjo Heart rate", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_heart_rate", "previous_unique_id": null, "unit_of_measurement": "bpm"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.734352+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_heart_rate_variability", "hidden_by": null, "icon": null, "id": "99f91eeec04efd7a33ccd54215a02075", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.734411+00:00", "name": null, "options": {}, "original_device_class": "duration", "original_icon": "mdi:heart-pulse", "original_name": "Tablet Juanjo Heart rate variability", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_heart_rate_variability", "previous_unique_id": null, "unit_of_measurement": "ms"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.752836+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_oxygen_saturation", "hidden_by": null, "icon": null, "id": "ae6e1fb51b11d1cca9789a7b2a383d5c", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.752900+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:sleep", "original_name": "Tablet Juanjo Oxygen saturation", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_oxygen_saturation", "previous_unique_id": null, "unit_of_measurement": "%"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.771501+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_respiratory_rate", "hidden_by": null, "icon": null, "id": "53b00b198876cef2ff271a3fedaceb4a", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.771607+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:account-voice", "original_name": "Tablet Juanjo Respiratory rate", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_respiratory_rate", "previous_unique_id": null, "unit_of_measurement": "bpm"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.786281+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_resting_heart_rate", "hidden_by": null, "icon": null, "id": "5f9691ecff9456a071f02412f5b030a1", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.786346+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:heart-pulse", "original_name": "Tablet <PERSON><PERSON> Resting heart rate", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_resting_heart_rate", "previous_unique_id": null, "unit_of_measurement": "bpm"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.806152+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_sleep_duration", "hidden_by": null, "icon": null, "id": "63d3c2be1183cb1d42a7cd6a80d06503", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.806218+00:00", "name": null, "options": {}, "original_device_class": "duration", "original_icon": "mdi:sleep", "original_name": "Tablet <PERSON>jo Sleep duration", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_sleep_duration", "previous_unique_id": null, "unit_of_measurement": "min"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.828250+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_daily_steps", "hidden_by": null, "icon": null, "id": "3e161bbd781e1cf9c122de0e17e5255b", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.828315+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:walk", "original_name": "Tablet Juanjo Daily steps", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_steps", "previous_unique_id": null, "unit_of_measurement": "steps"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.863443+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_systolic_blood_pressure", "hidden_by": null, "icon": null, "id": "9e831fdb9f6686018d9af410e431229e", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.863504+00:00", "name": null, "options": {}, "original_device_class": "pressure", "original_icon": "mdi:heart-pulse", "original_name": "Tablet <PERSON><PERSON>lic blood pressure", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_systolic_blood_pressure", "previous_unique_id": null, "unit_of_measurement": "mmHg"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.889046+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_total_calories_burned", "hidden_by": null, "icon": null, "id": "4bec7224153b09dd3090abb5a9a35c5e", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.889110+00:00", "name": null, "options": {}, "original_device_class": "energy", "original_icon": "mdi:fire", "original_name": "Tablet Juanjo Total calories burned", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_total_calories_burned", "previous_unique_id": null, "unit_of_measurement": "kcal"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.912909+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_vo2_max", "hidden_by": null, "icon": null, "id": "2edfa441a5cf852c0ed139ad3a18c5ff", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.912967+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:heart", "original_name": "Tablet Juanjo VO2 max", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_vo2_max", "previous_unique_id": null, "unit_of_measurement": "mL/kg/min"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.940079+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_weight", "hidden_by": null, "icon": null, "id": "7b6ca19e6fc5edc09953ee1426560f2b", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.940141+00:00", "name": null, "options": {}, "original_device_class": "weight", "original_icon": "mdi:scale-bathroom", "original_name": "Tablet <PERSON><PERSON>", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_health_connect_weight", "previous_unique_id": null, "unit_of_measurement": "g"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.959724+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_device_locked", "hidden_by": null, "icon": null, "id": "3d81a5c6f809508fd175cb0bee68eb61", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.959794+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:cellphone-lock", "original_name": "Tablet <PERSON><PERSON> locked", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_device_locked", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.977763+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_device_secure", "hidden_by": null, "icon": null, "id": "ce90c15dc25a8eb3e469923c42daf630", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.977832+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:cellphone-key", "original_name": "Tablet <PERSON><PERSON> secure", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_device_secure", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:38.996118+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_keyguard_locked", "hidden_by": null, "icon": null, "id": "4718eb0da85cc5f55e5771b841637506", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:38.996177+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:cellphone-lock", "original_name": "Tablet <PERSON><PERSON> Keyguard locked", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_keyguard_locked", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:39.018386+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_keyguard_secure", "hidden_by": null, "icon": null, "id": "3defe10994c7bdecccda1a7fbe4c52fa", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:39.018447+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:cellphone-key", "original_name": "Tablet <PERSON><PERSON> Keyguard secure", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_keyguard_secure", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:39.036316+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_last_used_app", "hidden_by": null, "icon": null, "id": "926fd6f4761b44205ffc3d0bd8db868a", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:39.036378+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:android", "original_name": "Tablet <PERSON><PERSON> Last used app", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_last_used_app", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:39.069488+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_last_reboot", "hidden_by": null, "icon": null, "id": "9c71796927723c99b252e79e98655daa", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:39.069547+00:00", "name": null, "options": {}, "original_device_class": "timestamp", "original_icon": "mdi:restart", "original_name": "Tablet <PERSON><PERSON> Last reboot", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_last_reboot", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:39.089121+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_last_update_trigger", "hidden_by": null, "icon": null, "id": "62db5ca3587200badb61ca2899fd3a32", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:39.089187+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:update", "original_name": "Tablet <PERSON> Last update trigger", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_last_update", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:39.115718+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_light_sensor", "hidden_by": null, "icon": null, "id": "1c7f2ab14a7567f801f467ca7640f679", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:40:39.115777+00:00", "name": null, "options": {}, "original_device_class": "illuminance", "original_icon": "mdi:brightness-5", "original_name": "Tablet Juanjo Light sensor", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_light_sensor", "previous_unique_id": null, "unit_of_measurement": "lx"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:40:39.136448+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_high_accuracy_mode", "hidden_by": null, "icon": null, "id": "87bde4f83bc93d0f8b097a658bf5e1c7", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.451751+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:crosshairs-gps", "original_name": "Tablet Juanjo High accuracy mode", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_high_accuracy_mode", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.511235+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_high_accuracy_update_interval", "hidden_by": null, "icon": null, "id": "53f742751eb8afd8ccd24821c0048973", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.511341+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:timer", "original_name": "Tablet Juanjo High accuracy update interval", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_high_accuracy_update_interval", "previous_unique_id": null, "unit_of_measurement": "seconds"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.612695+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_wi_fi_connection", "hidden_by": null, "icon": null, "id": "08438bc74c51abdb5bda40034fb169b3", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.612789+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:wifi", "original_name": "Tablet Juanjo Wi-Fi connection", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_wifi_connection", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.634946+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_wi_fi_bssid", "hidden_by": null, "icon": null, "id": "2934e871dbc6bde08a94ab4264f33ab0", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.635015+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:wifi", "original_name": "Tablet Juanjo Wi-Fi BSSID", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_wifi_bssid", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.653896+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_wi_fi_ip_address", "hidden_by": null, "icon": null, "id": "ba475672c3d95f030186b91629f8ac4d", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.653963+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:ip", "original_name": "Tablet Juanjo Wi-Fi IP address", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_wifi_ip_address", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.675899+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_wi_fi_link_speed", "hidden_by": null, "icon": null, "id": "65f619025b516d854fb08fccaaae4099", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.675973+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:wifi-strength-3", "original_name": "Tablet Juanjo Wi-Fi link speed", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_wifi_link_speed", "previous_unique_id": null, "unit_of_measurement": "Mbps"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.699964+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_wi_fi_state", "hidden_by": null, "icon": null, "id": "5cf0a98af1cba4b6af4c963b95ed2e1b", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.700032+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:wifi", "original_name": "Tablet Juanjo Wi-Fi state", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_wifi_state", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.723833+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_wi_fi_frequency", "hidden_by": null, "icon": null, "id": "5e91c02b3a4f1f1a05ab62571e80635a", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.723896+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:wifi", "original_name": "Tablet Juanjo Wi-Fi frequency", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_wifi_frequency", "previous_unique_id": null, "unit_of_measurement": "MHz"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.753356+00:00", "device_class": "signal_strength", "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_wi_fi_signal_strength", "hidden_by": null, "icon": null, "id": "4ab7e00f471024976944359e64c9b275", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.898860+00:00", "name": null, "options": {}, "original_device_class": "signal_strength", "original_icon": "mdi:wifi-strength-3", "original_name": "Tablet <PERSON>jo Wi-Fi signal strength", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_wifi_signal_strength", "previous_unique_id": null, "unit_of_measurement": "dBm"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.779332+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_public_ip_address", "hidden_by": null, "icon": null, "id": "84f24d56dab0a63e1462e74ea74a053d", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.779421+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:ip", "original_name": "Tablet Juanjo Public IP address", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_public_ip_address", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.807480+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_hotspot_state", "hidden_by": null, "icon": null, "id": "65fd39fcc647a41bf79f9ae13f8d4084", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.807565+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:access-point", "original_name": "Tablet Juanjo Hotspot state", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_hotspot_state", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.834805+00:00", "device_class": "enum", "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_network_type", "hidden_by": null, "icon": null, "id": "3bfc24c28733933e061372d482b27751", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.980249+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:network", "original_name": "Tablet Juanjo Network type", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_network_type", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.863691+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_ipv6_addresses", "hidden_by": null, "icon": null, "id": "6d170ed536270c83e201238e897ad5ed", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.863755+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:ip", "original_name": "Tablet Juanjo IPv6 addresses", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_ip6_addresses", "previous_unique_id": null, "unit_of_measurement": "address(es)"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.887426+00:00", "device_class": "timestamp", "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_next_alarm", "hidden_by": null, "icon": null, "id": "0a30eb6e5249b39f086e818b7c6315f0", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.041113+00:00", "name": null, "options": {}, "original_device_class": "timestamp", "original_icon": "mdi:alarm", "original_name": "Tablet <PERSON>jo Next alarm", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_next_alarm", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.910673+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_last_notification", "hidden_by": null, "icon": null, "id": "f6d3ccb00d8f828382be000c339a40ed", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.910766+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:bell-ring", "original_name": "Tablet <PERSON><PERSON> Last notification", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_last_notification", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.945259+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_last_removed_notification", "hidden_by": null, "icon": null, "id": "f2115409bf405c5601e192f2f4b3d237", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.945330+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:bell-ring", "original_name": "Tablet <PERSON><PERSON> removed notification", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_last_removed_notification", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.968356+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_active_notification_count", "hidden_by": null, "icon": null, "id": "79d2eb9784a2837b5fdc0f1f8b0e287e", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:13.968420+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:bell-ring", "original_name": "Tablet <PERSON><PERSON> Active notification count", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_active_notification_count", "previous_unique_id": null, "unit_of_measurement": "notifications"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:13.992216+00:00", "device_class": "enum", "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_media_session", "hidden_by": null, "icon": null, "id": "5ad6e6bb69b95bb23669edbee25404d6", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.143370+00:00", "name": null, "options": {}, "original_device_class": "enum", "original_icon": "mdi:play-circle", "original_name": "Tablet Juanjo Media session", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_media_session", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.016538+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_interactive", "hidden_by": null, "icon": null, "id": "21b44e932037257fda4f9a5a994bc0c8", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.016607+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:cellphone", "original_name": "Tablet Juanjo Interactive", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_is_interactive", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.055728+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_doze_mode", "hidden_by": null, "icon": null, "id": "b06007e7541c233c02cb40978881532f", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.055787+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:sleep", "original_name": "Tablet <PERSON><PERSON> mode", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_is_idle", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.079412+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "binary_sensor.tablet_juanjo_power_save", "hidden_by": null, "icon": null, "id": "c64595c30c8bd832041ca6c5311b7851", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.079485+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:battery-plus", "original_name": "Tablet <PERSON><PERSON> Power save", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_power_save", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.108159+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": null, "entity_id": "sensor.tablet_juanjo_steps_sensor", "hidden_by": null, "icon": null, "id": "afabe5e706eb1b272fdff866b3fb3366", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.108220+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:walk", "original_name": "Tablet <PERSON> sensor", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_steps_sensor", "previous_unique_id": null, "unit_of_measurement": "steps"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.131523+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_internal_storage", "hidden_by": null, "icon": null, "id": "8aff918dabcbaa16adcc1c22f1dafe62", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.131589+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:harddisk", "original_name": "Tablet Juanjo Internal storage", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_storage_sensor", "previous_unique_id": null, "unit_of_measurement": "%"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "measurement"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.162215+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_external_storage", "hidden_by": null, "icon": null, "id": "e8b7c9d2fe1b52da7777f1c78589cde7", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.162291+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:micro-sd", "original_name": "Tablet Juanjo External storage", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_external_storage", "previous_unique_id": null, "unit_of_measurement": "%"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": null, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.184994+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_current_time_zone", "hidden_by": null, "icon": null, "id": "7900f54860705f19b8854260f31b795e", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.185085+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:map-clock", "original_name": "Tablet Juan<PERSON> Current time zone", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_current_time_zone", "previous_unique_id": null, "unit_of_measurement": null}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.214464+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_total_rx_gb", "hidden_by": null, "icon": null, "id": "78a3b13b19d005f13a8731144b30bc5e", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.214527+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:radio-tower", "original_name": "Tablet Juanjo Total Rx GB", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_total_rx_gb", "previous_unique_id": null, "unit_of_measurement": "GB"}, {"aliases": [], "area_id": null, "categories": {}, "capabilities": {"state_class": "total_increasing"}, "config_entry_id": "01JZR7THX0P72CTZ92TTJG69EF", "config_subentry_id": null, "created_at": "2025-07-09T18:41:14.240063+00:00", "device_class": null, "device_id": "d3a15388aa2d7aeacc2eb6ae4df1aca9", "disabled_by": "integration", "entity_category": "diagnostic", "entity_id": "sensor.tablet_juanjo_total_tx_gb", "hidden_by": null, "icon": null, "id": "aed54cf17056155a64a51162bbacb8e3", "has_entity_name": false, "labels": [], "modified_at": "2025-07-09T18:41:14.240127+00:00", "name": null, "options": {}, "original_device_class": null, "original_icon": "mdi:radio-tower", "original_name": "Tablet Juanjo Total Tx GB", "platform": "mobile_app", "suggested_object_id": null, "supported_features": 0, "translation_key": null, "unique_id": "47b24827b8c4fdc394a0093afd5198a680a3e64e0ebc82ed68c94b31be8c7061_total_tx_gb", "previous_unique_id": null, "unit_of_measurement": "GB"}], "deleted_entities": []}}