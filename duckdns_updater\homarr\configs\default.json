{"schemaVersion": 2, "configProperties": {"name": "default"}, "categories": [], "wrappers": [{"id": "default", "position": 0}], "apps": [{"id": "5df743d9-5cb1-457c-85d2-64ff86855652", "name": "Documentation", "url": "https://homarr.dev", "behaviour": {"onClickUrl": "https://homarr.dev", "externalUrl": "https://homarr.dev", "isOpeningNewTab": true}, "network": {"enabledStatusChecker": false, "statusCodes": ["200"]}, "appearance": {"iconUrl": "/imgs/logo/logo.png", "appNameStatus": "normal", "positionAppName": "column", "lineClampAppName": 1}, "integration": {"type": null, "properties": []}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"md": {"location": {"x": 5, "y": 1}, "size": {"width": 1, "height": 1}}, "sm": {"location": {"x": 0, "y": 1}, "size": {"width": 1, "height": 2}}, "lg": {"location": {"x": 6, "y": 1}, "size": {"width": 2, "height": 2}}}}, {"id": "47af36c0-47c1-4e5b-bfc7-ad645ee6a337", "name": "Discord", "url": "https://discord.com/invite/aCsmEV5RgA", "behaviour": {"onClickUrl": "https://discord.com/invite/aCsmEV5RgA", "isOpeningNewTab": true, "externalUrl": "https://discord.com/invite/aCsmEV5RgA", "tooltipDescription": "Join our Discord server! We're waiting for your ideas and feedback. "}, "network": {"enabledStatusChecker": false, "statusCodes": ["200"]}, "appearance": {"iconUrl": "https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons@master/png/discord.png", "appNameStatus": "normal", "positionAppName": "row-reverse", "lineClampAppName": 1}, "integration": {"type": null, "properties": []}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"md": {"location": {"x": 3, "y": 1}, "size": {"width": 1, "height": 1}}, "sm": {"location": {"x": 1, "y": 4}, "size": {"width": 1, "height": 1}}, "lg": {"location": {"x": 4, "y": 0}, "size": {"width": 2, "height": 1}}}}, {"id": "47af36c0-47c1-4e5b-bfc7-ad645ee6a330", "name": "Contribute", "url": "https://github.com/ajnart/homarr", "behaviour": {"onClickUrl": "https://github.com/ajnart/homarr", "externalUrl": "https://github.com/ajnart/homarr", "isOpeningNewTab": true, "tooltipDescription": ""}, "network": {"enabledStatusChecker": false, "statusCodes": []}, "appearance": {"iconUrl": "https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons@master/png/github.png", "appNameStatus": "normal", "positionAppName": "row-reverse", "lineClampAppName": 2}, "integration": {"type": null, "properties": []}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"md": {"location": {"x": 3, "y": 2}, "size": {"width": 2, "height": 1}}, "sm": {"location": {"x": 1, "y": 3}, "size": {"width": 2, "height": 1}}, "lg": {"location": {"x": 2, "y": 0}, "size": {"width": 2, "height": 1}}}}, {"id": "47af36c0-47c1-4e5b-bfc7-ad645ee6a990", "name": "Donate", "url": "https://ko-fi.com/ajnart", "behaviour": {"onClickUrl": "https://ko-fi.com/ajnart", "externalUrl": "https://ko-fi.com/ajnart", "isOpeningNewTab": true, "tooltipDescription": "Please consider making a donation"}, "network": {"enabledStatusChecker": false, "statusCodes": ["200"]}, "appearance": {"iconUrl": "https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons@master/png/ko-fi.png", "appNameStatus": "normal", "positionAppName": "row-reverse", "lineClampAppName": 1}, "integration": {"type": null, "properties": []}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"md": {"location": {"x": 4, "y": 1}, "size": {"width": 1, "height": 1}}, "sm": {"location": {"x": 2, "y": 4}, "size": {"width": 1, "height": 1}}, "lg": {"location": {"x": 6, "y": 0}, "size": {"width": 2, "height": 1}}}}], "widgets": [{"id": "e3004052-6b83-480e-b458-56e8ccdca5f0", "type": "weather", "properties": {"displayInFahrenheit": false, "location": {"name": "Paris", "latitude": 48.85341, "longitude": 2.3488}, "displayCityName": true}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"md": {"location": {"x": 5, "y": 0}, "size": {"width": 1, "height": 1}}, "sm": {"location": {"x": 2, "y": 0}, "size": {"width": 1, "height": 1}}, "lg": {"location": {"x": 0, "y": 0}, "size": {"width": 2, "height": 1}}}}, {"id": "971aa859-8570-49a1-8d34-dd5c7b3638d1", "type": "date", "properties": {"display24HourFormat": true, "dateFormat": "hide", "enableTimezone": false, "timezoneLocation": {"name": "Paris", "latitude": 48.85341, "longitude": 2.3488}, "titleState": "city"}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"sm": {"location": {"x": 1, "y": 0}, "size": {"width": 1, "height": 1}}, "md": {"location": {"x": 4, "y": 0}, "size": {"width": 1, "height": 1}}, "lg": {"location": {"x": 8, "y": 0}, "size": {"width": 2, "height": 1}}}}, {"id": "f252768d-9e69-491b-b6b4-8cad04fa30e8", "type": "date", "properties": {"display24HourFormat": true, "dateFormat": "hide", "enableTimezone": true, "timezoneLocation": {"name": "Tokyo", "latitude": 35.6895, "longitude": 139.69171}, "titleState": "city"}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"sm": {"location": {"x": 0, "y": 0}, "size": {"width": 1, "height": 1}}, "md": {"location": {"x": 3, "y": 0}, "size": {"width": 1, "height": 1}}, "lg": {"location": {"x": 8, "y": 1}, "size": {"width": 2, "height": 1}}}}, {"id": "86b1921f-efa7-410f-92dd-79553bf3264d", "type": "notebook", "properties": {"showToolbar": true, "content": "<h2><strong>Welcome to <PERSON><PERSON><PERSON> 🚀👋</strong></h2><p>We're glad that you're here! <PERSON><PERSON><PERSON> is a <em>modern </em>and <em>easy to use</em> dashboard that helps you to <strong>organize and manage</strong> your home network from one place. Control is <strong>at your fingertips</strong>.</p><p>We recommend you to read the <a target=\"_blank\" rel=\"noopener noreferrer nofollow\" href=\"https://homarr.dev/docs/getting-started/after-the-installation\">getting started guide</a> first. To edit this board you must enter the edit mode - only administrators can do this. Adding an app is the first step you should take. You can do this by clicking the <code>Add tile</code> button at the top right and select <code>App</code>. After you provided an internal URL, external URL and selected an icon you can drag it around when holding down the left mouse button. Make it bigger or smaller using the drag icon at the bottom right. When you're happy with it's position, you <strong>must exit edit mode to save your board</strong>. Adding widgets works the same way but may require additional configuration - read the documentation for more information.</p><p>To remove this widget, you must log in to your administrator account and click on the menu to delete it.</p><p><strong><u>Your TODO list:</u></strong></p><ul data-type=\"taskList\"><li data-checked=\"false\" data-type=\"taskItem\"><label><input type=\"checkbox\"><span></span></label><div><p>Read the <a target=\"_blank\" rel=\"noopener noreferrer nofollow\" href=\"https://homarr.dev\">documentation</a></p></div></li><li data-checked=\"false\" data-type=\"taskItem\"><label><input type=\"checkbox\"><span></span></label><div><p>Add your <em>first app</em></p></div></li><li data-checked=\"false\" data-type=\"taskItem\"><label><input type=\"checkbox\"><span></span></label><div><p><em>Resize </em>and <em>drag</em> your app to a different position</p></div></li><li data-checked=\"false\" data-type=\"taskItem\"><label><input type=\"checkbox\"><span></span></label><div><p>Add the <em>clock widget</em> to your dashboard</p></div></li><li data-checked=\"false\" data-type=\"taskItem\"><label><input type=\"checkbox\"><span></span></label><div><p>Create a <em>new user</em></p></div></li></ul>"}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"sm": {"location": {"x": 0, "y": 0}, "size": {"width": 3, "height": 2}}, "md": {"location": {"x": 0, "y": 0}, "size": {"width": 3, "height": 4}}, "lg": {"location": {"x": 0, "y": 1}, "size": {"width": 6, "height": 3}}}}], "settings": {"common": {"searchEngine": {"type": "google", "properties": {}}}, "customization": {"layout": {"enabledLeftSidebar": false, "enabledRightSidebar": false, "enabledDocker": false, "enabledPing": false, "enabledSearchbar": true}, "pageTitle": "<PERSON><PERSON><PERSON> ⭐️", "logoImageUrl": "/imgs/logo/logo.png", "faviconUrl": "/imgs/favicon/favicon-squared.png", "backgroundImageUrl": "", "customCss": "", "colors": {"primary": "red", "secondary": "yellow", "shade": 7}, "appOpacity": 100, "gridstack": {"columnCountSmall": 3, "columnCountMedium": 6, "columnCountLarge": 10}}, "access": {"allowGuests": false}}}