{"schemaVersion": 2, "configProperties": {"name": "default"}, "categories": [], "wrappers": [{"id": "default", "position": 0}], "apps": [{"id": "07f853fa-4da2-40d3-aeaf-5b71ce336e89", "name": "Sonarr", "url": "https://sonarr", "appearance": {"iconUrl": "https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons/svg/sonarr-4k.svg", "appNameStatus": "normal", "positionAppName": "column", "lineClampAppName": 1, "appNameFontSize": 16}, "network": {"enabledStatusChecker": true, "statusCodes": ["200", "301", "302", "304", "307", "308"]}, "behaviour": {"isOpeningNewTab": true, "externalUrl": "https://tankesonarr.duckdns.org/", "tooltipDescription": "Buscador de series"}, "area": {"type": "sidebar", "properties": {"location": "left"}}, "shape": {"lg": {"location": {"x": 0, "y": 2}, "size": {"width": 1, "height": 1}}}, "integration": {"type": "sonarr", "properties": [{"field": "<PERSON><PERSON><PERSON><PERSON>", "type": "private", "value": "2f9da07e98744f4890c0960d15ead111"}]}}, {"id": "d68a0e34-d3e8-431d-879e-177108a1b587", "name": "QBittorrent", "url": "https://qbittorrent", "appearance": {"iconUrl": "https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons/svg/qbittorrent.svg", "appNameStatus": "normal", "positionAppName": "column", "lineClampAppName": 1, "appNameFontSize": 16}, "network": {"enabledStatusChecker": true, "statusCodes": ["200", "301", "302", "304", "307", "308"]}, "behaviour": {"isOpeningNewTab": true, "externalUrl": "https://tanketorrent.duckdns.org/", "tooltipDescription": "Descargador de cosas"}, "area": {"type": "sidebar", "properties": {"location": "left"}}, "shape": {"lg": {"location": {"x": 1, "y": 2}, "size": {"width": 1, "height": 1}}}, "integration": {"type": "qBittorrent", "properties": [{"field": "username", "type": "public", "value": "Tankeeee2_GAMES"}, {"field": "password", "type": "private", "value": "Ahmadmuhsin4148!"}]}}, {"id": "f02d8581-ed5e-40ed-a89d-4156146b29e9", "name": "<PERSON><PERSON>", "url": "http://radarr", "appearance": {"iconUrl": "https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons/svg/radarr-4k.svg", "appNameStatus": "normal", "positionAppName": "column", "lineClampAppName": 1, "appNameFontSize": 16}, "network": {"enabledStatusChecker": true, "statusCodes": ["200", "301", "302", "304", "307", "308"]}, "behaviour": {"isOpeningNewTab": true, "externalUrl": "https://tankeradarr.duckdns.org", "tooltipDescription": "Buscador de peliculas"}, "area": {"type": "sidebar", "properties": {"location": "left"}}, "shape": {"lg": {"location": {"x": 0, "y": 1}, "size": {"width": 1, "height": 1}}}, "integration": {"type": "radarr", "properties": [{"field": "<PERSON><PERSON><PERSON><PERSON>", "type": "private", "value": "cfc0cde90b0f483eb4190dc634ca86f2"}]}}], "widgets": [{"id": "e3004052-6b83-480e-b458-56e8ccdca5f0", "type": "weather", "properties": {"displayInFahrenheit": false, "location": {"name": "Villafranca de Córdoba", "latitude": 37.96257, "longitude": -4.54547}, "displayCityName": true, "displayWeekly": false, "forecastDays": 5}, "area": {"type": "sidebar", "properties": {"location": "left"}}, "shape": {"md": {"location": {"x": 5, "y": 0}, "size": {"width": 1, "height": 1}}, "sm": {"location": {"x": 2, "y": 0}, "size": {"width": 1, "height": 1}}, "lg": {"location": {"x": 0, "y": 0}, "size": {"width": 2, "height": 1}}}}, {"id": "971aa859-8570-49a1-8d34-dd5c7b3638d1", "type": "date", "properties": {"display24HourFormat": true, "dateFormat": "hide", "enableTimezone": false, "timezoneLocation": {"name": "Paris", "latitude": 48.85341, "longitude": 2.3488}, "titleState": "city"}, "area": {"type": "sidebar", "properties": {"location": "left"}}, "shape": {"sm": {"location": {"x": 1, "y": 0}, "size": {"width": 1, "height": 1}}, "md": {"location": {"x": 4, "y": 0}, "size": {"width": 1, "height": 1}}, "lg": {"location": {"x": 1, "y": 1}, "size": {"width": 1, "height": 1}}}}, {"id": "1cf8a203-b1b3-41c4-87d1-ef1e06106a00", "type": "torrents-status", "properties": {"displayCompletedTorrents": true, "displayActiveTorrents": true, "speedLimitOfActiveTorrents": 10, "displayStaleTorrents": true, "labelFilterIsWhitelist": true, "labelFilter": [], "displayRatioWithFilter": true, "columnOrdering": true, "rowSorting": true, "columns": ["up", "down", "eta", "progress"], "nameColumnSize": 2}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"sm": {"location": {"x": 0, "y": 0}, "size": {"width": 2, "height": 2}}, "md": {"location": {"x": 0, "y": 0}, "size": {"width": 2, "height": 2}}, "lg": {"location": {"x": 0, "y": 0}, "size": {"width": 2, "height": 2}}}}, {"id": "2f2d7f29-d268-496c-91eb-bf2b2609edf0", "type": "media-transcoding", "properties": {"defaultView": "workers", "showHealthCheck": true, "showHealthChecksInQueue": true, "queuePageSize": 10, "showAppIcon": true}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"sm": {"location": {"x": 0, "y": 0}, "size": {"width": 3, "height": 2}}, "md": {"location": {"x": 0, "y": 0}, "size": {"width": 3, "height": 2}}, "lg": {"location": {"x": 0, "y": 2}, "size": {"width": 3, "height": 2}}}}, {"id": "8ea8b64d-cde1-4f9a-8b0a-fbd895c31e69", "type": "media-server", "properties": {}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"sm": {"location": {"x": 0, "y": 0}, "size": {"width": 3, "height": 2}}, "md": {"location": {"x": 0, "y": 0}, "size": {"width": 3, "height": 2}}, "lg": {"location": {"x": 3, "y": 0}, "size": {"width": 3, "height": 2}}}}, {"id": "edd2f34e-61e7-43da-a052-0b101586ac11", "type": "health-monitoring", "properties": {"fahrenheit": false, "cpu": true, "memory": true, "fileSystem": true, "defaultTabState": "system", "node": "", "defaultViewState": "none", "summary": true, "showNode": true, "showVM": true, "showLXCs": true, "showStorage": true, "sectionIndicatorColor": "all", "ignoreCert": true}, "area": {"type": "wrapper", "properties": {"id": "default"}}, "shape": {"sm": {"location": {"x": 0, "y": 0}, "size": {"width": 2, "height": 2}}, "md": {"location": {"x": 0, "y": 0}, "size": {"width": 2, "height": 2}}, "lg": {"location": {"x": 3, "y": 2}, "size": {"width": 2, "height": 2}}}}], "settings": {"common": {"searchEngine": {"type": "google", "properties": {}}}, "customization": {"layout": {"enabledLeftSidebar": true, "enabledRightSidebar": false, "enabledDocker": false, "enabledPing": true, "enabledSearchbar": true}, "pageTitle": "<PERSON><PERSON><PERSON> ⭐️", "logoImageUrl": "/imgs/logo/logo.png", "faviconUrl": "/imgs/favicon/favicon-squared.png", "backgroundImageUrl": "", "customCss": "", "colors": {"primary": "red", "secondary": "yellow", "shade": 7}, "appOpacity": 100, "gridstack": {"columnCountSmall": 3, "columnCountMedium": 6, "columnCountLarge": 10}, "backgroundImageAttachment": "fixed", "backgroundImageRepeat": "no-repeat", "backgroundImageSize": "cover", "metaTitle": ""}, "access": {"allowGuests": false}}}