2025-07-17 13:24:01.0|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnR3VLdU43MlR4a0RrRW9QVWxSSWRJNVZTZXh2ZWo1ZzJYOFB2X3p6VWt1dW54WV9UbnZNZEdLNURVX3BSOWp5VEJYUXc3V1VsVmgxV2c4NnhydHQ2SjJIVzl5VTZySlc1MldsX2xaLTk3REI5ekR4ZFo2dFhnY2lKVGNqVl9MblA2cGhkWmJtSGRoeC1jYkR2Y1c4RXdlZUx2dHVuUmNIdmlIeFRvWlJaSmdIbFhkWmF1OTdrWUxzWUliTlFBWXdv&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnR3VLdU43MlR4a0RrRW9QVWxSSWRJNVZTZXh2ZWo1ZzJYOFB2X3p6VWt1dW54WV9UbnZNZEdLNURVX3BSOWp5VEJYUXc3V1VsVmgxV2c4NnhydHQ2SjJIVzl5VTZySlc1MldsX2xaLTk3REI5ekR4ZFo2dFhnY2lKVGNqVl9MblA2cGhkWmJtSGRoeC1jYkR2Y1c4RXdlZUx2dHVuUmNIdmlIeFRvWlJaSmdIbFhkWmF1OTdrWUxzWUliTlFBWXdv&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 13:24:01.2|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 13:24:01.2|Info|RssSyncService|RSS Sync Completed. Reports found: 889, Reports grabbed: 0
2025-07-17 13:39:30.3|Info|RssSyncService|Starting RSS Sync
2025-07-17 13:39:30.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 11:23:00 and 07/17/2025 11:23:00 UTC. Search may be required.
2025-07-17 13:39:30.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 11:23:00 and 07/17/2025 11:23:00 UTC. Search may be required.
2025-07-17 13:39:32.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 11:23:04 and 07/17/2025 11:23:04 UTC. Search may be required.
2025-07-17 13:39:32.4|Info|DownloadDecisionMaker|Processing 889 releases
2025-07-17 13:40:31.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnQmZxXzNNcFBYbXplaS1PbWRvbEluWnNzYnVzQXNHQlZDUWI3YV9fTGV4LV8wMHBZdU12b0hLOE5XNG5wbGVpb0c2Q2o4TzNwMTY3anFTdG5RN0MxaHN5MEp2R0lraG80VkdKanNQNS1oeUduWHV6RkZyOXJYdFVaYnRLZ3NmS0oydl9pa2lHTkFZSjl5WUZSdDRVcDRQYkMzQlJ3eEhSYnA1dFdzWUp5ODl1R2VaWWhkRk1YUEpKdGZuVlVrV01r&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 13:40:31.0|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnQmZxXzNNcFBYbXplaS1PbWRvbEluWnNzYnVzQXNHQlZDUWI3YV9fTGV4LV8wMHBZdU12b0hLOE5XNG5wbGVpb0c2Q2o4TzNwMTY3anFTdG5RN0MxaHN5MEp2R0lraG80VkdKanNQNS1oeUduWHV6RkZyOXJYdFVaYnRLZ3NmS0oydl9pa2lHTkFZSjl5WUZSdDRVcDRQYkMzQlJ3eEhSYnA1dFdzWUp5ODl1R2VaWWhkRk1YUEpKdGZuVlVrV01r&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnQmZxXzNNcFBYbXplaS1PbWRvbEluWnNzYnVzQXNHQlZDUWI3YV9fTGV4LV8wMHBZdU12b0hLOE5XNG5wbGVpb0c2Q2o4TzNwMTY3anFTdG5RN0MxaHN5MEp2R0lraG80VkdKanNQNS1oeUduWHV6RkZyOXJYdFVaYnRLZ3NmS0oydl9pa2lHTkFZSjl5WUZSdDRVcDRQYkMzQlJ3eEhSYnA1dFdzWUp5ODl1R2VaWWhkRk1YUEpKdGZuVlVrV01r&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 13:40:31.1|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 13:40:32.9|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqTVhTZ2dEN1IyUnRnM3pJTURwQ1Fva003eTlCNjdvU3BXeERuSHc5OFBvVEgyRldmWVplbG1VVkhQWm13OXBNSFVaYWIta0x4VjhWQ09fUEdtb1BPUkY2WUR0OHZxTnhZZE1iYmlpenpGd0RuYzc4b3BPSGhSLTd3Wlh4d0s5YTlrUzZRdTVIS0lVYWhDM3BzNDZXMk5qbmZWV0NMS3VHTF9sb0VVYjc1X0NoWmxBRHh4TzN6MUZuN3p3TG9SNzNj&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 13:40:32.9|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqTVhTZ2dEN1IyUnRnM3pJTURwQ1Fva003eTlCNjdvU3BXeERuSHc5OFBvVEgyRldmWVplbG1VVkhQWm13OXBNSFVaYWIta0x4VjhWQ09fUEdtb1BPUkY2WUR0OHZxTnhZZE1iYmlpenpGd0RuYzc4b3BPSGhSLTd3Wlh4d0s5YTlrUzZRdTVIS0lVYWhDM3BzNDZXMk5qbmZWV0NMS3VHTF9sb0VVYjc1X0NoWmxBRHh4TzN6MUZuN3p3TG9SNzNj&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqTVhTZ2dEN1IyUnRnM3pJTURwQ1Fva003eTlCNjdvU3BXeERuSHc5OFBvVEgyRldmWVplbG1VVkhQWm13OXBNSFVaYWIta0x4VjhWQ09fUEdtb1BPUkY2WUR0OHZxTnhZZE1iYmlpenpGd0RuYzc4b3BPSGhSLTd3Wlh4d0s5YTlrUzZRdTVIS0lVYWhDM3BzNDZXMk5qbmZWV0NMS3VHTF9sb0VVYjc1X0NoWmxBRHh4TzN6MUZuN3p3TG9SNzNj&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 13:40:33.0|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 13:40:33.0|Info|RssSyncService|RSS Sync Completed. Reports found: 889, Reports grabbed: 0
2025-07-17 13:56:00.5|Info|RssSyncService|Starting RSS Sync
2025-07-17 13:56:00.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 11:23:00 and 07/17/2025 11:23:00 UTC. Search may be required.
2025-07-17 13:56:01.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 11:23:00 and 07/17/2025 11:23:00 UTC. Search may be required.
2025-07-17 13:56:02.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 11:23:04 and 07/17/2025 11:23:04 UTC. Search may be required.
2025-07-17 13:56:02.7|Info|DownloadDecisionMaker|Processing 889 releases
2025-07-17 13:56:59.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loUkktakJVa1VXRjU1WXdfX1ZTV3lqbzlUYzZtcTRETHJkLTJnRzBYSVQzbXFCaFI2VHlxeVc5S2tEbk91WjVsemxuVWxyMXNiUmR6TTZkYmIzbzc0a3k5T0N3SUM4MmRBeVVxUlZqMURsOGdNbmFOR191azhrRmNfcWFueGloUk1ZY3hXRTZFaFVEWUJuY0ttbUpMeVVRRVBpbXpsZWhxSzh4Um1WcUVUbzVuNWxQUm1qRTdvcDlRQ1E3bm1HcUZV&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 13:56:59.2|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loUkktakJVa1VXRjU1WXdfX1ZTV3lqbzlUYzZtcTRETHJkLTJnRzBYSVQzbXFCaFI2VHlxeVc5S2tEbk91WjVsemxuVWxyMXNiUmR6TTZkYmIzbzc0a3k5T0N3SUM4MmRBeVVxUlZqMURsOGdNbmFOR191azhrRmNfcWFueGloUk1ZY3hXRTZFaFVEWUJuY0ttbUpMeVVRRVBpbXpsZWhxSzh4Um1WcUVUbzVuNWxQUm1qRTdvcDlRQ1E3bm1HcUZV&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loUkktakJVa1VXRjU1WXdfX1ZTV3lqbzlUYzZtcTRETHJkLTJnRzBYSVQzbXFCaFI2VHlxeVc5S2tEbk91WjVsemxuVWxyMXNiUmR6TTZkYmIzbzc0a3k5T0N3SUM4MmRBeVVxUlZqMURsOGdNbmFOR191azhrRmNfcWFueGloUk1ZY3hXRTZFaFVEWUJuY0ttbUpMeVVRRVBpbXpsZWhxSzh4Um1WcUVUbzVuNWxQUm1qRTdvcDlRQ1E3bm1HcUZV&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 13:56:59.2|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 13:57:01.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnQk9Fd2Z6eC1nNWVURldFaTZYUzE3TVgtTnlUdkhYblhqczg0UzhHODVyUDdfNnYzTFZhS1BWbmY3QkhZZ3RoNHAxaFpaWUJtVE5GVndTV0dTMDdBZlFrMXdGaUJWNk83UHQteDhhYndUUzhJZUxiTnBYR2JTeGczSTRVMmtrRTFlYklqSVE3TkIwb0lRYllDQ2lBMVFwNHBVYWNBS0sxc25XVGtyd2JacVlmRl9YZ2Q5SUtaVHhhamRTZHpfRUhR&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 13:57:01.2|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnQk9Fd2Z6eC1nNWVURldFaTZYUzE3TVgtTnlUdkhYblhqczg0UzhHODVyUDdfNnYzTFZhS1BWbmY3QkhZZ3RoNHAxaFpaWUJtVE5GVndTV0dTMDdBZlFrMXdGaUJWNk83UHQteDhhYndUUzhJZUxiTnBYR2JTeGczSTRVMmtrRTFlYklqSVE3TkIwb0lRYllDQ2lBMVFwNHBVYWNBS0sxc25XVGtyd2JacVlmRl9YZ2Q5SUtaVHhhamRTZHpfRUhR&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnQk9Fd2Z6eC1nNWVURldFaTZYUzE3TVgtTnlUdkhYblhqczg0UzhHODVyUDdfNnYzTFZhS1BWbmY3QkhZZ3RoNHAxaFpaWUJtVE5GVndTV0dTMDdBZlFrMXdGaUJWNk83UHQteDhhYndUUzhJZUxiTnBYR2JTeGczSTRVMmtrRTFlYklqSVE3TkIwb0lRYllDQ2lBMVFwNHBVYWNBS0sxc25XVGtyd2JacVlmRl9YZ2Q5SUtaVHhhamRTZHpfRUhR&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 13:57:01.2|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 13:57:01.2|Info|RssSyncService|RSS Sync Completed. Reports found: 889, Reports grabbed: 0
2025-07-17 14:12:30.6|Info|RssSyncService|Starting RSS Sync
2025-07-17 14:12:30.9|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 14:12:31.0|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 14:12:31.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 11:23:00 and 07/17/2025 12:12:31 UTC. Search may be required.
2025-07-17 14:12:31.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 11:23:00 and 07/17/2025 12:12:31 UTC. Search may be required.
2025-07-17 14:12:31.4|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/17/2025 11:17:00 and 07/17/2025 12:00:31 UTC. Search may be required.
2025-07-17 14:12:35.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 11:23:04 and 07/17/2025 12:12:35 UTC. Search may be required.
2025-07-17 14:12:38.4|Info|DownloadDecisionMaker|Processing 874 releases
2025-07-17 14:13:32.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpN3E1TkxVSTg2ak1NTzdLN2RVazF5UDhSODJWT2tPV0NZemZtSWZWT2dXUmFub2JYQ1dsVTZVMDBqcjJTdXRmZHdHcWRMYVpPeTVlbjU3VEdOVU9JOUNSMHd5SXJIcEJnMTk1SkJ4bWV1OTlEY0FVS1BRNU1udFo1TUtOUjZYN25SdGd2aUwxcGI4b2tTVnF0elo3Sjd4VDRqd1J6bHNjeFlmZU5UeFBoMWRFSnpkS04wWWZock9FUGJsQk40V0lJ&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 14:13:32.7|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpN3E1TkxVSTg2ak1NTzdLN2RVazF5UDhSODJWT2tPV0NZemZtSWZWT2dXUmFub2JYQ1dsVTZVMDBqcjJTdXRmZHdHcWRMYVpPeTVlbjU3VEdOVU9JOUNSMHd5SXJIcEJnMTk1SkJ4bWV1OTlEY0FVS1BRNU1udFo1TUtOUjZYN25SdGd2aUwxcGI4b2tTVnF0elo3Sjd4VDRqd1J6bHNjeFlmZU5UeFBoMWRFSnpkS04wWWZock9FUGJsQk40V0lJ&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpN3E1TkxVSTg2ak1NTzdLN2RVazF5UDhSODJWT2tPV0NZemZtSWZWT2dXUmFub2JYQ1dsVTZVMDBqcjJTdXRmZHdHcWRMYVpPeTVlbjU3VEdOVU9JOUNSMHd5SXJIcEJnMTk1SkJ4bWV1OTlEY0FVS1BRNU1udFo1TUtOUjZYN25SdGd2aUwxcGI4b2tTVnF0elo3Sjd4VDRqd1J6bHNjeFlmZU5UeFBoMWRFSnpkS04wWWZock9FUGJsQk40V0lJ&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 14:13:32.7|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 14:13:34.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loVnJvZjdCR1pKZEhjRXA4NEExcU1maV93UVZ6RnhtU0liUUk2M0tIdDJiZHp1TUJMT0lDNUdyMFM5NFdZTnI5Q1FSRVZ1LTFxWURHOGdmbW94M3Z4a2dVa0pneFZlM3RNdUhYV0Q3M2Nha1lad1VudGg3eEtuVDhSVnc2cl9XV3hpYk1ZZjF0VjBMbDBmT0ZHN1kxb1VyS0Z5VGRPaTFYSXlyaU1qMG1GVHBKM0ZIRWhnSTZ2WjB6d201Z3BJNms0&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 14:13:34.7|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loVnJvZjdCR1pKZEhjRXA4NEExcU1maV93UVZ6RnhtU0liUUk2M0tIdDJiZHp1TUJMT0lDNUdyMFM5NFdZTnI5Q1FSRVZ1LTFxWURHOGdmbW94M3Z4a2dVa0pneFZlM3RNdUhYV0Q3M2Nha1lad1VudGg3eEtuVDhSVnc2cl9XV3hpYk1ZZjF0VjBMbDBmT0ZHN1kxb1VyS0Z5VGRPaTFYSXlyaU1qMG1GVHBKM0ZIRWhnSTZ2WjB6d201Z3BJNms0&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loVnJvZjdCR1pKZEhjRXA4NEExcU1maV93UVZ6RnhtU0liUUk2M0tIdDJiZHp1TUJMT0lDNUdyMFM5NFdZTnI5Q1FSRVZ1LTFxWURHOGdmbW94M3Z4a2dVa0pneFZlM3RNdUhYV0Q3M2Nha1lad1VudGg3eEtuVDhSVnc2cl9XV3hpYk1ZZjF0VjBMbDBmT0ZHN1kxb1VyS0Z5VGRPaTFYSXlyaU1qMG1GVHBKM0ZIRWhnSTZ2WjB6d201Z3BJNms0&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 14:13:34.7|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 14:13:34.7|Info|RssSyncService|RSS Sync Completed. Reports found: 874, Reports grabbed: 0
2025-07-17 14:29:00.8|Info|RssSyncService|Starting RSS Sync
2025-07-17 14:29:00.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 12:12:31 and 07/17/2025 12:12:31 UTC. Search may be required.
2025-07-17 14:29:01.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 14:29:01.2|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 14:29:01.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 12:12:31 and 07/17/2025 12:12:31 UTC. Search may be required.
2025-07-17 14:29:02.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 12:12:35 and 07/17/2025 12:12:35 UTC. Search may be required.
2025-07-17 14:29:03.0|Info|DownloadDecisionMaker|Processing 874 releases
2025-07-17 14:29:55.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnLVRxaFktakdhVU1LYlIxdklIclM5Q2wyQVFnTXU4UDF4Z1YtUnF6Q3RzeXJRbF9kS0lONHZWN29PSjhycGF5X0o5SjZ3cVFvQlB2SUt3NWVuSGtHMGh3ZDFfT3JwMFQtZkI3dlQzNV9FS3lOeHhQVkc0bW04akxFYlhNSVBQTWJ6SVJpZ2YtbHB5Um9ySE9NMXNoY3Q5UFF4dW14bmsxbVVIRkhZaGQ3d1JiekpyQ21CSjhEV2tKMTZVME9YMlJR&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 14:29:55.8|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnLVRxaFktakdhVU1LYlIxdklIclM5Q2wyQVFnTXU4UDF4Z1YtUnF6Q3RzeXJRbF9kS0lONHZWN29PSjhycGF5X0o5SjZ3cVFvQlB2SUt3NWVuSGtHMGh3ZDFfT3JwMFQtZkI3dlQzNV9FS3lOeHhQVkc0bW04akxFYlhNSVBQTWJ6SVJpZ2YtbHB5Um9ySE9NMXNoY3Q5UFF4dW14bmsxbVVIRkhZaGQ3d1JiekpyQ21CSjhEV2tKMTZVME9YMlJR&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnLVRxaFktakdhVU1LYlIxdklIclM5Q2wyQVFnTXU4UDF4Z1YtUnF6Q3RzeXJRbF9kS0lONHZWN29PSjhycGF5X0o5SjZ3cVFvQlB2SUt3NWVuSGtHMGh3ZDFfT3JwMFQtZkI3dlQzNV9FS3lOeHhQVkc0bW04akxFYlhNSVBQTWJ6SVJpZ2YtbHB5Um9ySE9NMXNoY3Q5UFF4dW14bmsxbVVIRkhZaGQ3d1JiekpyQ21CSjhEV2tKMTZVME9YMlJR&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 14:29:55.8|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 14:29:57.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loMnFuQWlpSFlYQ0FjZzh5N3BJSWM4MGNTczlRMlhheUtDM0FYUGlSakZ4WGliNVpnV01ielFzUGVTNmpuM0h4WlNMZkZXN3d0ZTNsZEVNYVRQNzRiYkdGNUlRVUZsOHdFWlozcnpadkU4QTlRc3I2SFpYTGM0TlVqOWhRUnA5QUhZNW5heGpIay1WTTROc0E3ajltLWlLQ2dPOXNVQVlxcW9ZUDZuMFd6QlhYVnZNN3p4eXJucldFQ2djc180Ym84&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 14:29:57.7|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loMnFuQWlpSFlYQ0FjZzh5N3BJSWM4MGNTczlRMlhheUtDM0FYUGlSakZ4WGliNVpnV01ielFzUGVTNmpuM0h4WlNMZkZXN3d0ZTNsZEVNYVRQNzRiYkdGNUlRVUZsOHdFWlozcnpadkU4QTlRc3I2SFpYTGM0TlVqOWhRUnA5QUhZNW5heGpIay1WTTROc0E3ajltLWlLQ2dPOXNVQVlxcW9ZUDZuMFd6QlhYVnZNN3p4eXJucldFQ2djc180Ym84&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loMnFuQWlpSFlYQ0FjZzh5N3BJSWM4MGNTczlRMlhheUtDM0FYUGlSakZ4WGliNVpnV01ielFzUGVTNmpuM0h4WlNMZkZXN3d0ZTNsZEVNYVRQNzRiYkdGNUlRVUZsOHdFWlozcnpadkU4QTlRc3I2SFpYTGM0TlVqOWhRUnA5QUhZNW5heGpIay1WTTROc0E3ajltLWlLQ2dPOXNVQVlxcW9ZUDZuMFd6QlhYVnZNN3p4eXJucldFQ2djc180Ym84&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 14:29:57.7|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 14:29:57.7|Info|RssSyncService|RSS Sync Completed. Reports found: 874, Reports grabbed: 0
2025-07-17 14:45:00.8|Info|RssSyncService|Starting RSS Sync
2025-07-17 14:45:00.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 12:12:31 and 07/17/2025 12:12:31 UTC. Search may be required.
2025-07-17 14:45:01.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 14:45:01.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 12:12:31 and 07/17/2025 12:12:31 UTC. Search may be required.
2025-07-17 14:45:01.2|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 14:45:02.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 12:12:35 and 07/17/2025 12:12:35 UTC. Search may be required.
2025-07-17 14:45:03.0|Info|DownloadDecisionMaker|Processing 874 releases
2025-07-17 14:45:53.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpR0RVRmhfWTU4d1lRbzVoT2drVDJ4WklTSERmd0NqcHpqaFpFeGFOc1BWM044Zk1ZeWNNc09rTG5QQzZmVVBNTlVGdkJ2aXNLOFRZbnp0U3JHeXBiUkd0amxKTFFJNklraVU5MHJWM1FBM3Z3eEpRa2tjYVVOVk9FcWVVdEZydkZOaHdpVDhkeVplandFTDc3ZXhNeWw5Y05NeEJITzhMX1hIeGRLdVVKVVpwOGxyaDVXRFE1MkcxUGtSdS1DSHlZ&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 14:45:53.6|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpR0RVRmhfWTU4d1lRbzVoT2drVDJ4WklTSERmd0NqcHpqaFpFeGFOc1BWM044Zk1ZeWNNc09rTG5QQzZmVVBNTlVGdkJ2aXNLOFRZbnp0U3JHeXBiUkd0amxKTFFJNklraVU5MHJWM1FBM3Z3eEpRa2tjYVVOVk9FcWVVdEZydkZOaHdpVDhkeVplandFTDc3ZXhNeWw5Y05NeEJITzhMX1hIeGRLdVVKVVpwOGxyaDVXRFE1MkcxUGtSdS1DSHlZ&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpR0RVRmhfWTU4d1lRbzVoT2drVDJ4WklTSERmd0NqcHpqaFpFeGFOc1BWM044Zk1ZeWNNc09rTG5QQzZmVVBNTlVGdkJ2aXNLOFRZbnp0U3JHeXBiUkd0amxKTFFJNklraVU5MHJWM1FBM3Z3eEpRa2tjYVVOVk9FcWVVdEZydkZOaHdpVDhkeVplandFTDc3ZXhNeWw5Y05NeEJITzhMX1hIeGRLdVVKVVpwOGxyaDVXRFE1MkcxUGtSdS1DSHlZ&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 14:45:53.6|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 14:45:55.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loTDctSXUweUF2Y3F0TlBobW9TYXJRSFpMX3VONHBSSmUyOTJwYlkwM1FXYVdTc29jalE4bURHN0JZb2dpT2FSZnJkNkVrQVcyQjg0YUNDcVFYN3FtUFZVVG1CekZkQWlqMzVlMktTc3BDUHZrRWRUSmkxZFduQkR1QURsQ2I5T0QwUEo0VWpNNU03YjN6c3VtZ2tONFdnbUZ1Y2EtWW13WWlweGc3SDVjakF4Y25mZ01jN1dxYjE3a09ybzIwZEx3&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 14:45:55.5|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loTDctSXUweUF2Y3F0TlBobW9TYXJRSFpMX3VONHBSSmUyOTJwYlkwM1FXYVdTc29jalE4bURHN0JZb2dpT2FSZnJkNkVrQVcyQjg0YUNDcVFYN3FtUFZVVG1CekZkQWlqMzVlMktTc3BDUHZrRWRUSmkxZFduQkR1QURsQ2I5T0QwUEo0VWpNNU03YjN6c3VtZ2tONFdnbUZ1Y2EtWW13WWlweGc3SDVjakF4Y25mZ01jN1dxYjE3a09ybzIwZEx3&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loTDctSXUweUF2Y3F0TlBobW9TYXJRSFpMX3VONHBSSmUyOTJwYlkwM1FXYVdTc29jalE4bURHN0JZb2dpT2FSZnJkNkVrQVcyQjg0YUNDcVFYN3FtUFZVVG1CekZkQWlqMzVlMktTc3BDUHZrRWRUSmkxZFduQkR1QURsQ2I5T0QwUEo0VWpNNU03YjN6c3VtZ2tONFdnbUZ1Y2EtWW13WWlweGc3SDVjakF4Y25mZ01jN1dxYjE3a09ybzIwZEx3&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 14:45:55.5|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 14:45:55.5|Info|RssSyncService|RSS Sync Completed. Reports found: 874, Reports grabbed: 0
2025-07-17 15:01:00.9|Info|RssSyncService|Starting RSS Sync
2025-07-17 15:01:01.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 15:01:01.1|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 15:01:01.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 12:12:31 and 07/17/2025 13:01:01 UTC. Search may be required.
2025-07-17 15:01:01.5|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/17/2025 12:11:31 and 07/17/2025 12:44:01 UTC. Search may be required.
2025-07-17 15:01:01.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 12:12:31 and 07/17/2025 13:01:01 UTC. Search may be required.
2025-07-17 15:01:05.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 12:12:35 and 07/17/2025 13:01:05 UTC. Search may be required.
2025-07-17 15:01:08.0|Info|DownloadDecisionMaker|Processing 874 releases
2025-07-17 15:02:02.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqT1FaVmlXRXRYSHlXZWl3T1A2WWJaVTVkQ1BwdzEyNngxNGMxYmV6UnhkWFV4VFpqWVJHc0dRMU05WmhsSGhyM1FzcWs3WldrTzN2cDNLdElkU3lMSUx4ODVtRFJUUUpJZDZDc2dGX1JIWm4wTG0tWklsVGlrU3M2SEJPc3NZRzdkUkR5NzZoMm5GUG5TOXRlWW1JZVMyS2N0X3JkbXkxeVlRUXdoUmxPQ25rVWlmWXc5ZmIyUHRmai13UnZfay0w&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 15:02:02.2|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqT1FaVmlXRXRYSHlXZWl3T1A2WWJaVTVkQ1BwdzEyNngxNGMxYmV6UnhkWFV4VFpqWVJHc0dRMU05WmhsSGhyM1FzcWs3WldrTzN2cDNLdElkU3lMSUx4ODVtRFJUUUpJZDZDc2dGX1JIWm4wTG0tWklsVGlrU3M2SEJPc3NZRzdkUkR5NzZoMm5GUG5TOXRlWW1JZVMyS2N0X3JkbXkxeVlRUXdoUmxPQ25rVWlmWXc5ZmIyUHRmai13UnZfay0w&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqT1FaVmlXRXRYSHlXZWl3T1A2WWJaVTVkQ1BwdzEyNngxNGMxYmV6UnhkWFV4VFpqWVJHc0dRMU05WmhsSGhyM1FzcWs3WldrTzN2cDNLdElkU3lMSUx4ODVtRFJUUUpJZDZDc2dGX1JIWm4wTG0tWklsVGlrU3M2SEJPc3NZRzdkUkR5NzZoMm5GUG5TOXRlWW1JZVMyS2N0X3JkbXkxeVlRUXdoUmxPQ25rVWlmWXc5ZmIyUHRmai13UnZfay0w&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 15:02:02.2|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 15:02:04.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpWUltQ2tDOTYzallPY2Q1bk9NSm50ZFlfVExjSWk1T2FHVkNWWXVkS042dG5JNGJBbl95WGdrMXlUaUQwckdsU1otZlNGSzFfa3pWTU55TllMMFhqUWdDZ0p3VXVRcHNlZF9nZ2ViVkJxOGhaVWhvc3lFc3dqbFRsQ25vcFNKcEJVaFNuVHJjakxRTUM3bmdHTzkxN3VCWWZmZWRuLUtiZHBfZ0dIbmUzczBWcThJNHQwLUhMLVNxRWlybktmWkVZ&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 15:02:04.1|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpWUltQ2tDOTYzallPY2Q1bk9NSm50ZFlfVExjSWk1T2FHVkNWWXVkS042dG5JNGJBbl95WGdrMXlUaUQwckdsU1otZlNGSzFfa3pWTU55TllMMFhqUWdDZ0p3VXVRcHNlZF9nZ2ViVkJxOGhaVWhvc3lFc3dqbFRsQ25vcFNKcEJVaFNuVHJjakxRTUM3bmdHTzkxN3VCWWZmZWRuLUtiZHBfZ0dIbmUzczBWcThJNHQwLUhMLVNxRWlybktmWkVZ&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpWUltQ2tDOTYzallPY2Q1bk9NSm50ZFlfVExjSWk1T2FHVkNWWXVkS042dG5JNGJBbl95WGdrMXlUaUQwckdsU1otZlNGSzFfa3pWTU55TllMMFhqUWdDZ0p3VXVRcHNlZF9nZ2ViVkJxOGhaVWhvc3lFc3dqbFRsQ25vcFNKcEJVaFNuVHJjakxRTUM3bmdHTzkxN3VCWWZmZWRuLUtiZHBfZ0dIbmUzczBWcThJNHQwLUhMLVNxRWlybktmWkVZ&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 15:02:04.1|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 15:02:04.1|Info|RssSyncService|RSS Sync Completed. Reports found: 874, Reports grabbed: 0
2025-07-17 15:17:31.0|Info|RssSyncService|Starting RSS Sync
2025-07-17 15:17:31.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 13:01:01 and 07/17/2025 13:01:01 UTC. Search may be required.
2025-07-17 15:17:31.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 13:01:01 and 07/17/2025 13:01:01 UTC. Search may be required.
2025-07-17 15:17:33.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 13:01:05 and 07/17/2025 13:01:05 UTC. Search may be required.
2025-07-17 15:17:33.2|Info|DownloadDecisionMaker|Processing 874 releases
2025-07-17 15:18:24.3|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqeGlzSnZOT2lWNGxDeTRJdENnelB0VUtqZXVSZTVIYmlzQXNtUld4R0RiUm9RejBQQnFwSk12N3FRdVJmQXJ4bTV0X0hrZkwybHRMbG40V1JNeWdGV2RhQ3FEdTZpTHpzYUE1OUhEV2EyY2RqVWpyMFJfMi1jUHBOQlU3bFlSYVNoTUtQZVhfSU85Uy1rbjVCUlF1ak85M294dWJhWDZKTlRBQzdBeHVwTy1lMlBhSnl2VG1KTkdLdXYzWXZEbG9j&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 15:18:24.3|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqeGlzSnZOT2lWNGxDeTRJdENnelB0VUtqZXVSZTVIYmlzQXNtUld4R0RiUm9RejBQQnFwSk12N3FRdVJmQXJ4bTV0X0hrZkwybHRMbG40V1JNeWdGV2RhQ3FEdTZpTHpzYUE1OUhEV2EyY2RqVWpyMFJfMi1jUHBOQlU3bFlSYVNoTUtQZVhfSU85Uy1rbjVCUlF1ak85M294dWJhWDZKTlRBQzdBeHVwTy1lMlBhSnl2VG1KTkdLdXYzWXZEbG9j&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqeGlzSnZOT2lWNGxDeTRJdENnelB0VUtqZXVSZTVIYmlzQXNtUld4R0RiUm9RejBQQnFwSk12N3FRdVJmQXJ4bTV0X0hrZkwybHRMbG40V1JNeWdGV2RhQ3FEdTZpTHpzYUE1OUhEV2EyY2RqVWpyMFJfMi1jUHBOQlU3bFlSYVNoTUtQZVhfSU85Uy1rbjVCUlF1ak85M294dWJhWDZKTlRBQzdBeHVwTy1lMlBhSnl2VG1KTkdLdXYzWXZEbG9j&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 15:18:24.3|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 15:18:26.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnV1VwZlpZMXpzRllCQWJCMTJsUXRWQmVwX2Rsb0hBcjFwcmtRUFFFSGFMQjB2TE9NX0FKTW9QdVN0bmdOTXlsZWNBTF9ZcF9DWHRoRml3YnF4QWJIUWpGVmVpd3o5WE9Dbkw3V3FrdWJRcHRDZnZJd0ZOZnNkeUZubENWVEhfcjZTLUN2VVdjUFR0Rmw4V0tyampQOUlMY2pGNVdBV0I1d202S253QWF5cTRsU29nRjQ3Z0RKNVk4QTY0MkNQMnA0&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 15:18:26.2|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnV1VwZlpZMXpzRllCQWJCMTJsUXRWQmVwX2Rsb0hBcjFwcmtRUFFFSGFMQjB2TE9NX0FKTW9QdVN0bmdOTXlsZWNBTF9ZcF9DWHRoRml3YnF4QWJIUWpGVmVpd3o5WE9Dbkw3V3FrdWJRcHRDZnZJd0ZOZnNkeUZubENWVEhfcjZTLUN2VVdjUFR0Rmw4V0tyampQOUlMY2pGNVdBV0I1d202S253QWF5cTRsU29nRjQ3Z0RKNVk4QTY0MkNQMnA0&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnV1VwZlpZMXpzRllCQWJCMTJsUXRWQmVwX2Rsb0hBcjFwcmtRUFFFSGFMQjB2TE9NX0FKTW9QdVN0bmdOTXlsZWNBTF9ZcF9DWHRoRml3YnF4QWJIUWpGVmVpd3o5WE9Dbkw3V3FrdWJRcHRDZnZJd0ZOZnNkeUZubENWVEhfcjZTLUN2VVdjUFR0Rmw4V0tyampQOUlMY2pGNVdBV0I1d202S253QWF5cTRsU29nRjQ3Z0RKNVk4QTY0MkNQMnA0&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 15:18:26.2|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 15:18:26.2|Info|RssSyncService|RSS Sync Completed. Reports found: 874, Reports grabbed: 0
2025-07-17 15:33:31.1|Info|RssSyncService|Starting RSS Sync
2025-07-17 15:33:31.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 13:01:01 and 07/17/2025 13:01:01 UTC. Search may be required.
2025-07-17 15:33:31.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 13:01:01 and 07/17/2025 13:01:01 UTC. Search may be required.
2025-07-17 15:33:33.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 13:01:05 and 07/17/2025 13:01:05 UTC. Search may be required.
2025-07-17 15:33:33.3|Info|DownloadDecisionMaker|Processing 889 releases
2025-07-17 15:34:27.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpZnczT3QyeG8wWDFKWm4yOTFFQ1A2cDNweDFhcEVqcGdUdHlmTFZxcjN2cUtaaEd0UnJtM3ZQYXU0RWpnZ2hTUDkwMnQ5SVBHT2xqM1MyaDUyN05RTlNqeV9KUXJMWUhOV1NtVjhmaFlsZHlVdGdOZ1JmclFYSE8tSGZSMWFPd19TVzhCRGpsWm1lQmd0eTBpaFRZX2RwWU9rNW5Qd1Q1V2d1QmZiaXQyRnpEVXkxUXZjSmF1ZlhrVTFjZzVNTHd3&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 15:34:27.6|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpZnczT3QyeG8wWDFKWm4yOTFFQ1A2cDNweDFhcEVqcGdUdHlmTFZxcjN2cUtaaEd0UnJtM3ZQYXU0RWpnZ2hTUDkwMnQ5SVBHT2xqM1MyaDUyN05RTlNqeV9KUXJMWUhOV1NtVjhmaFlsZHlVdGdOZ1JmclFYSE8tSGZSMWFPd19TVzhCRGpsWm1lQmd0eTBpaFRZX2RwWU9rNW5Qd1Q1V2d1QmZiaXQyRnpEVXkxUXZjSmF1ZlhrVTFjZzVNTHd3&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpZnczT3QyeG8wWDFKWm4yOTFFQ1A2cDNweDFhcEVqcGdUdHlmTFZxcjN2cUtaaEd0UnJtM3ZQYXU0RWpnZ2hTUDkwMnQ5SVBHT2xqM1MyaDUyN05RTlNqeV9KUXJMWUhOV1NtVjhmaFlsZHlVdGdOZ1JmclFYSE8tSGZSMWFPd19TVzhCRGpsWm1lQmd0eTBpaFRZX2RwWU9rNW5Qd1Q1V2d1QmZiaXQyRnpEVXkxUXZjSmF1ZlhrVTFjZzVNTHd3&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 15:34:27.6|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 15:34:29.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqek13T2lTZ3djc1R5cU8wOXJUSm5OSHhXSUhFeUtHaGRTSTBqd1g1Zk80OXNqaFVtVEZPTGtZTEloSFJuV21hSlk4YWlhMjhicmNORFhoeUZmaXIzQ19CbmU4bmZRdUZRN0U5ZXpSTFA0NUFySE40VklrSXJUZjR3djJHbzgySzlXVVR6ZVozdTRSMjlIa0xUNkxHZk92eWtmNXo0UmctR21XbzRFMldoYWdMd05ndXJkQVJhNDJwRTFuRnc2M3Fz&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 15:34:29.5|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqek13T2lTZ3djc1R5cU8wOXJUSm5OSHhXSUhFeUtHaGRTSTBqd1g1Zk80OXNqaFVtVEZPTGtZTEloSFJuV21hSlk4YWlhMjhicmNORFhoeUZmaXIzQ19CbmU4bmZRdUZRN0U5ZXpSTFA0NUFySE40VklrSXJUZjR3djJHbzgySzlXVVR6ZVozdTRSMjlIa0xUNkxHZk92eWtmNXo0UmctR21XbzRFMldoYWdMd05ndXJkQVJhNDJwRTFuRnc2M3Fz&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lqek13T2lTZ3djc1R5cU8wOXJUSm5OSHhXSUhFeUtHaGRTSTBqd1g1Zk80OXNqaFVtVEZPTGtZTEloSFJuV21hSlk4YWlhMjhicmNORFhoeUZmaXIzQ19CbmU4bmZRdUZRN0U5ZXpSTFA0NUFySE40VklrSXJUZjR3djJHbzgySzlXVVR6ZVozdTRSMjlIa0xUNkxHZk92eWtmNXo0UmctR21XbzRFMldoYWdMd05ndXJkQVJhNDJwRTFuRnc2M3Fz&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 15:34:29.6|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 15:34:29.6|Info|RssSyncService|RSS Sync Completed. Reports found: 889, Reports grabbed: 0
2025-07-17 15:43:31.2|Info|SceneMappingService|Updating Scene mappings
2025-07-17 15:49:31.3|Info|RssSyncService|Starting RSS Sync
2025-07-17 15:49:31.9|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-17 15:49:31.9|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-17 15:49:32.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 13:01:01 and 07/17/2025 13:49:32 UTC. Search may be required.
2025-07-17 15:49:32.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 13:01:01 and 07/17/2025 13:49:32 UTC. Search may be required.
2025-07-17 15:49:32.2|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/17/2025 12:47:01 and 07/17/2025 13:34:32 UTC. Search may be required.
2025-07-17 15:49:36.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 13:01:05 and 07/17/2025 13:49:36 UTC. Search may be required.
2025-07-17 15:49:38.8|Info|DownloadDecisionMaker|Processing 789 releases
2025-07-17 15:50:41.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnMEMzSm94MEpWSTZmYVF2VUVEcDRfbExLTjFIeVdEZFUzOUM2WWhyWkNrMEx0MFlFQ3ZIUXJnTnpRRFEzVHJabDlpS3ZQekl1WXFIcE4teUcyNk9fYjhSMGxtT0FYZTJ6ZDd2WjJUUloyek80QWdUdWdzYkNvX29mVEY3ZkFEeElZdm9rcUxUUGxZckh1Sm5lMHhIRk5xRU9XOUR3TnRiX2JPSXRXOC1VN2JhT0JhTTF2elRyWEJONlo5Q3lLNjhz&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 15:50:41.4|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnMEMzSm94MEpWSTZmYVF2VUVEcDRfbExLTjFIeVdEZFUzOUM2WWhyWkNrMEx0MFlFQ3ZIUXJnTnpRRFEzVHJabDlpS3ZQekl1WXFIcE4teUcyNk9fYjhSMGxtT0FYZTJ6ZDd2WjJUUloyek80QWdUdWdzYkNvX29mVEY3ZkFEeElZdm9rcUxUUGxZckh1Sm5lMHhIRk5xRU9XOUR3TnRiX2JPSXRXOC1VN2JhT0JhTTF2elRyWEJONlo5Q3lLNjhz&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lnMEMzSm94MEpWSTZmYVF2VUVEcDRfbExLTjFIeVdEZFUzOUM2WWhyWkNrMEx0MFlFQ3ZIUXJnTnpRRFEzVHJabDlpS3ZQekl1WXFIcE4teUcyNk9fYjhSMGxtT0FYZTJ6ZDd2WjJUUloyek80QWdUdWdzYkNvX29mVEY3ZkFEeElZdm9rcUxUUGxZckh1Sm5lMHhIRk5xRU9XOUR3TnRiX2JPSXRXOC1VN2JhT0JhTTF2elRyWEJONlo5Q3lLNjhz&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 15:50:41.4|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 15:50:43.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpMzBpb2dEZ3hlQ3V5VGk5dFF3MWM2WVdCSkFabFBHNlFPWGZ6NmVVS0hLYjhiQUh3WDg1Qk5jUFdsckN5akhQRUtCTm95dlhQak9OTXd2bEI0M2FWTnBwS1kxM3MyOExMVzI2akFkX0NCVTFtMmlvNVFDT0s3NURnNlFXa1dxRUR4dEstVnJ6TWtpWHVMTHNZczI1bGhlZWVsbTd3SVR3WnhzdUFidXdpRFFNaG9CYVQyV05wMEhEbkRocEQ2Rm9j&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 15:50:43.5|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpMzBpb2dEZ3hlQ3V5VGk5dFF3MWM2WVdCSkFabFBHNlFPWGZ6NmVVS0hLYjhiQUh3WDg1Qk5jUFdsckN5akhQRUtCTm95dlhQak9OTXd2bEI0M2FWTnBwS1kxM3MyOExMVzI2akFkX0NCVTFtMmlvNVFDT0s3NURnNlFXa1dxRUR4dEstVnJ6TWtpWHVMTHNZczI1bGhlZWVsbTd3SVR3WnhzdUFidXdpRFFNaG9CYVQyV05wMEhEbkRocEQ2Rm9j&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lpMzBpb2dEZ3hlQ3V5VGk5dFF3MWM2WVdCSkFabFBHNlFPWGZ6NmVVS0hLYjhiQUh3WDg1Qk5jUFdsckN5akhQRUtCTm95dlhQak9OTXd2bEI0M2FWTnBwS1kxM3MyOExMVzI2akFkX0NCVTFtMmlvNVFDT0s3NURnNlFXa1dxRUR4dEstVnJ6TWtpWHVMTHNZczI1bGhlZWVsbTd3SVR3WnhzdUFidXdpRFFNaG9CYVQyV05wMEhEbkRocEQ2Rm9j&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 15:50:43.5|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 15:50:43.5|Info|RssSyncService|RSS Sync Completed. Reports found: 789, Reports grabbed: 0
2025-07-17 16:06:01.7|Info|RssSyncService|Starting RSS Sync
2025-07-17 16:06:01.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/17/2025 13:49:32 and 07/17/2025 13:49:32 UTC. Search may be required.
2025-07-17 16:06:02.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/17/2025 13:49:32 and 07/17/2025 13:49:32 UTC. Search may be required.
2025-07-17 16:06:03.9|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/17/2025 12:58:01 and 07/17/2025 13:06:02 UTC. Search may be required.
2025-07-17 16:06:04.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/17/2025 13:49:36 and 07/17/2025 13:49:36 UTC. Search may be required.
2025-07-17 16:06:04.0|Info|DownloadDecisionMaker|Processing 889 releases
2025-07-17 16:07:28.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lna0VlcWcxdHFIQmc3TEppWGlGX1hUUUNYeVBIMHVjT2pabjc2SHdPT3YwQlRqeWItQWFkN2toR2hDSEhnTXF2bm1aMEVXZ3FUT3V5SGRCNVViZ1E3XzA5UldudWcybFRQRmh6YXFBYVJic01yYTRFR3FOek5OTEVvSlFOMC1BQXlKSXZtSHZpZ3dXT2MzUWc0eXFyaVV0eTVQX3dJRzgzS0FrV2JrcTR0YzF2MkxQTnVXaFVqelJQMzQxS2U1QTln&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 16:07:28.5|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lna0VlcWcxdHFIQmc3TEppWGlGX1hUUUNYeVBIMHVjT2pabjc2SHdPT3YwQlRqeWItQWFkN2toR2hDSEhnTXF2bm1aMEVXZ3FUT3V5SGRCNVViZ1E3XzA5UldudWcybFRQRmh6YXFBYVJic01yYTRFR3FOek5OTEVvSlFOMC1BQXlKSXZtSHZpZ3dXT2MzUWc0eXFyaVV0eTVQX3dJRzgzS0FrV2JrcTR0YzF2MkxQTnVXaFVqelJQMzQxS2U1QTln&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0lna0VlcWcxdHFIQmc3TEppWGlGX1hUUUNYeVBIMHVjT2pabjc2SHdPT3YwQlRqeWItQWFkN2toR2hDSEhnTXF2bm1aMEVXZ3FUT3V5SGRCNVViZ1E3XzA5UldudWcybFRQRmh6YXFBYVJic01yYTRFR3FOek5OTEVvSlFOMC1BQXlKSXZtSHZpZ3dXT2MzUWc0eXFyaVV0eTVQX3dJRzgzS0FrV2JrcTR0YzF2MkxQTnVXaFVqelJQMzQxS2U1QTln&file=El+juego+del+calamar+S03+2160p+NF+WEB-DL+DD%2B****+Atmos+DV+HDR+H.265-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 16:07:28.8|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 2160p NF WEB-DL DD+ 5.1 Atmos DV HDR H.265-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 16:07:30.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loeTB4UmJoZm55LW1LeEVRbWExNGF3bE94WGs0QUk4b1ZmZ0psTnRiZFl2UG5wck55WXpjYVB4RDJ3RVRJRk94eXVUSERhOGgtMUtwQzRib2JfZ0Vwd3g3RnJxcHlfUHRHTkw5OU82OEFfY1M5ZFNmeTlqLV9zdDhIc1FDWVdraV9pd0NfLUhXb1lBSVdhWDdUNlU2U2U5Qkl5SW04SU9JTXZIWWNWNHdFRF83R3N2NUJwemhjZjBfNW9ZMzFpZnNF&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish: 404.NotFound (0 bytes)
2025-07-17 16:07:30.4|Error|QBittorrent|Downloading torrent file for episode 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' failed since it no longer exists (http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loeTB4UmJoZm55LW1LeEVRbWExNGF3bE94WGs0QUk4b1ZmZ0psTnRiZFl2UG5wck55WXpjYVB4RDJ3RVRJRk94eXVUSERhOGgtMUtwQzRib2JfZ0Vwd3g3RnJxcHlfUHRHTkw5OU82OEFfY1M5ZFNmeTlqLV9zdDhIc1FDWVdraV9pd0NfLUhXb1lBSVdhWDdUNlU2U2U5Qkl5SW04SU9JTXZIWWNWNHdFRF83R3N2NUJwemhjZjBfNW9ZMzFpZnNF&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish)

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [GET] at [http://jackett:9117/dl/emuwarez/?jackett_apikey=(removed)&path=Q2ZESjhDX2pEekctRVpoSm50UmpPUktlR0loeTB4UmJoZm55LW1LeEVRbWExNGF3bE94WGs0QUk4b1ZmZ0psTnRiZFl2UG5wck55WXpjYVB4RDJ3RVRJRk94eXVUSERhOGgtMUtwQzRib2JfZ0Vwd3g3RnJxcHlfUHRHTkw5OU82OEFfY1M5ZFNmeTlqLV9zdDhIc1FDWVdraV9pd0NfLUhXb1lBSVdhWDdUNlU2U2U5Qkl5SW04SU9JTXZIWWNWNHdFRF83R3N2NUJwemhjZjBfNW9ZMzFpZnNF&file=El+juego+del+calamar+S03+REPACK+1080p+NF+WEB-DL+DD%2B****+Atmos+H.264-EMUWAREZ+Spanish]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Core.Download.TorrentClientBase`1.<>c.<<DownloadFromWebUrl>b__11_0>d.MoveNext() in ./Sonarr.Core/Download/TorrentClientBase.cs:line 143
--- End of stack trace from previous location ---
   at Polly.ResiliencePipeline.<>c__9`2.<<ExecuteAsync>b__9_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Outcome`1.GetResultOrRethrow()
   at Polly.ResiliencePipeline.ExecuteAsync[TResult,TState](Func`3 callback, TState state, CancellationToken cancellationToken)
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 215


2025-07-17 16:07:30.4|Warn|ProcessDownloadDecisions|Failed to download release 'El juego del calamar S03 REPACK 1080p NF WEB-DL DD+ 5.1 Atmos H.264-EMUWAREZ Spanish' from Indexer Emuwarez. Release not available
2025-07-17 16:07:30.5|Info|RssSyncService|RSS Sync Completed. Reports found: 889, Reports grabbed: 0
2025-07-17 16:13:44.0|Warn|Auth|Auth-Failure ip ********** username 'Tankeeee2_GAMES'
2025-07-17 16:13:52.7|Warn|Auth|Auth-Failure ip ********** username 'Tankeeee2_GAMES'
2025-07-17 16:14:04.9|Warn|Auth|Auth-Failure ip ********** username 'Tankeeee2_GAMES'
2025-07-17 16:14:13.0|Warn|Auth|Auth-Failure ip ********** username 'Tankeeee2_GAMES'
2025-07-17 16:14:24.7|Warn|Auth|Auth-Failure ip ********** username 'Tankeeee2_GAMES'
