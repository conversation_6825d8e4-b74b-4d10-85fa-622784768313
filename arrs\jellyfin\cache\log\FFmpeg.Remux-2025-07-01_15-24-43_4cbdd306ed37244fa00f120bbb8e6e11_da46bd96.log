{"Protocol":0,"Id":"4cbdd306ed37244fa00f120bbb8e6e11","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 3/<PERSON> and <PERSON><PERSON><PERSON> - S03E03 - <PERSON><PERSON> HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":221810524,"Name":"<PERSON> and <PERSON><PERSON>y - S03E03 - <PERSON><PERSON> Rick HDTV-720p","IsRemote":false,"ETag":"229fe763798230be6d784ebe4887b725","RunTimeTicks":13450880000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":911143,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":103,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":89,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1319232,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E03 - Pickle Rick HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x56afab679840] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E03 - Pickle Rick HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Rickinillo
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Beth, Summer y Morty van a terapia familiar.Lo típico. Rickinillo también.
    show            : Rick y Morty
    episode_id      : 3
    season_number   : 3
  Duration: 00:22:25.09, start: 0.000000, bitrate: 1319 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 911 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x56afab6cf040] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 911 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0050.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0051.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0052.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0053.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0054.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0055.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0056.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0057.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0058.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d0059.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00510.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00511.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00512.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00513.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00514.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00515.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00516.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00517.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00518.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00519.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00520.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00521.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00522.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00523.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00524.ts' for writing
size=N/A time=00:01:23.52 bitrate=N/A speed=83.5x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00525.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00526.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00527.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00528.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00529.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00530.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00531.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00532.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00533.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00534.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00535.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00536.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00537.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00538.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00539.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00540.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00541.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00542.ts' for writing
size=N/A time=00:03:09.40 bitrate=N/A speed= 126x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00543.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00544.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00545.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00546.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00547.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00548.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00549.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00550.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00551.ts' for writing
size=N/A time=00:04:05.92 bitrate=N/A speed= 123x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00552.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00553.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00554.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00555.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00556.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00557.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00558.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00559.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00560.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00561.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00562.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00563.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00564.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00565.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00566.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00567.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00568.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00569.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00570.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00571.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00572.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00573.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00574.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00575.ts' for writing
size=N/A time=00:06:24.19 bitrate=N/A speed= 154x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00576.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00577.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00578.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00579.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00580.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00581.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00582.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00583.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00584.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00585.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00586.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00587.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00588.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00589.ts' for writing
size=N/A time=00:07:56.32 bitrate=N/A speed= 159x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00590.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00591.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00592.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00593.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00594.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00595.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00596.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00597.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00598.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d00599.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005100.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005101.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005102.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005103.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005104.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005105.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005106.ts' for writing
size=N/A time=00:09:34.94 bitrate=N/A speed= 164x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005107.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005108.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005109.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005110.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005111.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005112.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005113.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005114.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005115.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005116.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005117.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005118.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005119.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005120.ts' for writing
size=N/A time=00:10:57.18 bitrate=N/A speed= 164x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005121.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005122.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005123.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005124.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005125.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005126.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005127.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005128.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005129.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005130.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005131.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005132.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005133.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005134.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005135.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005136.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005137.ts' for writing
size=N/A time=00:12:40.99 bitrate=N/A speed= 169x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005138.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005139.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005140.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005141.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005142.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005143.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005144.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005145.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005146.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005147.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005148.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005149.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005150.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005151.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005152.ts' for writing
size=N/A time=00:14:09.18 bitrate=N/A speed= 169x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005153.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005154.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005155.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005156.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005157.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005158.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005159.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005160.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005161.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005162.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005163.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005164.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005165.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005166.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005167.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005168.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005169.ts' for writing
size=N/A time=00:15:50.62 bitrate=N/A speed= 172x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005170.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005171.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005172.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005173.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005174.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005175.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005176.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005177.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005178.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005179.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005180.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005181.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005182.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005183.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005184.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005185.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005186.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005187.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005188.ts' for writing
size=N/A time=00:17:44.48 bitrate=N/A speed= 177x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005189.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005190.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005191.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005192.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005193.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005194.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005195.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005196.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005197.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005198.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005199.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005200.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005201.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005202.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005203.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005204.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005205.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005206.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005207.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005208.ts' for writing
size=N/A time=00:19:48.28 bitrate=N/A speed= 182x    
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005209.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005210.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005211.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005212.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005213.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005214.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005215.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005216.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005217.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005218.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005219.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005220.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005221.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005222.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005223.ts' for writing
[hls @ 0x56afab6cc1c0] Opening '/cache/transcodes/a3e2231c5e199574d90ac12fcc57d005224.ts' for writing
[out#0/hls @ 0x56afab6cf040] video:149589KiB audio:21017KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:21:13.09 bitrate=N/A speed= 184x    