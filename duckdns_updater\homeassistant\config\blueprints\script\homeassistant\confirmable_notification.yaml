blueprint:
  name: Confirmable Notification
  description: >-
    A script that sends an actionable notification with a confirmation before
    running the specified action.
  domain: script
  source_url: https://github.com/home-assistant/core/blob/master/homeassistant/components/script/blueprints/confirmable_notification.yaml
  author: Home Assistant
  input:
    notify_device:
      name: Devi<PERSON> to notify
      description: <PERSON><PERSON> needs to run the official Home Assistant app to receive notifications.
      selector:
        device:
          filter:
            integration: mobile_app
    title:
      name: "Title"
      description: "The title of the button shown in the notification."
      default: ""
      selector:
        text:
    message:
      name: "Message"
      description: "The message body"
      selector:
        text:
    confirm_text:
      name: "Confirmation Text"
      description: "Text to show on the confirmation button"
      default: "Confirm"
      selector:
        text:
    confirm_action:
      name: "Confirmation Action"
      description: "Action to run when notification is confirmed"
      default: []
      selector:
        action:
    dismiss_text:
      name: "Dismiss Text"
      description: "Text to show on the dismiss button"
      default: "Dismiss"
      selector:
        text:
    dismiss_action:
      name: "Dismiss Action"
      description: "Action to run when notification is dismissed"
      default: []
      selector:
        action:

mode: restart

sequence:
  - alias: "Set up variables"
    variables:
      action_confirm: "{{ 'CONFIRM_' ~ context.id }}"
      action_dismiss: "{{ 'DISMISS_' ~ context.id }}"
  - alias: "Send notification"
    domain: mobile_app
    type: notify
    device_id: !input notify_device
    title: !input title
    message: !input message
    data:
      actions:
        - action: "{{ action_confirm }}"
          title: !input confirm_text
        - action: "{{ action_dismiss }}"
          title: !input dismiss_text
  - alias: "Awaiting response"
    wait_for_trigger:
      - trigger: event
        event_type: mobile_app_notification_action
        event_data:
          action: "{{ action_confirm }}"
      - trigger: event
        event_type: mobile_app_notification_action
        event_data:
          action: "{{ action_dismiss }}"
  - choose:
      - conditions: "{{ wait.trigger.event.data.action == action_confirm }}"
        sequence: !input confirm_action
      - conditions: "{{ wait.trigger.event.data.action == action_dismiss }}"
        sequence: !input dismiss_action
