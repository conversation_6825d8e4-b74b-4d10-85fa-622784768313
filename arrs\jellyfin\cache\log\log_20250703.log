[2025-07-03 00:12:13.777 +02:00] [INF] [152] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" request
[2025-07-03 00:13:59.699 +02:00] [INF] [136] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" closed
[2025-07-03 00:26:14.482 +02:00] [INF] [49] Emby.Server.Implementations.IO.LibraryMonitor: "<PERSON> y Morty" ("/CONTENIDO/SERIES/<PERSON> and Morty") will be refreshed.
[2025-07-03 00:26:23.678 +02:00] [INF] [92] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "<PERSON> y Morty"
[2025-07-03 00:26:26.667 +02:00] [INF] [92] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/<PERSON> and <PERSON>rty/Season 8/<PERSON> and <PERSON>rty - S08E06 - The CuRicksous Case of Bethjamin Button HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-07-03 00:26:28.564 +02:00] [INF] [92] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-03 00:26:28.564 +02:00] [INF] [92] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-07-03 00:26:32.575 +02:00] [INF] [44] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Episode", Name: "The Curicksous Case of Bethjamin Button", Path: "", Id: 76280cb0-e2c5-91bf-1d06-c07c93cce98b
[2025-07-03 00:26:32.710 +02:00] [INF] [44] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Rick y Morty"
[2025-07-03 00:26:32.710 +02:00] [INF] [44] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 6ed225c1-b48f-10be-fa7e-3694be710444
[2025-07-03 00:26:42.623 +02:00] [INF] [44] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-03 00:26:42.652 +02:00] [INF] [44] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-07-03 00:26:42.652 +02:00] [INF] [44] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-07-03 00:26:42.653 +02:00] [INF] [44] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Update to process
[2025-07-03 00:26:42.683 +02:00] [INF] [44] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-07-03 00:26:42.683 +02:00] [INF] [44] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-07-03 00:26:42.683 +02:00] [INF] [44] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 episodes with event type Update
[2025-07-03 00:26:43.093 +02:00] [INF] [93] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-07-03 00:26:43.094 +02:00] [INF] [93] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-07-03 00:26:43.094 +02:00] [INF] [93] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-07-03 01:03:24.152 +02:00] [INF] [162] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 01:03:24.153 +02:00] [INF] [162] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 01:03:24.176 +02:00] [INF] [162] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-03 01:59:58.711 +02:00] [INF] [100] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-03 01:59:58.798 +02:00] [INF] [100] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-03 01:59:59.339 +02:00] [INF] [99] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-03 02:00:00.000 +02:00, which is 00:00:00.6609721 from now.
[2025-07-03 02:00:00.123 +02:00] [INF] [99] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-03 02:00:00.205 +02:00] [INF] [99] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-03 02:00:00.999 +02:00] [INF] [103] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-04 02:00:00.000 +02:00, which is 23:59:59.0002965 from now.
[2025-07-03 02:59:58.464 +02:00] [INF] [215] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-03 02:59:59.170 +02:00] [INF] [215] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-03 03:00:00.000 +02:00, which is 00:00:00.8299444 from now.
[2025-07-03 03:00:00.031 +02:00] [INF] [224] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-03 03:00:01.004 +02:00] [INF] [216] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-04 03:00:00.000 +02:00, which is 23:59:58.9960757 from now.
[2025-07-03 03:18:29.861 +02:00] [INF] [156] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-03 03:18:29.872 +02:00] [INF] [156] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-03 03:18:29.894 +02:00] [INF] [156] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-03 03:18:30.014 +02:00] [INF] [156] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-03 03:18:31.415 +02:00] [WRN] [156] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-03 03:18:32.198 +02:00] [WRN] [156] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-03 03:18:32.262 +02:00] [INF] [157] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 2 seconds
[2025-07-03 03:18:34.370 +02:00] [INF] [90] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-03 03:18:49.238 +02:00] [INF] [107] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-03 03:18:49.238 +02:00] [INF] [107] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-03 03:19:13.184 +02:00] [INF] [38] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 43 seconds
[2025-07-03 03:19:13.188 +02:00] [INF] [107] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-03 03:19:13.380 +02:00] [INF] [44] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-03 03:19:13.586 +02:00] [INF] [94] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-03 05:24:27.751 +02:00] [INF] [199] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 05:24:27.752 +02:00] [INF] [199] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 05:24:27.753 +02:00] [INF] [199] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-03 08:50:42.331 +02:00] [INF] [40] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" request
[2025-07-03 08:54:30.323 +02:00] [INF] [32] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-03 08:54:42.319 +02:00] [INF] [33] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-03 08:54:49.510 +02:00] [WRN] [33] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "172.19.0.1" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-07-03 08:54:49.518 +02:00] [INF] [33] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" closed
[2025-07-03 15:18:27.706 +02:00] [INF] [209] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar los subtítulos que faltan" Completed after 0 minute(s) and 0 seconds
[2025-07-03 15:18:27.800 +02:00] [INF] [212] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-03 15:18:27.856 +02:00] [INF] [218] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: Optimizing and vacuuming jellyfin.db...
[2025-07-03 15:18:27.879 +02:00] [INF] [211] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar registros" Completed after 0 minute(s) and 0 seconds
[2025-07-03 15:18:27.992 +02:00] [INF] [50] Emby.Server.Implementations.ScheduledTasks.TaskManager: "TasksRefreshChannels" Completed after 0 minute(s) and 0 seconds
[2025-07-03 15:18:28.095 +02:00] [INF] [207] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar letras faltantes" Completed after 0 minute(s) and 0 seconds
[2025-07-03 15:18:28.099 +02:00] [INF] [206] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Normalización de audio" Completed after 0 minute(s) and 0 seconds
[2025-07-03 15:18:28.480 +02:00] [INF] [218] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: jellyfin.db optimized successfully!
[2025-07-03 15:18:28.480 +02:00] [INF] [218] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Optimizar la base de datos" Completed after 0 minute(s) and 0 seconds
[2025-07-03 15:18:29.855 +02:00] [INF] [28] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 2 seconds
[2025-07-03 15:18:30.437 +02:00] [INF] [222] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Eliminar archivos temporales" Completed after 0 minute(s) and 2 seconds
[2025-07-03 15:18:31.272 +02:00] [INF] [206] Jellyfin.LiveTv.Guide.GuideManager: Refreshing guide with 7 days of guide data
[2025-07-03 15:18:31.277 +02:00] [INF] [206] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Refresh Guide" Completed after 0 minute(s) and 3 seconds
[2025-07-03 15:19:28.853 +02:00] [INF] [206] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-03 15:19:28.853 +02:00] [INF] [206] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-03 15:19:28.854 +02:00] [INF] [206] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-03 15:19:28.854 +02:00] [INF] [206] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-03 15:19:29.177 +02:00] [WRN] [206] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-03 15:19:29.978 +02:00] [WRN] [210] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-03 15:19:30.809 +02:00] [INF] [222] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 1 seconds
[2025-07-03 15:19:31.523 +02:00] [INF] [39] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-03 15:19:32.707 +02:00] [INF] [41] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-03 15:19:32.707 +02:00] [INF] [41] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-03 15:19:32.393 +02:00] [ERR] [3] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-03 15:19:33.215 +02:00] [INF] [41] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-03 15:19:33.216 +02:00] [INF] [41] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-03 15:20:12.655 +02:00] [INF] [131] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 43 seconds
[2025-07-03 15:20:12.658 +02:00] [INF] [30] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-03 15:20:12.848 +02:00] [INF] [41] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-03 15:20:13.048 +02:00] [INF] [149] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-03 22:36:00.515 +02:00] [INF] [207] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 22:36:01.191 +02:00] [INF] [207] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 22:36:01.950 +02:00] [INF] [207] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-03 22:36:35.035 +02:00] [INF] [211] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 22:36:35.050 +02:00] [INF] [211] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 22:36:35.050 +02:00] [INF] [211] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-03 23:40:29.919 +02:00] [INF] [157] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 23:40:29.920 +02:00] [INF] [157] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-03 23:40:29.921 +02:00] [INF] [157] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
