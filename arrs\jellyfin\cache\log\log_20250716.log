[2025-07-16 00:15:21.544 +02:00] [INF] [43] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Cocodrilo". Stopped at "3237104" ms
[2025-07-16 01:59:59.125 +02:00] [INF] [66] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-16 01:59:59.207 +02:00] [INF] [66] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-16 01:59:59.800 +02:00] [INF] [63] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-16 02:00:00.000 +02:00, which is 00:00:00.1997451 from now.
[2025-07-16 02:00:00.120 +02:00] [INF] [61] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-16 02:00:00.201 +02:00] [INF] [61] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-16 02:00:01.005 +02:00] [INF] [63] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-17 02:00:00.000 +02:00, which is 23:59:58.9945842 from now.
[2025-07-16 02:04:34.880 +02:00] [INF] [41] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-16 02:04:34.881 +02:00] [INF] [41] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-16 02:04:34.884 +02:00] [INF] [41] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-16 02:59:58.876 +02:00] [INF] [141] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-16 02:59:59.659 +02:00] [INF] [142] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-16 03:00:00.000 +02:00, which is 00:00:00.3402711 from now.
[2025-07-16 03:00:00.025 +02:00] [INF] [141] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-16 03:00:01.000 +02:00] [INF] [140] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-17 03:00:00.000 +02:00, which is 23:59:58.9992042 from now.
[2025-07-16 06:04:54.666 +02:00] [INF] [185] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-16 06:04:54.667 +02:00] [INF] [185] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-16 06:04:54.668 +02:00] [INF] [185] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-16 06:04:54.671 +02:00] [INF] [185] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-16 06:04:55.458 +02:00] [WRN] [185] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-16 06:04:55.652 +02:00] [INF] [188] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/07c34f0773574690a8841fa7c0f58566.png"
[2025-07-16 06:04:55.994 +02:00] [INF] [186] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 1 seconds
[2025-07-16 06:04:56.049 +02:00] [INF] [188] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/07c34f0773574690a8841fa7c0f58566.png"
[2025-07-16 06:04:56.654 +02:00] [WRN] [189] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-16 06:04:58.441 +02:00] [INF] [189] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-16 06:04:59.838 +02:00] [ERR] [184] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-16 06:05:02.565 +02:00] [INF] [186] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-16 06:05:02.565 +02:00] [INF] [186] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-16 06:05:23.266 +02:00] [INF] [186] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 28 seconds
[2025-07-16 06:05:23.269 +02:00] [INF] [185] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-16 06:05:23.420 +02:00] [INF] [181] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-16 06:05:23.601 +02:00] [INF] [188] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-16 11:09:47.486 +02:00] [INF] [24] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" request
[2025-07-16 11:10:00.004 +02:00] [INF] [24] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" request
[2025-07-16 11:10:36.772 +02:00] [WRN] [183] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "172.19.0.1" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-07-16 11:10:36.775 +02:00] [INF] [183] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" closed
[2025-07-16 11:11:08.444 +02:00] [INF] [183] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-16 12:05:39.811 +02:00] [INF] [43] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Resquebrajando a Cherry". Stopped at "3040193" ms
[2025-07-16 12:05:39.914 +02:00] [INF] [65] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-16 12:05:41.025 +02:00] [WRN] [187] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-07-16 13:08:26.943 +02:00] [INF] [145] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Vamos a echar una mano al chico". Stopped at "3436180" ms
[2025-07-16 13:08:27.132 +02:00] [INF] [43] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-16 13:08:28.212 +02:00] [WRN] [43] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-07-16 13:57:57.936 +02:00] [INF] [66] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Amor al estilo americano". Stopped at "1317627" ms
[2025-07-16 13:59:27.694 +02:00] [INF] [66] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-16 14:03:37.680 +02:00] [INF] [139] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Amor al estilo americano". Stopped at "1561695" ms
[2025-07-16 14:15:26.786 +02:00] [INF] [147] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" request
[2025-07-16 14:15:46.374 +02:00] [INF] [148] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-16 14:15:46.527 +02:00] [WRN] [64] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-07-16 14:16:47.017 +02:00] [INF] [146] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-16 14:16:59.020 +02:00] [INF] [147] Emby.Server.Implementations.Session.SessionWebSocketListener: Lost 1 WebSockets.
[2025-07-16 14:17:06.698 +02:00] [WRN] [63] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "172.19.0.1" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-07-16 14:17:06.703 +02:00] [INF] [63] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.19.0.1" closed
[2025-07-16 14:26:53.494 +02:00] [INF] [160] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Amor al estilo americano". Stopped at "2162341" ms
[2025-07-16 14:27:09.887 +02:00] [INF] [144] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-16 14:48:41.234 +02:00] [INF] [156] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Amor al estilo americano". Stopped at "3301205" ms
[2025-07-16 14:50:50.273 +02:00] [INF] [144] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-16 16:07:40.078 +02:00] [INF] [63] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Devuélvase al remitente". Stopped at "3151413" ms
[2025-07-16 16:07:40.140 +02:00] [INF] [64] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-16 16:07:41.287 +02:00] [WRN] [64] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-07-16 16:13:41.038 +02:00] [INF] [156] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-16 16:13:41.038 +02:00] [INF] [156] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-16 16:13:41.040 +02:00] [INF] [156] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-16 17:20:08.835 +02:00] [INF] [153] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Círculo de amigos". Stopped at "3098649" ms
[2025-07-16 18:04:52.639 +02:00] [INF] [154] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar los subtítulos que faltan" Completed after 0 minute(s) and 0 seconds
[2025-07-16 18:04:52.790 +02:00] [INF] [148] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar registros" Completed after 0 minute(s) and 0 seconds
[2025-07-16 18:04:52.849 +02:00] [INF] [144] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar letras faltantes" Completed after 0 minute(s) and 0 seconds
[2025-07-16 18:04:52.852 +02:00] [INF] [55] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-16 18:04:52.854 +02:00] [INF] [139] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Normalización de audio" Completed after 0 minute(s) and 0 seconds
[2025-07-16 18:04:52.976 +02:00] [INF] [148] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: Optimizing and vacuuming jellyfin.db...
[2025-07-16 18:04:53.009 +02:00] [INF] [61] Emby.Server.Implementations.ScheduledTasks.TaskManager: "TasksRefreshChannels" Completed after 0 minute(s) and 0 seconds
[2025-07-16 18:04:53.210 +02:00] [INF] [148] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: jellyfin.db optimized successfully!
[2025-07-16 18:04:53.210 +02:00] [INF] [148] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Optimizar la base de datos" Completed after 0 minute(s) and 0 seconds
[2025-07-16 18:04:54.619 +02:00] [INF] [147] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-07-16 18:04:55.269 +02:00] [INF] [154] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Eliminar archivos temporales" Completed after 0 minute(s) and 2 seconds
[2025-07-16 18:04:56.124 +02:00] [INF] [55] Jellyfin.LiveTv.Guide.GuideManager: Refreshing guide with 7 days of guide data
[2025-07-16 18:04:56.133 +02:00] [INF] [55] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Refresh Guide" Completed after 0 minute(s) and 3 seconds
[2025-07-16 18:05:54.333 +02:00] [INF] [55] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-16 18:05:54.333 +02:00] [INF] [55] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-16 18:05:54.334 +02:00] [INF] [55] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-16 18:05:54.335 +02:00] [INF] [55] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-16 18:05:54.718 +02:00] [WRN] [55] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-16 18:05:55.549 +02:00] [INF] [154] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 1 seconds
[2025-07-16 18:05:55.789 +02:00] [WRN] [55] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-16 18:05:57.144 +02:00] [INF] [61] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-16 18:05:57.967 +02:00] [ERR] [144] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-16 18:06:02.341 +02:00] [INF] [183] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-16 18:06:02.341 +02:00] [INF] [183] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-16 18:06:29.666 +02:00] [INF] [61] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 35 seconds
[2025-07-16 18:06:29.668 +02:00] [INF] [187] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-16 18:06:29.868 +02:00] [INF] [187] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-16 18:06:30.099 +02:00] [INF] [61] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
