{"sdk":{"name":"sentry.dotnet","version":"4.0.2"},"event_id":"02449ec9a79e47309cf0de472f3f483b","trace":{"trace_id":"b07e58b0972e4d599218ccd3136448b8","public_key":"40f1288c1b4d495cbafdb5c89f7f01be","release":"Radarr@5.26.2.10099-master","environment":"master"}}
{"type":"event","length":23586}
{"modules":{"System.Private.CoreLib":"*******","Radarr":"5.26.2.10099","System.Runtime":"*******","Radarr.Common":"5.26.2.10099","System.Collections":"*******","System.Net.Primitives":"*******","Microsoft.Win32.Primitives":"*******","Radarr.Host":"5.26.2.10099","Microsoft.Extensions.Hosting.Abstractions":"*******","NLog":"*******","netstandard":"*******","System.Console":"*******","Microsoft.AspNetCore.Connections.Abstractions":"*******","NLog.Layouts.ClefJsonLayout":"*******","System.ComponentModel":"*******","System.Threading":"*******","System.Net.Mail":"*******","System.Private.Uri":"*******","System.Linq":"*******","System.Threading.Thread":"*******","System.IO.FileSystem.Watcher":"*******","System.Diagnostics.Process":"*******","System.ComponentModel.Primitives":"*******","System.Memory":"*******","Sentry":"*******","System.IO.Compression":"*******","System.Text.Json":"*******","System.Text.RegularExpressions":"*******","System.Reflection.Emit.ILGeneration":"*******","System.Reflection.Emit.Lightweight":"*******","System.Reflection.Primitives":"*******","System.Security.Cryptography.Algorithms":"*******","System.Security.Cryptography.Primitives":"*******","System.Collections.Concurrent":"*******","System.Net.Http":"*******","System.Runtime.InteropServices.RuntimeInformation":"*******","System.Diagnostics.DiagnosticSource":"*******","System.ObjectModel":"*******","Radarr.Core":"5.26.2.10099","System.Text.Encoding.CodePages":"*******","Microsoft.Extensions.Configuration.Abstractions":"*******","System.Data.SQLite":"*********","System.Data.Common":"*******","System.Transactions.Local":"*******","Npgsql":"********","Microsoft.Extensions.Hosting":"*******","Microsoft.Extensions.DependencyInjection.Abstractions":"*******","DryIoc":"*******","DryIoc.Microsoft.DependencyInjection":"*******","Microsoft.Extensions.Hosting.WindowsServices":"*******","Microsoft.Extensions.Configuration":"*******","Microsoft.Extensions.Configuration.Xml":"*******","Microsoft.Extensions.Configuration.EnvironmentVariables":"*******","Microsoft.Extensions.Configuration.FileExtensions":"*******","Microsoft.Extensions.FileProviders.Abstractions":"*******","Microsoft.Extensions.FileProviders.Physical":"*******","Microsoft.Extensions.Primitives":"*******","System.Xml.ReaderWriter":"*******","System.Private.Xml":"*******","System.Security.Cryptography.Xml":"*******","System.Text.Encoding.Extensions":"*******","Microsoft.Extensions.Configuration.Binder":"*******","Microsoft.AspNetCore.Hosting.Abstractions":"*******","Microsoft.AspNetCore.Hosting":"*******","System.ComponentModel.TypeConverter":"*******","Microsoft.Extensions.DependencyInjection":"*******","System.Diagnostics.StackTrace":"*******","Microsoft.AspNetCore.Server.Kestrel.Core":"*******","Microsoft.AspNetCore.Server.Kestrel":"*******","Microsoft.AspNetCore.Server.Kestrel.Transport.Quic":"*******","System.Net.Quic":"*******","System.Diagnostics.Tracing":"*******","Microsoft.Extensions.Options":"*******","Microsoft.Extensions.Logging":"*******","Microsoft.Extensions.Logging.Abstractions":"*******","Microsoft.Extensions.Options.ConfigurationExtensions":"*******","Microsoft.AspNetCore.Http.Abstractions":"*******","Microsoft.Extensions.Features":"*******","Microsoft.AspNetCore.Http":"*******","Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets":"*******","Microsoft.AspNetCore.Hosting.Server.Abstractions":"*******","Microsoft.AspNetCore.HttpOverrides":"*******","Microsoft.AspNetCore.Routing":"*******","Microsoft.AspNetCore.Routing.Abstractions":"*******","Microsoft.AspNetCore.ResponseCompression":"*******","Microsoft.AspNetCore.Cors":"*******","Microsoft.AspNetCore.Mvc.Core":"*******","Microsoft.AspNetCore.Mvc":"*******","Radarr.Api.V3":"5.26.2.10099","Microsoft.AspNetCore.Mvc.ViewFeatures":"*******","Microsoft.AspNetCore.Mvc.Abstractions":"*******","Radarr.Http":"5.26.2.10099","Swashbuckle.AspNetCore.SwaggerGen":"*******","Microsoft.AspNetCore.SignalR":"*******","Microsoft.AspNetCore.SignalR.Core":"*******","Microsoft.AspNetCore.SignalR.Common":"*******","Microsoft.AspNetCore.SignalR.Protocols.Json":"*******","Microsoft.AspNetCore.DataProtection":"*******","Microsoft.AspNetCore.Authorization":"*******","Microsoft.AspNetCore.Authorization.Policy":"*******","Microsoft.AspNetCore.Authentication":"*******","Microsoft.AspNetCore.Authentication.Abstractions":"*******","NLog.Extensions.Logging":"*******","Microsoft.Extensions.ObjectPool":"*******","System.Runtime.Loader":"*******","Microsoft.AspNetCore.Mvc.ApiExplorer":"*******","Microsoft.AspNetCore.Authentication.Core":"*******","System.Security.Claims":"*******","Microsoft.AspNetCore.Mvc.Cors":"*******","Microsoft.AspNetCore.Mvc.DataAnnotations":"*******","FluentValidation":"9.0.0.0","Microsoft.AspNetCore.Metadata":"*******","Swashbuckle.AspNetCore.Swagger":"*******","Microsoft.OpenApi":"1.6.14.0","Microsoft.AspNetCore.WebSockets":"*******","Microsoft.AspNetCore.Http.Connections":"*******","Microsoft.AspNetCore.DataProtection.Abstractions":"*******","Microsoft.AspNetCore.Cryptography.Internal":"*******","Microsoft.AspNetCore.Authentication.Cookies":"*******","Microsoft.Extensions.WebEncoders":"*******","System.Text.Encodings.Web":"*******","System.Runtime.InteropServices":"*******","Radarr.SignalR":"5.26.2.10099","Radarr.Mono":"5.26.2.10099","System.Linq.Expressions":"*******","System.Security.Cryptography.X509Certificates":"*******","System.Collections.Immutable":"*******","Equ":"*******","FFMpegCore":"*******","Newtonsoft.Json":"********","System.Runtime.Numerics":"*******","FluentMigrator":"*******","FluentMigrator.Abstractions":"*******","FluentMigrator.Runner.SQLite":"*******","FluentMigrator.Runner.Core":"*******","Dapper":"*******","System.Collections.Specialized":"*******","System.Net.Requests":"*******","System.IO.FileSystem.DriveInfo":"*******","System.Net.Sockets":"*******","System.IO.Pipelines":"*******","Mono.Posix.NETStandard":"*******","System.ServiceProcess.ServiceController":"*******","System.Collections.NonGeneric":"*******","System.Xml.XDocument":"*******","System.Private.Xml.Linq":"*******","Microsoft.AspNetCore.Diagnostics":"*******","FluentMigrator.Runner":"*******","FluentMigrator.Runner.Postgres":"*******","System.Threading.ThreadPool":"*******","System.ComponentModel.Annotations":"*******","System.Diagnostics.TraceSource":"*******","System.Net.ServicePoint":"*******","System.Net.Security":"*******","Instances":"*******","System.IO.Pipes":"*******","System.Runtime.Intrinsics":"*******","System.Numerics.Vectors":"*******","System.Runtime.CompilerServices.Unsafe":"*******","SixLabors.ImageSharp":"*******","Microsoft.AspNetCore.Http.Connections.Common":"*******","Microsoft.Extensions.Localization.Abstractions":"*******","Microsoft.Net.Http.Headers":"*******","Microsoft.AspNetCore.Http.Features":"*******","Microsoft.AspNetCore.Http.Extensions":"*******","Microsoft.AspNetCore.StaticFiles":"*******","Microsoft.CSharp":"*******","Microsoft.AspNetCore.WebUtilities":"*******","System.Net.WebSockets":"*******","System.Net.NameResolution":"*******","System.Net.NetworkInformation":"*******","System.Security.Cryptography.Encoding":"*******","System.Formats.Asn1":"*******","System.Security.Cryptography.OpenSsl":"*******","System.Threading.Channels":"*******","System.IO.Compression.Brotli":"*******","System.Runtime.Serialization.Formatters":"*******","System.Runtime.Serialization.Primitives":"*******","System.Net.WebProxy":"*******","Diacritical":"*******","System.Security.Principal.Windows":"*******","System.Reflection.Metadata":"*******","System.IO.MemoryMappedFiles":"*******","Polly.Core":"*******","Microsoft.Bcl.TimeProvider":"*******","MonoTorrent":"*******","Microsoft.AspNetCore.Diagnostics.Abstractions":"*******"},"event_id":"02449ec9a79e47309cf0de472f3f483b","timestamp":"2025-07-16T20:31:58.4019003+00:00","logentry":{"message":"Request Failed. POST /api/v3/release"},"logger":"RadarrErrorPipeline","platform":"csharp","release":"Radarr@5.26.2.10099-master","exception":{"values":[{"type":"NzbDrone.Core.Download.Clients.DownloadClientException","value":"Download client failed to add torrent","module":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","thread_id":39,"stacktrace":{"frames":[{"function":"async Task ExceptionHandlerMiddleware.Invoke(HttpContext context)\u002BAwaited(?)","in_app":false,"package":"Microsoft.AspNetCore.Diagnostics, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x133","addr_mode":"rel:8","function_id":"0x194"},{"function":"async Task AuthenticationMiddleware.Invoke(HttpContext context)","in_app":false,"package":"Microsoft.AspNetCore.Authentication, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x401","addr_mode":"rel:7","function_id":"0x132"},{"function":"async Task AuthorizationMiddleware.Invoke(HttpContext context)","in_app":false,"package":"Microsoft.AspNetCore.Authorization.Policy, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x448","addr_mode":"rel:6","function_id":"0x43"},{"function":"async Task AuthorizationMiddlewareResultHandler.HandleAsync(RequestDelegate next, HttpContext context, AuthorizationPolicy policy, PolicyAuthorizationResult authorizeResult)","in_app":false,"package":"Microsoft.AspNetCore.Authorization.Policy, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x338","addr_mode":"rel:6","function_id":"0x45"},{"function":"async Task ResponseCompressionMiddleware.InvokeCore(HttpContext context)","in_app":false,"package":"Microsoft.AspNetCore.ResponseCompression, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x1e1","addr_mode":"rel:5","function_id":"0x74"},{"filename":"./Radarr.Http/Middleware/VersionMiddleware.cs","function":"async Task VersionMiddleware.InvokeAsync(HttpContext context)","lineno":29,"colno":9,"in_app":true,"package":"Radarr.Http, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0xf6","addr_mode":"rel:4","function_id":"0x192"},{"filename":"./Radarr.Http/Middleware/UrlBaseMiddleware.cs","function":"async Task UrlBaseMiddleware.InvokeAsync(HttpContext context)","lineno":29,"colno":9,"in_app":true,"package":"Radarr.Http, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x14b","addr_mode":"rel:4","function_id":"0x190"},{"filename":"./Radarr.Http/Middleware/StartingUpMiddleware.cs","function":"async Task StartingUpMiddleware.InvokeAsync(HttpContext context)","lineno":38,"colno":9,"in_app":true,"package":"Radarr.Http, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x1b2","addr_mode":"rel:4","function_id":"0x18e"},{"filename":"./Radarr.Http/Middleware/CacheHeaderMiddleware.cs","function":"async Task CacheHeaderMiddleware.InvokeAsync(HttpContext context)","lineno":33,"colno":9,"in_app":true,"package":"Radarr.Http, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x103","addr_mode":"rel:4","function_id":"0x188"},{"filename":"./Radarr.Http/Middleware/IfModifiedMiddleware.cs","function":"async Task IfModifiedMiddleware.InvokeAsync(HttpContext context)","lineno":41,"colno":9,"in_app":true,"package":"Radarr.Http, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x15c","addr_mode":"rel:4","function_id":"0x18a"},{"filename":"./Radarr.Http/Middleware/BufferingMiddleware.cs","function":"async Task BufferingMiddleware.InvokeAsync(HttpContext context)","lineno":28,"colno":9,"in_app":true,"package":"Radarr.Http, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0xdd","addr_mode":"rel:4","function_id":"0x186"},{"function":"async Task EndpointMiddleware.Invoke(HttpContext httpContext)\u002BAwaitRequestTask(?)","in_app":false,"package":"Microsoft.AspNetCore.Routing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0xa9","addr_mode":"rel:3","function_id":"0x527"},{"function":"async Task ResourceInvoker.InvokeAsync()\u002BAwaited(?) x 2","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x145","addr_mode":"rel:2","function_id":"0x101e"},{"function":"async Task ResourceInvoker.InvokeFilterPipelineAsync()\u002BAwaited(?)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x117","addr_mode":"rel:2","function_id":"0x1022"},{"function":"Task ResourceInvoker.Next(ref State next, ref Scope scope, ref object state, ref bool isCompleted)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x84f","addr_mode":"rel:2","function_id":"0xa97"},{"function":"void ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x15","addr_mode":"rel:2","function_id":"0xaa0"},{"function":"async Task ResourceInvoker.InvokeNextResourceFilter()\u002BAwaited(?)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x6a","addr_mode":"rel:2","function_id":"0x1026"},{"function":"async Task ControllerActionInvoker.InvokeInnerFilterAsync()\u002BAwaited(?)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x117","addr_mode":"rel:2","function_id":"0xffa"},{"function":"Task ControllerActionInvoker.Next(ref State next, ref Scope scope, ref object state, ref bool isCompleted)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x3c1","addr_mode":"rel:2","function_id":"0x9db"},{"function":"void ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x2f","addr_mode":"rel:2","function_id":"0x9e0"},{"function":"async Task ControllerActionInvoker.InvokeNextActionFilterAsync()\u002BAwaited(?)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x161","addr_mode":"rel:2","function_id":"0xffc"},{"function":"async Task ControllerActionInvoker.InvokeActionMethodAsync()\u002BAwaited(?)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0xae","addr_mode":"rel:2","function_id":"0xff6"},{"function":"async ValueTask\u003CIActionResult\u003E AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, object controller, object[] arguments)","in_app":false,"package":"Microsoft.AspNetCore.Mvc.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60","instruction_addr":"0x65","addr_mode":"rel:2","function_id":"0x110f"},{"function":"object lambda_method388(Closure, object)","in_app":false},{"filename":"./Radarr.Api.V3/Indexers/ReleaseController.cs","function":"async Task\u003Cobject\u003E ReleaseController.DownloadRelease(ReleaseResource release)","lineno":116,"colno":17,"in_app":true,"package":"Radarr.Api.V3, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x35a","addr_mode":"rel:1","function_id":"0x902"},{"filename":"./Radarr.Core/Download/DownloadService.cs","function":"async Task DownloadService.DownloadReport(RemoteMovie remoteMovie, int? downloadClientId)","lineno":63,"colno":13,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x105","addr_mode":"rel:0","function_id":"0x3b4e"},{"filename":"./Radarr.Core/Download/DownloadService.cs","function":"async Task DownloadService.DownloadReport(RemoteMovie remoteMovie, IDownloadClient downloadClient)","lineno":97,"colno":17,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x2a6","addr_mode":"rel:0","function_id":"0x3b4c"},{"filename":"./Radarr.Core/Download/TorrentClientBase.cs","function":"async Task\u003Cstring\u003E TorrentClientBase\u003CTSettings\u003E.Download(RemoteMovie remoteMovie, IIndexer indexer)","lineno":124,"colno":21,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x2a0","addr_mode":"rel:0","function_id":"0x3b61"},{"filename":"./Radarr.Core/Download/TorrentClientBase.cs","function":"async Task\u003Cstring\u003E TorrentClientBase\u003CTSettings\u003E.DownloadFromWebUrl(RemoteMovie remoteMovie, IIndexer indexer, string torrentUrl)","lineno":204,"colno":13,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x434","addr_mode":"rel:0","function_id":"0x3b63"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrent.cs","function":"string QBittorrent.AddFromTorrentFile(RemoteMovie remoteMovie, string hash, string filename, byte[] fileContent)","lineno":141,"colno":13,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0xa2","addr_mode":"rel:0","function_id":"0x253e"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrentProxyV2.cs","function":"void QBittorrentProxyV2.AddTorrentFromFile(string fileName, byte[] fileContent, TorrentSeedConfiguration seedConfiguration, QBittorrentSettings settings)","lineno":185,"colno":17,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x5f","addr_mode":"rel:0","function_id":"0x25a2"}]},"mechanism":{"type":"Logger.Fatal","description":"Logger.Fatal was called","handled":false}}]},"level":"fatal","request":{},"contexts":{"Current Culture":{"display_name":"Invariant Language (Invariant Country)","calendar":"GregorianCalendar"},"Dynamic Code":{"Compiled":true,"Supported":true},"Memory Info":{"allocated_bytes":11416927192,"fragmented_bytes":32092360,"heap_size_bytes":73408664,"high_memory_load_threshold_bytes":2745980928,"total_available_memory_bytes":3051089920,"memory_load_bytes":1586566758,"total_committed_bytes":86614016,"promoted_bytes":409608,"index":1286,"finalization_pending_count":75,"compacted":true,"concurrent":false,"pause_durations":[2.461,0]},"ThreadPool Info":{"min_worker_threads":2,"min_completion_port_threads":2,"max_worker_threads":32767,"max_completion_port_threads":1000,"available_worker_threads":32766,"available_completion_port_threads":1000},"app":{"type":"app","app_start_time":"2025-07-15T16:03:18.0962547+00:00"},"device":{"type":"device","timezone":"Europe/Madrid","timezone_display_name":"(UTC\u002B01:00) Central European Time (Madrid)","boot_time":"2025-07-15T16:02:03.8502174+00:00"},"os":{"type":"os","raw_description":"Linux ********-microsoft-standard-WSL2 #1 SMP PREEMPT_DYNAMIC Thu Jun  5 18:30:46 UTC 2025"},"runtime":{"type":"runtime","name":".NET","version":"6.0.35","raw_description":".NET 6.0.35","identifier":"alpine.3.22-x64"},"trace":{"type":"trace","span_id":"9dc55f35aca6a02d","trace_id":"b07e58b0972e4d599218ccd3136448b8"}},"user":{"ip_address":"{{auto}}"},"environment":"master","sdk":{"packages":[{"name":"nuget:sentry.dotnet","version":"4.0.2"}],"name":"sentry.dotnet","version":"4.0.2"},"fingerprint":["Fatal","RadarrErrorPipeline","Request Failed. {0} {1}","NzbDrone.Core.Download.Clients.DownloadClientException","Void AddTorrentFromFile(System.String, Byte[], NzbDrone.Core.Download.Clients.TorrentSeedConfiguration, NzbDrone.Core.Download.Clients.QBittorrent.QBittorrentSettings)"],"breadcrumbs":[{"timestamp":"2025-07-16T20:31:58.401Z","message":"Request Failed. POST /api/v3/release","category":"RadarrErrorPipeline","level":"critical"}],"debug_meta":{"images":[{"type":"pe_dotnet","debug_id":"f49236c8-8051-4d10-824e-8134a09765f3-91239eab","debug_checksum":"SHA256:c83692f45180101d024e8134a09765f3ab9e231186d8e6009587685e09aeb583","debug_file":"D:\\a\\1\\s\\_temp\\obj\\Radarr.Core\\Release\\net6.0\\linux-musl-x64\\Radarr.Core.pdb","code_id":"FFC0F60E252000","code_file":"/app/radarr/bin/Radarr.Core.dll"},{"type":"pe_dotnet","debug_id":"d36294e5-993b-4203-8993-9e2aab625f48-a388dacf","debug_checksum":"SHA256:e59462d33b99033209939e2aab625f48cfda88a398e101e77a8844442fc14f4f","debug_file":"D:\\a\\1\\s\\_temp\\obj\\Radarr.Api.V3\\Release\\net6.0\\linux-musl-x64\\Radarr.Api.V3.pdb","code_id":"8A28C45458000","code_file":"/app/radarr/bin/Radarr.Api.V3.dll"},{"type":"pe_dotnet","debug_id":"94f7d4ed-2841-477a-ae08-ff409abf2b99-91bbe76a","debug_checksum":"SHA256:edd4f79441287a97ae08ff409abf2b996ae7bb11ddf5b96fb33555fdd11f4e65","debug_file":"/_/artifacts/obj/Microsoft.AspNetCore.Mvc.Core/Release/net6.0/Microsoft.AspNetCore.Mvc.Core.pdb","code_id":"F607E3FC1fe400","code_file":"/app/radarr/bin/Microsoft.AspNetCore.Mvc.Core.dll"},{"type":"pe_dotnet","debug_id":"9d790c06-f0e6-46b5-942c-a66103be51c6-e97235f3","debug_checksum":"SHA256:060c799de6f0b566942ca66103be51c6f3357269155a38b20b46ffc2a016beb9","debug_file":"/_/artifacts/obj/Microsoft.AspNetCore.Routing/Release/net6.0/Microsoft.AspNetCore.Routing.pdb","code_id":"810110BEe3800","code_file":"/app/radarr/bin/Microsoft.AspNetCore.Routing.dll"},{"type":"pe_dotnet","debug_id":"903bc27d-eae3-47c8-a1f4-35c43d8dbe3d-a2425bbf","debug_checksum":"SHA256:7dc23b90e3eac817e1f435c43d8dbe3dbf5b42a2b4fc49dd597d4e930da6d7fe","debug_file":"D:\\a\\1\\s\\_temp\\obj\\Radarr.Http\\Release\\net6.0\\linux-musl-x64\\Radarr.Http.pdb","code_id":"8070F0A51c000","code_file":"/app/radarr/bin/Radarr.Http.dll"},{"type":"pe_dotnet","debug_id":"3803f5b5-f699-4bfc-a4c4-31389f5c7e46-8336923c","debug_checksum":"SHA256:b5f5033899f6fc0b24c431389f5c7e463c9236035e46932d9de99d62d9a52db5","debug_file":"/_/artifacts/obj/Microsoft.AspNetCore.ResponseCompression/Release/net6.0/Microsoft.AspNetCore.ResponseCompression.pdb","code_id":"FD0247883fc00","code_file":"/app/radarr/bin/Microsoft.AspNetCore.ResponseCompression.dll"},{"type":"pe_dotnet","debug_id":"2024d9ad-8418-4446-b217-73cb907f491e-8300f216","debug_checksum":"SHA256:add9242018844604321773cb907f491e16f20083e940e1ff33a979c9796f5afd","debug_file":"/_/artifacts/obj/Microsoft.AspNetCore.Authorization.Policy/Release/net6.0/Microsoft.AspNetCore.Authorization.Policy.pdb","code_id":"DA17D5BD3ba00","code_file":"/app/radarr/bin/Microsoft.AspNetCore.Authorization.Policy.dll"},{"type":"pe_dotnet","debug_id":"f8438e32-28ff-4203-9431-0f74ba5a380f-9cabdafb","debug_checksum":"SHA256:328e43f8ff2803d2d4310f74ba5a380ffbdaab9cc6db0a8831d2ddd8f87887fa","debug_file":"/_/artifacts/obj/Microsoft.AspNetCore.Authentication/Release/net6.0/Microsoft.AspNetCore.Authentication.pdb","code_id":"F368C6C851600","code_file":"/app/radarr/bin/Microsoft.AspNetCore.Authentication.dll"},{"type":"pe_dotnet","debug_id":"e78e40ae-790d-4394-8252-0793c91b72aa-a8a8682d","debug_checksum":"SHA256:ae408ee70d7994d382520793c91b72aa2d68a8289347e518e552db210e998b70","debug_file":"/_/artifacts/obj/Microsoft.AspNetCore.Diagnostics/Release/net6.0/Microsoft.AspNetCore.Diagnostics.pdb","code_id":"F7DA14618f200","code_file":"/app/radarr/bin/Microsoft.AspNetCore.Diagnostics.dll"}]}}
