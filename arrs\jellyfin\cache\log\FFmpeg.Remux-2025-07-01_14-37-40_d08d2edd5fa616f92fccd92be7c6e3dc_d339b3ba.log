{"Protocol":0,"Id":"d08d2edd5fa616f92fccd92be7c6e3dc","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 3/<PERSON> and <PERSON><PERSON><PERSON> - S03E01 - The Rickshank Rickdemption HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":295416756,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S03E01 - The Rickshank Rickdemption HDTV-720p","IsRemote":false,"ETag":"81c6460caac52557adc6ebca560677f9","RunTimeTicks":13431040000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1328885,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":105,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":131,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1759606,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E01 - The Rickshank Rickdemption HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/522ce1091f000453e3daeb7e58f1196c%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/522ce1091f000453e3daeb7e58f1196c.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x5b0ce4b86840] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E01 - The Rickshank Rickdemption HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Cadena Rickpetua
    date            : 2024
    encoder         : Lavf61.9.106
    description     : La familia se ocupa de sus problemas en este episodio  ¿Qué hará Rick? Este es un episodio real, bro.
    show            : Rick y Morty
    episode_id      : 1
    season_number   : 3
  Duration: 00:22:23.10, start: 0.000000, bitrate: 1759 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1328 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x5b0ce4bf5740] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1328 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c0.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c1.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c2.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c3.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c4.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c5.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c6.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c7.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c8.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c9.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c10.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c11.ts' for writing
size=N/A time=00:00:24.12 bitrate=N/A speed=24.1x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c12.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c13.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c14.ts' for writing
size=N/A time=00:00:44.60 bitrate=N/A speed=29.7x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c15.ts' for writing
size=N/A time=00:00:48.12 bitrate=N/A speed=24.1x    
size=N/A time=00:00:49.53 bitrate=N/A speed=19.7x    
size=N/A time=00:00:53.24 bitrate=N/A speed=17.6x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c16.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c17.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c18.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c19.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c20.ts' for writing
size=N/A time=00:01:21.85 bitrate=N/A speed=23.3x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c21.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c22.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c23.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c24.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c25.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c26.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c27.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c28.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c29.ts' for writing
size=N/A time=00:02:18.01 bitrate=N/A speed=34.3x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c30.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c31.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c32.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c33.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c34.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c35.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c36.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c37.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c38.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c39.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c40.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c41.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c42.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c43.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c44.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c45.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c46.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c47.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c48.ts' for writing
size=N/A time=00:04:10.75 bitrate=N/A speed=55.5x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c49.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c50.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c51.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c52.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c53.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c54.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c55.ts' for writing
size=N/A time=00:04:55.36 bitrate=N/A speed=58.9x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c56.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c57.ts' for writing
size=N/A time=00:05:03.80 bitrate=N/A speed=55.1x    
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c58.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c59.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c60.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c61.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c62.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c63.ts' for writing
[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c64.ts' for writing


[q] command received. Exiting.

[hls @ 0x5b0ce4bd9a80] Opening '/cache/transcodes/522ce1091f000453e3daeb7e58f1196c65.ts' for writing
[out#0/hls @ 0x5b0ce4bf5740] video:56605KiB audio:6147KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:05:45.31 bitrate=N/A speed=57.2x    