{"category": "<PERSON><PERSON><PERSON>", "changelog": "- Improved null handling for tvdb provider (#207) @systemcrash\n\n### New features and improvements ###\n- Added missing season type 'alttwo' (#200) @Kianda\n\n### Bug Fixes ###\n- Handle empty ParentIndexNumber (#209) @Shadowghost", "description": "Get TV metadata from TheTvdb\n", "guid": "a677c0da-fac5-4cde-941a-7134223f14c8", "name": "TheTVDB", "overview": "Get TV metadata from TheTvdb", "owner": "jellyfin", "targetAbi": "*********", "timestamp": "2025-02-25T03:41:03.0000000Z", "version": "********", "status": "Active", "autoUpdate": true, "imagePath": "/config/plugins/TheTVDB_********/jellyfin-plugin-tvdb.png", "assemblies": []}