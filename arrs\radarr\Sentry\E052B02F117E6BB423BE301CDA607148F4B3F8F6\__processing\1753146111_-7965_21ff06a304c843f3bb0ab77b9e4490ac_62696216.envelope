{"sdk":{"name":"sentry.dotnet","version":"4.0.2"},"event_id":"21ff06a304c843f3bb0ab77b9e4490ac","trace":{"trace_id":"9129e863a7db42ada35106dd8e0a26b5","public_key":"40f1288c1b4d495cbafdb5c89f7f01be","release":"Radarr@5.26.2.10099-master","environment":"master"}}
{"type":"event","length":27561}
{"modules":{"System.Private.CoreLib":"*******","Radarr":"5.26.2.10099","System.Runtime":"*******","Radarr.Common":"5.26.2.10099","System.Collections":"*******","System.Net.Primitives":"*******","Microsoft.Win32.Primitives":"*******","Radarr.Host":"5.26.2.10099","Microsoft.Extensions.Hosting.Abstractions":"*******","NLog":"*******","netstandard":"*******","System.Console":"*******","Microsoft.AspNetCore.Connections.Abstractions":"*******","NLog.Layouts.ClefJsonLayout":"*******","System.ComponentModel":"*******","System.Threading":"*******","System.Net.Mail":"*******","System.Private.Uri":"*******","System.Linq":"*******","System.Threading.Thread":"*******","System.IO.FileSystem.Watcher":"*******","System.Diagnostics.Process":"*******","System.ComponentModel.Primitives":"*******","System.Memory":"*******","Sentry":"*******","System.IO.Compression":"*******","System.Text.Json":"*******","System.Text.RegularExpressions":"*******","System.Reflection.Emit.ILGeneration":"*******","System.Reflection.Emit.Lightweight":"*******","System.Reflection.Primitives":"*******","System.Security.Cryptography.Algorithms":"*******","System.Security.Cryptography.Primitives":"*******","System.Collections.Concurrent":"*******","System.Net.Http":"*******","System.Runtime.InteropServices.RuntimeInformation":"*******","System.Diagnostics.DiagnosticSource":"*******","System.ObjectModel":"*******","Radarr.Core":"5.26.2.10099","System.Text.Encoding.CodePages":"*******","Microsoft.Extensions.Configuration.Abstractions":"*******","System.Data.SQLite":"*********","System.Data.Common":"*******","System.Transactions.Local":"*******","Npgsql":"********","Microsoft.Extensions.Hosting":"*******","Microsoft.Extensions.DependencyInjection.Abstractions":"*******","DryIoc":"*******","DryIoc.Microsoft.DependencyInjection":"*******","Microsoft.Extensions.Hosting.WindowsServices":"*******","Microsoft.Extensions.Configuration":"*******","Microsoft.Extensions.Configuration.Xml":"*******","Microsoft.Extensions.Configuration.EnvironmentVariables":"*******","Microsoft.Extensions.Configuration.FileExtensions":"*******","Microsoft.Extensions.FileProviders.Abstractions":"*******","Microsoft.Extensions.FileProviders.Physical":"*******","Microsoft.Extensions.Primitives":"*******","System.Xml.ReaderWriter":"*******","System.Private.Xml":"*******","System.Security.Cryptography.Xml":"*******","System.Text.Encoding.Extensions":"*******","Microsoft.Extensions.Configuration.Binder":"*******","Microsoft.AspNetCore.Hosting.Abstractions":"*******","Microsoft.AspNetCore.Hosting":"*******","System.ComponentModel.TypeConverter":"*******","Microsoft.Extensions.DependencyInjection":"*******","System.Diagnostics.StackTrace":"*******","Microsoft.AspNetCore.Server.Kestrel.Core":"*******","Microsoft.AspNetCore.Server.Kestrel":"*******","Microsoft.AspNetCore.Server.Kestrel.Transport.Quic":"*******","System.Net.Quic":"*******","System.Diagnostics.Tracing":"*******","Microsoft.Extensions.Options":"*******","Microsoft.Extensions.Logging":"*******","Microsoft.Extensions.Logging.Abstractions":"*******","Microsoft.Extensions.Options.ConfigurationExtensions":"*******","Microsoft.AspNetCore.Http.Abstractions":"*******","Microsoft.Extensions.Features":"*******","Microsoft.AspNetCore.Http":"*******","Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets":"*******","Microsoft.AspNetCore.Hosting.Server.Abstractions":"*******","Microsoft.AspNetCore.HttpOverrides":"*******","Microsoft.AspNetCore.Routing":"*******","Microsoft.AspNetCore.Routing.Abstractions":"*******","Microsoft.AspNetCore.ResponseCompression":"*******","Microsoft.AspNetCore.Cors":"*******","Microsoft.AspNetCore.Mvc.Core":"*******","Microsoft.AspNetCore.Mvc":"*******","Radarr.Api.V3":"5.26.2.10099","Microsoft.AspNetCore.Mvc.ViewFeatures":"*******","Microsoft.AspNetCore.Mvc.Abstractions":"*******","Radarr.Http":"5.26.2.10099","Swashbuckle.AspNetCore.SwaggerGen":"*******","Microsoft.AspNetCore.SignalR":"*******","Microsoft.AspNetCore.SignalR.Core":"*******","Microsoft.AspNetCore.SignalR.Common":"*******","Microsoft.AspNetCore.SignalR.Protocols.Json":"*******","Microsoft.AspNetCore.DataProtection":"*******","Microsoft.AspNetCore.Authorization":"*******","Microsoft.AspNetCore.Authorization.Policy":"*******","Microsoft.AspNetCore.Authentication":"*******","Microsoft.AspNetCore.Authentication.Abstractions":"*******","NLog.Extensions.Logging":"*******","Microsoft.Extensions.ObjectPool":"*******","System.Runtime.Loader":"*******","Microsoft.AspNetCore.Mvc.ApiExplorer":"*******","Microsoft.AspNetCore.Authentication.Core":"*******","System.Security.Claims":"*******","Microsoft.AspNetCore.Mvc.Cors":"*******","Microsoft.AspNetCore.Mvc.DataAnnotations":"*******","FluentValidation":"9.0.0.0","Microsoft.AspNetCore.Metadata":"*******","Swashbuckle.AspNetCore.Swagger":"*******","Microsoft.OpenApi":"1.6.14.0","Microsoft.AspNetCore.WebSockets":"*******","Microsoft.AspNetCore.Http.Connections":"*******","Microsoft.AspNetCore.DataProtection.Abstractions":"*******","Microsoft.AspNetCore.Cryptography.Internal":"*******","Microsoft.AspNetCore.Authentication.Cookies":"*******","Microsoft.Extensions.WebEncoders":"*******","System.Text.Encodings.Web":"*******","System.Runtime.InteropServices":"*******","Radarr.SignalR":"5.26.2.10099","Radarr.Mono":"5.26.2.10099","System.Linq.Expressions":"*******","System.Security.Cryptography.X509Certificates":"*******","System.Collections.Immutable":"*******","Equ":"*******","FFMpegCore":"*******","Newtonsoft.Json":"********","System.Runtime.Numerics":"*******","FluentMigrator":"*******","FluentMigrator.Abstractions":"*******","FluentMigrator.Runner.SQLite":"*******","FluentMigrator.Runner.Core":"*******","Dapper":"*******","System.Collections.Specialized":"*******","System.Net.Requests":"*******","System.IO.FileSystem.DriveInfo":"*******","System.Net.Sockets":"*******","System.IO.Pipelines":"*******","Mono.Posix.NETStandard":"*******","System.ServiceProcess.ServiceController":"*******","System.Collections.NonGeneric":"*******","System.Xml.XDocument":"*******","System.Private.Xml.Linq":"*******","Microsoft.AspNetCore.Diagnostics":"*******","FluentMigrator.Runner":"*******","FluentMigrator.Runner.Postgres":"*******","System.Threading.ThreadPool":"*******","System.ComponentModel.Annotations":"*******","System.Diagnostics.TraceSource":"*******","System.Net.ServicePoint":"*******","System.Net.Security":"*******","Instances":"*******","System.IO.Pipes":"*******","System.Runtime.Intrinsics":"*******","System.Numerics.Vectors":"*******","System.Runtime.CompilerServices.Unsafe":"*******","SixLabors.ImageSharp":"*******","Microsoft.AspNetCore.Http.Connections.Common":"*******","Microsoft.Extensions.Localization.Abstractions":"*******","Microsoft.Net.Http.Headers":"*******","Microsoft.AspNetCore.Http.Features":"*******","Microsoft.AspNetCore.Http.Extensions":"*******","Microsoft.AspNetCore.StaticFiles":"*******","Microsoft.CSharp":"*******","Microsoft.AspNetCore.WebUtilities":"*******","System.Net.WebSockets":"*******","System.Net.NameResolution":"*******","System.Net.NetworkInformation":"*******","System.Security.Cryptography.Encoding":"*******","System.Formats.Asn1":"*******","System.Security.Cryptography.OpenSsl":"*******","System.Threading.Channels":"*******","System.IO.Compression.Brotli":"*******","System.Runtime.Serialization.Formatters":"*******","System.Runtime.Serialization.Primitives":"*******","System.Reflection.Metadata":"*******","System.IO.MemoryMappedFiles":"*******"},"event_id":"21ff06a304c843f3bb0ab77b9e4490ac","timestamp":"2025-07-22T01:01:50.9871703+00:00","logentry":{"message":"Unknown error occurred in DownloadClientHistoryRetentionCheck HealthCheck"},"logger":"DownloadClientRemovesCompletedDownloadsCheck","platform":"csharp","release":"Radarr@5.26.2.10099-master","exception":{"values":[{"type":"System.Net.Sockets.SocketException","value":"Connection refused","module":"System.Net.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","thread_id":4,"stacktrace":{"frames":[{"function":"async ValueTask\u003CValueTuple\u003CSocket, Stream\u003E\u003E HttpConnectionPool.ConnectToTcpHostAsync(string host, int port, HttpRequestMessage initialRequest, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x102","addr_mode":"rel:2","function_id":"0x786"},{"filename":"./Radarr.Common/Http/Dispatchers/ManagedHttpDispatcher.cs","function":"async ValueTask\u003CStream\u003E ManagedHttpDispatcher.onConnect(SocketsHttpConnectionContext context, CancellationToken cancellationToken)","lineno":312,"colno":13,"in_app":true,"package":"Radarr.Common, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x178","addr_mode":"rel:1","function_id":"0x689"},{"filename":"./Radarr.Common/Http/Dispatchers/ManagedHttpDispatcher.cs","function":"async ValueTask\u003CStream\u003E ManagedHttpDispatcher.attemptConnection(AddressFamily addressFamily, SocketsHttpConnectionContext context, CancellationToken cancellationToken)","lineno":326,"colno":17,"in_app":true,"package":"Radarr.Common, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x9c","addr_mode":"rel:1","function_id":"0x687"},{"function":"async Task Socket.ConnectAsync(EndPoint remoteEP)\u002BWaitForConnectWithCancellation(?)","in_app":false,"package":"System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0xa4","addr_mode":"rel:0","function_id":"0x2c9"},{"function":"void AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x10","addr_mode":"rel:0","function_id":"0x2ba"}]},"mechanism":{"type":"chained","source":"InnerException","handled":true,"exception_id":1,"parent_id":0}},{"type":"System.Net.Http.HttpRequestException","value":"Connection refused (qbittorrent:8091)","module":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","thread_id":4,"stacktrace":{"frames":[{"filename":"./Radarr.Core/HealthCheck/Checks/DownloadClientRemovesCompletedDownloadsCheck.cs","function":"HealthCheck DownloadClientRemovesCompletedDownloadsCheck.Check()","lineno":42,"colno":21,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x2c","addr_mode":"rel:3","function_id":"0x1f41"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrent.cs","function":"DownloadClientInfo QBittorrent.GetStatus()","lineno":374,"colno":13,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x0","addr_mode":"rel:3","function_id":"0x2544"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrent.cs","function":"IQBittorrentProxy QBittorrent.get_Proxy()","lineno":49,"colno":44,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x0","addr_mode":"rel:3","function_id":"0x253a"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrentProxySelector.cs","function":"IQBittorrentProxy QBittorrentProxySelector.GetProxy(QBittorrentSettings settings, bool force)","lineno":59,"colno":13,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x0","addr_mode":"rel:3","function_id":"0x257e"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrentProxySelector.cs","function":"Tuple\u003CIQBittorrentProxy, Version\u003E QBittorrentProxySelector.GetProxyCache(QBittorrentSettings settings, bool force)","lineno":76,"colno":13,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x8a","addr_mode":"rel:3","function_id":"0x2580"},{"filename":"./Radarr.Common/Cache/Cached.cs","function":"T Cached\u003CT\u003E.Get(string key, Func\u003CT\u003E function, TimeSpan? lifeTime)","lineno":98,"colno":17,"in_app":true,"package":"Radarr.Common, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0xb1","addr_mode":"rel:1","function_id":"0x59d"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrentProxySelector.cs","function":"Tuple\u003CIQBittorrentProxy, Version\u003E QBittorrentProxySelector.GetProxyCache(QBittorrentSettings settings, bool force)\u002B() =\u003E { }","lineno":76,"colno":52,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x0","addr_mode":"rel:3","function_id":"0x3be5"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrentProxySelector.cs","function":"Tuple\u003CIQBittorrentProxy, Version\u003E QBittorrentProxySelector.FetchProxy(QBittorrentSettings settings)","lineno":81,"colno":13,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x0","addr_mode":"rel:3","function_id":"0x2581"},{"filename":"./Radarr.Core/Download/Clients/QBittorrent/QBittorrentProxyV2.cs","function":"bool QBittorrentProxyV2.IsApiSupported(QBittorrentSettings settings)","lineno":35,"colno":17,"in_app":true,"package":"Radarr.Core, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x19","addr_mode":"rel:3","function_id":"0x2599"},{"filename":"./Radarr.Common/Http/HttpClient.cs","function":"HttpResponse HttpClient.Execute(HttpRequest request)","lineno":128,"colno":13,"in_app":true,"package":"Radarr.Common, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0xd","addr_mode":"rel:1","function_id":"0x1f6"},{"filename":"./Radarr.Common/Http/HttpClient.cs","function":"async Task\u003CHttpResponse\u003E HttpClient.ExecuteAsync(HttpRequest request)","lineno":70,"colno":13,"in_app":true,"package":"Radarr.Common, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x8d","addr_mode":"rel:1","function_id":"0x65a"},{"filename":"./Radarr.Common/Http/HttpClient.cs","function":"async Task\u003CHttpResponse\u003E HttpClient.ExecuteRequestAsync(HttpRequest request, CookieContainer cookieContainer)","lineno":157,"colno":13,"in_app":true,"package":"Radarr.Common, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x18a","addr_mode":"rel:1","function_id":"0x65c"},{"filename":"./Radarr.Common/Http/Dispatchers/ManagedHttpDispatcher.cs","function":"async Task\u003CHttpResponse\u003E ManagedHttpDispatcher.GetResponseAsync(HttpRequest request, CookieContainer cookies)","lineno":115,"colno":17,"in_app":true,"package":"Radarr.Common, Version=5.26.2.10099, Culture=neutral, PublicKeyToken=null","instruction_addr":"0x2e0","addr_mode":"rel:1","function_id":"0x685"},{"function":"async Task\u003CHttpResponseMessage\u003E HttpClient.SendAsync(HttpRequestMessage request)\u002BCore(?)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0xae","addr_mode":"rel:2","function_id":"0x240"},{"function":"async ValueTask\u003CHttpResponseMessage\u003E DecompressionHandler.SendAsync(HttpRequestMessage request, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x14a","addr_mode":"rel:2","function_id":"0x583"},{"function":"async ValueTask\u003CHttpResponseMessage\u003E AuthenticationHelper.SendWithAuthAsync(HttpRequestMessage request, Uri authUri, bool async, ICredentials credentials, bool preAuthenticate, bool isProxyAuth, bool doRequestAuth, HttpConnectionPool pool, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x11c","addr_mode":"rel:2","function_id":"0x437"},{"function":"async ValueTask\u003CHttpResponseMessage\u003E HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, bool async, bool doRequestAuth, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x34c","addr_mode":"rel:2","function_id":"0x778"},{"function":"async ValueTask\u003CHttpConnection\u003E HttpConnectionPool.GetHttp11ConnectionAsync(HttpRequestMessage request, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x1c1","addr_mode":"rel:2","function_id":"0x76a"},{"function":"async ValueTask\u003CT\u003E TaskCompletionSourceWithCancellation\u003CT\u003E.WaitWithCancellationAsync(CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0xa4","addr_mode":"rel:2","function_id":"0xc3"},{"function":"async Task HttpConnectionPool.AddHttp11ConnectionAsync(HttpRequestMessage request)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0xb7","addr_mode":"rel:2","function_id":"0x766"},{"function":"async ValueTask\u003CHttpConnection\u003E HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x8f","addr_mode":"rel:2","function_id":"0x788"},{"function":"async ValueTask\u003CValueTuple\u003CSocket, Stream, TransportContext\u003E\u003E HttpConnectionPool.ConnectAsync(HttpRequestMessage request, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0xf2","addr_mode":"rel:2","function_id":"0x784"},{"function":"async ValueTask\u003CValueTuple\u003CSocket, Stream\u003E\u003E HttpConnectionPool.ConnectToTcpHostAsync(string host, int port, HttpRequestMessage initialRequest, bool async, CancellationToken cancellationToken)","in_app":false,"package":"System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a","instruction_addr":"0x28c","addr_mode":"rel:2","function_id":"0x786"}]},"mechanism":{"type":"generic","handled":true,"exception_id":0}}]},"level":"error","request":{},"contexts":{"Current Culture":{"display_name":"Invariant Language (Invariant Country)","calendar":"GregorianCalendar"},"Dynamic Code":{"Compiled":true,"Supported":true},"Memory Info":{"allocated_bytes":53176400,"fragmented_bytes":144704,"heap_size_bytes":16584568,"high_memory_load_threshold_bytes":2745980928,"total_available_memory_bytes":3051089920,"memory_load_bytes":1617077657,"total_committed_bytes":23277568,"promoted_bytes":1733080,"pause_time_percentage":1.92,"index":6,"finalization_pending_count":34,"compacted":true,"concurrent":false,"pause_durations":[5.314,0]},"ThreadPool Info":{"min_worker_threads":2,"min_completion_port_threads":2,"max_worker_threads":32767,"max_completion_port_threads":1000,"available_worker_threads":32766,"available_completion_port_threads":1000},"app":{"type":"app","app_start_time":"2025-07-22T01:01:48.1506124+00:00","device_app_hash":"e6801f56","app_name":"Radarr","app_version":"5.26.2.10099","app_build":"5.26.2.10099-master"},"device":{"type":"device","timezone":"Europe/Madrid","timezone_display_name":"(UTC\u002B01:00) Central European Time (Madrid)","boot_time":"2025-07-21T06:55:25.8244579+00:00"},"os":{"type":"os","raw_description":"Linux ********-microsoft-standard-WSL2 #1 SMP PREEMPT_DYNAMIC Thu Jun  5 18:30:46 UTC 2025"},"runtime":{"type":"runtime","name":".NET","version":"6.0.35","raw_description":".NET 6.0.35","identifier":"alpine.3.22-x64"},"trace":{"type":"trace","span_id":"e31c679f14c25044","trace_id":"9129e863a7db42ada35106dd8e0a26b5"}},"user":{"id":"e6801f56","ip_address":"{{auto}}"},"environment":"master","sdk":{"packages":[{"name":"nuget:sentry.dotnet","version":"4.0.2"}],"name":"sentry.dotnet","version":"4.0.2"},"fingerprint":["Error","DownloadClientRemovesCompletedDownloadsCheck","Unknown error occurred in DownloadClientHistoryRetentionCheck HealthCheck","System.Net.Http.HttpRequestException","Void MoveNext()","System.Net.Sockets.SocketException"],"breadcrumbs":[{"timestamp":"2025-07-22T01:01:48.357Z","message":"Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099","category":"Bootstrap"},{"timestamp":"2025-07-22T01:01:48.383Z","message":"Data directory is being overridden to [/config]","category":"AppFolderInfo"},{"timestamp":"2025-07-22T01:01:48.419Z","message":"Console selected","category":"Bootstrap","level":"debug"},{"timestamp":"2025-07-22T01:01:48.421Z","message":"Data directory is being overridden to [/config]","category":"AppFolderInfo"},{"timestamp":"2025-07-22T01:01:48.774Z","message":"Data directory is being overridden to [/config]","category":"AppFolderInfo"},{"timestamp":"2025-07-22T01:01:48.942Z","message":"Found 0 processes with the name: Radarr.Console","category":"ProcessProvider","level":"debug"},{"timestamp":"2025-07-22T01:01:48.947Z","message":"Found 1 processes with the name: Radarr","category":"ProcessProvider","level":"debug"},{"timestamp":"2025-07-22T01:01:48.955Z","message":" - [152] Radarr","category":"ProcessProvider","level":"debug"},{"timestamp":"2025-07-22T01:01:49.005Z","message":"*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***","category":"MigrationController"},{"timestamp":"2025-07-22T01:01:49.102Z","message":"DatabaseEngineVersionCheck migrating","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.124Z","message":"PerformDBOperation ","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.134Z","message":"Performing DB Operation","category":"NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor"},{"timestamp":"2025-07-22T01:01:49.206Z","message":"SQLite 3.49.2","category":"DatabaseEngineVersionCheck"},{"timestamp":"2025-07-22T01:01:49.217Z","message":"=\u003E 0.0820911s","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.237Z","message":"DatabaseEngineVersionCheck migrated","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.256Z","message":"=\u003E 0.1028902s","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.311Z","message":"Took: 00:00:00.3060707","category":"MigrationController","level":"debug"},{"timestamp":"2025-07-22T01:01:49.321Z","message":"*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***","category":"MigrationController"},{"timestamp":"2025-07-22T01:01:49.347Z","message":"DatabaseEngineVersionCheck migrating","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.358Z","message":"PerformDBOperation ","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.377Z","message":"Performing DB Operation","category":"NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor"},{"timestamp":"2025-07-22T01:01:49.409Z","message":"SQLite 3.49.2","category":"DatabaseEngineVersionCheck"},{"timestamp":"2025-07-22T01:01:49.417Z","message":"=\u003E 0.0395892s","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.437Z","message":"DatabaseEngineVersionCheck migrated","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.457Z","message":"=\u003E 0.0595262s","category":"FluentMigrator.Runner.MigrationRunner"},{"timestamp":"2025-07-22T01:01:49.483Z","message":"Took: 00:00:00.1625994","category":"MigrationController","level":"debug"},{"timestamp":"2025-07-22T01:01:49.835Z","message":"Now listening on: http://[::]:7878","category":"Microsoft.Hosting.Lifetime"},{"timestamp":"2025-07-22T01:01:49.984Z","message":"Setting up default quality config","category":"QualityDefinitionService","level":"debug"},{"timestamp":"2025-07-22T01:01:50.136Z","message":"Initializing Providers. Count 26","category":"NotificationFactory","level":"debug"},{"timestamp":"2025-07-22T01:01:50.149Z","message":"Starting 2 threads for tasks.","category":"CommandExecutor"},{"timestamp":"2025-07-22T01:01:50.306Z","message":"Initializing Providers. Count 9","category":"IndexerFactory","level":"debug"},{"timestamp":"2025-07-22T01:01:50.341Z","message":"Initializing Providers. Count 19","category":"ImportListFactory","level":"debug"},{"timestamp":"2025-07-22T01:01:50.350Z","message":"Initializing Providers. Count 5","category":"MetadataFactory","level":"debug"},{"timestamp":"2025-07-22T01:01:50.369Z","message":"Initializing Providers. Count 18","category":"DownloadClientFactory","level":"debug"},{"timestamp":"2025-07-22T01:01:50.383Z","message":"Application started. Press Ctrl\u002BC to shut down.","category":"Microsoft.Hosting.Lifetime"},{"timestamp":"2025-07-22T01:01:50.397Z","message":"Hosting environment: Production","category":"Microsoft.Hosting.Lifetime"},{"timestamp":"2025-07-22T01:01:50.417Z","message":"Content root path: /app/radarr/bin","category":"Microsoft.Hosting.Lifetime"},{"timestamp":"2025-07-22T01:01:50.475Z","message":"IPv4 is available: True, IPv6 will be disabled","category":"ManagedHttpDispatcher"},{"timestamp":"2025-07-22T01:01:50.932Z","message":"Unable to communicate with qBittorrent","category":"DownloadClientCheck","level":"debug"},{"timestamp":"2025-07-22T01:01:50.984Z","message":"Unknown error occurred in DownloadClientHistoryRetentionCheck HealthCheck","category":"DownloadClientRemovesCompletedDownloadsCheck","level":"error"}],"tags":{"runtime_identifier":"alpine.3.22-x64","branch":"master","runtime_version":".NET 6.0.35","culture":"","database_migration":"242","sqlite_version":"3.49.2","is_docker":"True"},"debug_meta":{"images":[{"type":"pe_dotnet","debug_id":"88eaaa81-ccfd-4e97-847d-6080628c50cb-b5f1cce4","debug_file":"/__w/1/s/artifacts/obj/System.Net.Sockets/net6.0-Unix-Release/System.Net.Sockets.pdb","code_id":"B5F1CCE4bbc00","code_file":"/app/radarr/bin/System.Net.Sockets.dll"},{"type":"pe_dotnet","debug_id":"1bc230f6-c88e-416c-8d43-a1c85e687ef2-d121b469","debug_checksum":"SHA256:f630c21b8ec86c318d43a1c85e687ef269b421d17f2c556fd10f18139ee70ab6","debug_file":"D:\\a\\1\\s\\_temp\\obj\\Radarr.Common\\Release\\net6.0\\linux-musl-x64\\Radarr.Common.pdb","code_id":"AF055EC946000","code_file":"/app/radarr/bin/Radarr.Common.dll"},{"type":"pe_dotnet","debug_id":"06a578ee-949f-4bce-9b65-d22feb75962a-e4852337","debug_file":"/__w/1/s/artifacts/obj/System.Net.Http/net6.0-Linux-Release/System.Net.Http.pdb","code_id":"E48523371dec00","code_file":"/app/radarr/bin/System.Net.Http.dll"},{"type":"pe_dotnet","debug_id":"f49236c8-8051-4d10-824e-8134a09765f3-91239eab","debug_checksum":"SHA256:c83692f45180101d024e8134a09765f3ab9e231186d8e6009587685e09aeb583","debug_file":"D:\\a\\1\\s\\_temp\\obj\\Radarr.Core\\Release\\net6.0\\linux-musl-x64\\Radarr.Core.pdb","code_id":"FFC0F60E252000","code_file":"/app/radarr/bin/Radarr.Core.dll"}]}}
