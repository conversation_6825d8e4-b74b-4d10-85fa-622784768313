[2025-07-05 00:32:58.835 +02:00] [INF] [141] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-05 00:33:01.257 +02:00] [INF] [141] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-05 00:33:03.192 +02:00] [INF] [141] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-05 00:33:14.998 +02:00] [INF] [142] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-05 00:33:14.999 +02:00] [INF] [142] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-05 00:33:14.999 +02:00] [INF] [142] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-05 02:43:31.642 +02:00] [INF] [125] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-06 02:00:00.000 +02:00, which is 23:16:28.5801757 from now.
[2025-07-05 02:43:44.113 +02:00] [INF] [122] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-05 02:43:44.917 +02:00] [INF] [122] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 13 seconds
[2025-07-05 03:48:29.252 +02:00] [INF] [209] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-06 03:00:00.000 +02:00, which is 23:11:30.7482226 from now.
[2025-07-05 03:48:29.292 +02:00] [INF] [211] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 1 seconds
[2025-07-05 06:17:27.762 +02:00] [INF] [189] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-05 06:17:27.972 +02:00] [INF] [189] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-05 06:17:28.014 +02:00] [INF] [189] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-05 06:17:28.209 +02:00] [INF] [189] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-05 06:17:31.393 +02:00] [INF] [187] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 4 seconds
[2025-07-05 06:17:43.035 +02:00] [WRN] [184] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-05 06:17:43.900 +02:00] [INF] [183] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/0e0a51b1280d4c628a50aeef192f7d94.png"
[2025-07-05 06:17:44.304 +02:00] [INF] [183] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/0e0a51b1280d4c628a50aeef192f7d94.png"
[2025-07-05 06:17:45.001 +02:00] [WRN] [183] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-05 06:17:51.662 +02:00] [INF] [234] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-05 06:17:51.716 +02:00] [INF] [234] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-05 06:17:52.894 +02:00] [INF] [238] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-05 06:17:52.895 +02:00] [INF] [238] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-05 06:17:55.017 +02:00] [ERR] [236] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-05 06:18:13.960 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/62bf802fa3e94487a981d426c5e0b0e0.png"
[2025-07-05 06:18:14.745 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/62bf802fa3e94487a981d426c5e0b0e0.png"
[2025-07-05 06:18:14.828 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/f8e045afa84c4407bbb0ac6bdf9107fd.png"
[2025-07-05 06:18:15.154 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/f8e045afa84c4407bbb0ac6bdf9107fd.png"
[2025-07-05 06:18:15.270 +02:00] [INF] [233] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/e5c7e5a227cb4744a4f7e58983ee0370.png"
[2025-07-05 06:18:15.634 +02:00] [INF] [233] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/e5c7e5a227cb4744a4f7e58983ee0370.png"
[2025-07-05 06:18:15.750 +02:00] [INF] [233] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/ed103e0923934f648621875d80c03be0.png"
[2025-07-05 06:18:16.144 +02:00] [INF] [233] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/ed103e0923934f648621875d80c03be0.png"
[2025-07-05 06:18:16.240 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/96d0331c1ba7456ca5db4956e6099bcb.png"
[2025-07-05 06:18:16.575 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/96d0331c1ba7456ca5db4956e6099bcb.png"
[2025-07-05 06:18:16.667 +02:00] [INF] [233] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/d120dab482654e8a98a959452d879a33.png"
[2025-07-05 06:18:17.396 +02:00] [INF] [233] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/d120dab482654e8a98a959452d879a33.png"
[2025-07-05 06:18:17.508 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/94d4a5ff42b94c488b9762b73830e562.png"
[2025-07-05 06:18:18.075 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/94d4a5ff42b94c488b9762b73830e562.png"
[2025-07-05 06:18:18.162 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/adbc5341482a45679b1d72183cdd57ed.png"
[2025-07-05 06:18:18.920 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/adbc5341482a45679b1d72183cdd57ed.png"
[2025-07-05 06:18:19.018 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/897800777e5a4c19aef0b57031a486a7.png"
[2025-07-05 06:18:19.827 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/897800777e5a4c19aef0b57031a486a7.png"
[2025-07-05 06:18:19.901 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/539813058aa84e1296d06d78b2f933d7.png"
[2025-07-05 06:18:20.285 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/539813058aa84e1296d06d78b2f933d7.png"
[2025-07-05 06:18:20.401 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/327908999c8f495a9de4fcfaae0b5851.png"
[2025-07-05 06:18:20.769 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/327908999c8f495a9de4fcfaae0b5851.png"
[2025-07-05 06:18:20.884 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/ccde3d0314094fa4b46df89b42364229.png"
[2025-07-05 06:18:21.578 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/ccde3d0314094fa4b46df89b42364229.png"
[2025-07-05 06:18:21.682 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/eb5a721cf3734287bf9612bca4d93bfc.png"
[2025-07-05 06:18:22.086 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/eb5a721cf3734287bf9612bca4d93bfc.png"
[2025-07-05 06:18:22.316 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/395954b48eac42559514e969f8c843bc.png"
[2025-07-05 06:18:22.962 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/395954b48eac42559514e969f8c843bc.png"
[2025-07-05 06:18:23.091 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/4a84729c62d0476d8992c4b5523b212e.png"
[2025-07-05 06:18:23.741 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/4a84729c62d0476d8992c4b5523b212e.png"
[2025-07-05 06:18:23.862 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/dcd1477c83d84825aa30d2ed6a3a9f1d.png"
[2025-07-05 06:18:24.248 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/dcd1477c83d84825aa30d2ed6a3a9f1d.png"
[2025-07-05 06:18:24.358 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/bd8ada7ac9b04dc0ba0d7a3988c9aea2.png"
[2025-07-05 06:18:25.425 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/bd8ada7ac9b04dc0ba0d7a3988c9aea2.png"
[2025-07-05 06:18:25.514 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/cad6f3256bae4dd0b1fc7cbb0002d16c.png"
[2025-07-05 06:18:25.877 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/cad6f3256bae4dd0b1fc7cbb0002d16c.png"
[2025-07-05 06:18:25.998 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/37f7cb85864c4e568853b481e0135372.png"
[2025-07-05 06:18:26.848 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/37f7cb85864c4e568853b481e0135372.png"
[2025-07-05 06:18:26.949 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/6e52b6ec106244a7bcc7d5681fa583f6.png"
[2025-07-05 06:18:27.268 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/6e52b6ec106244a7bcc7d5681fa583f6.png"
[2025-07-05 06:18:27.388 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/0d4763380f564a73be2c1b5e9aeb994b.png"
[2025-07-05 06:18:27.717 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/0d4763380f564a73be2c1b5e9aeb994b.png"
[2025-07-05 06:18:27.806 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/4a08851fd9be4b3687db9f56f01579c4.png"
[2025-07-05 06:18:28.180 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/4a08851fd9be4b3687db9f56f01579c4.png"
[2025-07-05 06:18:28.299 +02:00] [INF] [204] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/da0bcca31952469d907e678d03e7a6ae.png"
[2025-07-05 06:18:28.645 +02:00] [INF] [204] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/da0bcca31952469d907e678d03e7a6ae.png"
[2025-07-05 06:18:28.752 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/e6efabab44834b87b2c9c90ebdbe20c8.png"
[2025-07-05 06:18:29.695 +02:00] [INF] [236] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/e6efabab44834b87b2c9c90ebdbe20c8.png"
[2025-07-05 06:18:29.782 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/5cf055c161d24bf6a40eeb8534232ed3.png"
[2025-07-05 06:18:30.134 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/5cf055c161d24bf6a40eeb8534232ed3.png"
[2025-07-05 06:18:30.276 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/024ab603df634019a227010b01f6b8ed.png"
[2025-07-05 06:18:31.013 +02:00] [INF] [235] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/024ab603df634019a227010b01f6b8ed.png"
[2025-07-05 06:18:32.770 +02:00] [INF] [235] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 1 minute(s) and 5 seconds
[2025-07-05 06:18:32.781 +02:00] [INF] [204] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-05 06:18:32.961 +02:00] [INF] [236] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-05 06:18:33.152 +02:00] [INF] [233] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-05 11:14:23.681 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-05 11:14:23.804 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_DATA_DIR, /config]"]
[2025-07-05 11:14:23.806 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-05 11:14:23.809 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-05 11:14:23.809 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-05 11:14:23.817 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-05 11:14:23.817 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-05 11:14:23.817 +02:00] [INF] [1] Main: Processor count: 12
[2025-07-05 11:14:23.817 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-05 11:14:23.818 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-05 11:14:23.818 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-05 11:14:23.818 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-05 11:14:23.818 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-05 11:14:23.818 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-05 11:14:23.818 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-05 11:14:24.349 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-05 11:14:24.734 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-05 11:14:24.856 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-05 11:14:24.874 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-05 11:14:24.896 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-05 11:14:25.032 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-05 11:14:25.032 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-05 11:14:25.032 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-05 11:14:25.033 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-05 11:14:25.034 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-05 11:14:25.034 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-05 11:14:25.035 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-05 11:14:39.247 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-05 11:14:39.251 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-05 11:14:39.251 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-05 11:14:39.251 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-05 11:14:39.252 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-05 11:14:39.278 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-05 11:14:39.278 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-05 11:14:39.401 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-05 11:14:40.037 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-05 11:14:40.082 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-05 11:14:40.089 +02:00] [INF] [11] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-05 11:14:40.119 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-06 03:00:00.000 +02:00, which is 15:45:19.8803604 from now.
[2025-07-05 11:14:40.173 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-06 02:00:00.000 +02:00, which is 14:45:19.8261480 from now.
[2025-07-05 11:14:40.239 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-05 11:14:40.449 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-05 11:14:40.531 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-05 11:14:40.560 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-05 11:14:40.588 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-05 11:14:40.765 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-05 11:14:41.468 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-05 11:14:43.201 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-05 11:14:43.256 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-05 11:14:45.047 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-07-05 11:14:53.886 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-05 11:14:53.903 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-05 11:14:53.903 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-05 11:14:53.903 +02:00] [INF] [1] Main: Startup complete 0:00:31.1866451
[2025-07-05 17:05:11.176 +02:00] [INF] [170] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-05 17:05:12.885 +02:00] [INF] [170] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-05 17:05:13.403 +02:00] [INF] [170] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-05 18:32:31.162 +02:00] [INF] [73] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-05 18:32:31.467 +02:00] [INF] [73] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-05 18:32:31.493 +02:00] [INF] [73] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-05 19:00:49.565 +02:00] [INF] [217] Emby.Server.Implementations.Session.SessionManager: Sending shutdown notifications
