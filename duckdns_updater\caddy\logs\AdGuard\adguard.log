{"level":"info","ts":1752091428.5882618,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"49954","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/.well-known/acme-challenge/JVp_xj1gBMjVzEC7GV0xpdLjBUgPaOrapVl-EUveSvc","headers":{"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.003765331,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1752091428.9553347,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"49968","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/.well-known/acme-challenge/JVp_xj1gBMjVzEC7GV0xpdLjBUgPaOrapVl-EUveSvc","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000051282,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1752091439.103008,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"49326","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/.well-known/acme-challenge/JVp_xj1gBMjVzEC7GV0xpdLjBUgPaOrapVl-EUveSvc","headers":{"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.000105504,"size":87,"status":200,"resp_headers":{"Content-Type":["text/plain"],"Server":["Caddy"]}}
{"level":"info","ts":1752091439.3314548,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"49342","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/.well-known/acme-challenge/JVp_xj1gBMjVzEC7GV0xpdLjBUgPaOrapVl-EUveSvc","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000062933,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1752091458.841961,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"56524","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/.well-known/acme-challenge/JVp_xj1gBMjVzEC7GV0xpdLjBUgPaOrapVl-EUveSvc","headers":{"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.000055133,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1752091504.409492,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"56216","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Accept-Encoding":["gzip"],"User-Agent":["Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]}},"bytes_read":0,"user_id":"","duration":0.00001347,"size":0,"status":308,"resp_headers":{"Content-Type":[],"Server":["Caddy"],"Connection":["close"],"Location":["https://tankeguard.duckdns.org/"]}}
{"level":"info","ts":1752091504.8469002,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"56226","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/favicon.ico","headers":{"User-Agent":["Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.000017331,"size":0,"status":308,"resp_headers":{"Server":["Caddy"],"Connection":["close"],"Location":["https://tankeguard.duckdns.org/favicon.ico"],"Content-Type":[]}}
{"level":"info","ts":1752091509.1305177,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60886","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.004051936,"size":34,"status":302,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["text/html; charset=utf-8"],"Location":["/login.html"],"Date":["Wed, 09 Jul 2025 20:05:09 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["34"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091509.3488731,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60886","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Referer":["https://tankeguard.duckdns.org"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.006881401,"size":595,"status":200,"resp_headers":{"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:05:09 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091519.6397724,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"48998","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001842697,"size":34,"status":302,"resp_headers":{"Content-Type":["text/html; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:05:19 GMT"],"Content-Length":["34"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Location":["/login.html"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091519.821344,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"48998","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Referer":["https://tankeguard.duckdns.org"],"Accept-Encoding":["gzip"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.010303231,"size":595,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:05:19 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091562.096739,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"47720","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Accept":["*/*"],"Connection":["keep-alive"],"Sentry-Trace":["ef6a44ac031b4ad3a684f3ec65aff4bf-b0ffffc7ca9c54c9-0"],"Baggage":["sentry-trace_id=ef6a44ac031b4ad3a684f3ec65aff4bf,sentry-sample_rand=0.950186,sentry-environment=production,sentry-release=1.2.3,sentry-public_key=a29b631a21524e2b9f82b5894c61596d,sentry-transaction=Process%20job%20-%20run%20selected%20pipelines,sentry-sample_rate=0.00125,sentry-sampled=false"],"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15"],"Accept-Encoding":["gzip, deflate"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"http/1.1","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.022123131,"size":34,"status":302,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:06:02 GMT"],"Content-Length":["34"],"Content-Type":["text/html; charset=utf-8"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Location":["/login.html"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091562.388413,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"47720","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Accept":["*/*"],"Connection":["keep-alive"],"Sentry-Trace":["4ab15cf379544e8e825ce14a5eb0f80b-8bed0a82b0d1ad5e-0"],"Baggage":["sentry-trace_id=4ab15cf379544e8e825ce14a5eb0f80b,sentry-sample_rand=0.339279,sentry-environment=production,sentry-release=1.2.3,sentry-public_key=a29b631a21524e2b9f82b5894c61596d,sentry-transaction=Process%20job%20-%20run%20selected%20pipelines,sentry-sample_rate=0.00125,sentry-sampled=false"],"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15"],"Accept-Encoding":["gzip, deflate"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"http/1.1","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002026511,"size":595,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"X-Frame-Options":["SAMEORIGIN"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:06:02 GMT"],"Via":["1.1 Caddy"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091563.4871545,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"47732","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/robots.txt","headers":{"Accept-Encoding":["gzip, deflate"],"Accept":["*/*"],"Connection":["keep-alive"],"Sentry-Trace":["ef6a44ac031b4ad3a684f3ec65aff4bf-8ef60cda66fcfa14-0"],"Baggage":["sentry-trace_id=ef6a44ac031b4ad3a684f3ec65aff4bf,sentry-sample_rand=0.950186,sentry-environment=production,sentry-release=1.2.3,sentry-public_key=a29b631a21524e2b9f82b5894c61596d,sentry-transaction=Process%20job%20-%20run%20selected%20pipelines,sentry-sample_rate=0.00125,sentry-sampled=false"],"User-Agent":["Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"http/1.1","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001968736,"size":9,"status":403,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Content-Length":["9"],"Date":["Wed, 09 Jul 2025 20:06:03 GMT"]}}
{"level":"info","ts":1752091570.1617892,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"40240","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002011006,"size":34,"status":302,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/html; charset=utf-8"],"Location":["/login.html"],"Date":["Wed, 09 Jul 2025 20:06:10 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Length":["34"]}}
{"level":"info","ts":1752091570.385525,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"40240","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Referer":["https://tankeguard.duckdns.org"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002397319,"size":595,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["text/html; charset=utf-8"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:06:10 GMT"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091573.206634,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"40248","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002100829,"size":34,"status":302,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["34"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Content-Type":["text/html; charset=utf-8"],"Location":["/login.html"],"Date":["Wed, 09 Jul 2025 20:06:13 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091573.4213402,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"40248","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Referer":["https://tankeguard.duckdns.org"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002118532,"size":595,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:06:13 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091576.039704,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"40262","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001932843,"size":34,"status":302,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Location":["/login.html"],"Date":["Wed, 09 Jul 2025 20:06:16 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["text/html; charset=utf-8"],"Content-Length":["34"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091576.2556317,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"40262","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Referer":["https://tankeguard.duckdns.org"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002937951,"size":595,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:06:16 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091590.0410187,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42932","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Accept-Language":["en-US,en;q=0.9"],"Connection":["close, Te"],"Upgrade-Insecure-Requests":["1"],"Te":["trailers"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8"],"Accept-Encoding":["gzip, deflate"],"User-Agent":["Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001887644,"size":34,"status":302,"resp_headers":{"Content-Length":["34"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["text/html; charset=utf-8"],"Location":["/login.html"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:06:30 GMT"]}}
{"level":"info","ts":1752091590.6054866,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42940","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Upgrade-Insecure-Requests":["1"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8"],"User-Agent":["Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)"],"Accept-Encoding":["gzip, deflate"],"Connection":["close, Te"],"Accept-Language":["en-US,en;q=0.9"],"Te":["trailers"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001984964,"size":595,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Date":["Wed, 09 Jul 2025 20:06:30 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091593.761958,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["none"],"Priority":["u=0, i"],"Upgrade-Insecure-Requests":["1"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-User":["?1"],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["navigate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001908354,"size":34,"status":302,"resp_headers":{"Content-Type":["text/html; charset=utf-8"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:06:33 GMT"],"Content-Length":["34"],"Location":["/login.html"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091593.7692626,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Priority":["u=0, i"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-User":["?1"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["document"],"Sec-Ch-Ua-Mobile":["?0"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Upgrade-Insecure-Requests":["1"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001836533,"size":595,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Content-Type":["text/html; charset=utf-8"],"X-Frame-Options":["SAMEORIGIN"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:06:33 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091593.857317,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.3426965bcaeb3e0efc8c.css","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["text/css,*/*;q=0.1"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["style"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.041473318,"size":51010,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/css; charset=utf-8"],"Vary":["Accept-Encoding"],"Accept-Ranges":["bytes"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Encoding":["gzip"],"Date":["Wed, 09 Jul 2025 20:06:33 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091593.951709,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.1aaa65cbc351dcc2d6ca.js","headers":{"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["script"],"Accept":["*/*"],"Referer":["https://tankeguard.duckdns.org/login.html"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.136718315,"size":681446,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/javascript; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:06:33 GMT"],"Accept-Ranges":["bytes"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Encoding":["gzip"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091594.1676283,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/assets/favicon.png","headers":{"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["image"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002501528,"size":1296,"status":200,"resp_headers":{"Content-Length":["1296"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["image/png"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:06:34 GMT"],"Accept-Ranges":["bytes"]}}
{"level":"info","ts":1752091597.2141929,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42964","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-User":["?1"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["none"],"Referer":["https://www.google.com/"],"Accept-Language":["en-US"],"Accept-Encoding":["gzip, deflate, br"],"Upgrade-Insecure-Requests":["1"],"Sec-Ch-Ua":["\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003210314,"size":595,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Date":["Wed, 09 Jul 2025 20:06:37 GMT"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091597.4387653,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42964","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.3426965bcaeb3e0efc8c.css","headers":{"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["style"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Encoding":["gzip, deflate, br"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Language":["en-US"],"Sec-Ch-Ua":["\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\""],"Sec-Fetch-Mode":["no-cors"],"Accept":["text/css,*/*;q=0.1"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.014469447,"size":51010,"status":200,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:06:37 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Accept-Ranges":["bytes"],"Content-Encoding":["gzip"],"Content-Type":["text/css; charset=utf-8"]}}
{"level":"info","ts":1752091597.5015695,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42964","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.1aaa65cbc351dcc2d6ca.js","headers":{"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Sec-Ch-Ua":["\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\""],"Sec-Ch-Ua-Platform":["\"Windows\""],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Encoding":["gzip, deflate, br"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["*/*"],"Sec-Fetch-Dest":["script"],"Accept-Language":["en-US"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.104728688,"size":681446,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Content-Encoding":["gzip"],"Content-Type":["text/javascript; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:06:37 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Accept-Ranges":["bytes"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091609.0564024,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"57716","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00198031,"size":34,"status":302,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/html; charset=utf-8"],"Location":["/login.html"],"Content-Length":["34"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 20:06:49 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091609.2682586,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"57716","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Referer":["https://tankeguard.duckdns.org"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001894087,"size":595,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:06:49 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091630.3069627,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/login","headers":{"Content-Type":["application/json"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Origin":["https://tankeguard.duckdns.org"],"Content-Length":["57"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":57,"user_id":"","duration":30.000288145,"size":0,"status":0,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091634.3399646,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Dest":["document"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Mobile":["?0"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002533612,"size":595,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:14 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Content-Encoding":["gzip"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["text/html; charset=utf-8"]}}
{"level":"info","ts":1752091634.3822243,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.3426965bcaeb3e0efc8c.css","headers":{"Accept":["text/css,*/*;q=0.1"],"Sec-Fetch-Dest":["style"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Priority":["u=0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.015153891,"size":51010,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:07:14 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Vary":["Accept-Encoding"],"Accept-Ranges":["bytes"],"Content-Encoding":["gzip"],"Content-Type":["text/css; charset=utf-8"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091634.4806328,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.1aaa65cbc351dcc2d6ca.js","headers":{"Sec-Fetch-Dest":["script"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["*/*"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.113616353,"size":681446,"status":200,"resp_headers":{"Accept-Ranges":["bytes"],"Content-Encoding":["gzip"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:07:14 GMT"],"Content-Type":["text/javascript; charset=utf-8"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091634.5412645,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/assets/favicon.png","headers":{"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["image"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00243658,"size":1296,"status":200,"resp_headers":{"Content-Type":["image/png"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Accept-Ranges":["bytes"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 20:07:14 GMT"],"Content-Length":["1296"]}}
{"level":"info","ts":1752091635.591351,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Upgrade-Insecure-Requests":["1"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-User":["?1"],"Priority":["u=0, i"],"Sec-Fetch-Mode":["navigate"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["none"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002174408,"size":595,"status":200,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Content-Type":["text/html; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:15 GMT"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Encoding":["gzip"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091635.628848,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.3426965bcaeb3e0efc8c.css","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["text/css,*/*;q=0.1"],"Sec-Fetch-Dest":["style"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeguard.duckdns.org/login.html"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.011118667,"size":51010,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["text/css; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:07:15 GMT"],"Accept-Ranges":["bytes"],"X-Content-Type-Options":["nosniff"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Content-Encoding":["gzip"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091635.7170477,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.1aaa65cbc351dcc2d6ca.js","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["*/*"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["script"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.104828746,"size":681446,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Encoding":["gzip"],"Content-Type":["text/javascript; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:15 GMT"],"Accept-Ranges":["bytes"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091635.7703223,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/assets/favicon.png","headers":{"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["image"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002055182,"size":1296,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["image/png"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:15 GMT"],"Accept-Ranges":["bytes"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Length":["1296"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091635.8731308,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Dest":["document"],"Priority":["u=0, i"],"Cache-Control":["max-age=0"],"Sec-Fetch-Site":["none"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Upgrade-Insecure-Requests":["1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-User":["?1"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002650266,"size":595,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:15 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"]}}
{"level":"info","ts":1752091635.91772,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.3426965bcaeb3e0efc8c.css","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["style"],"Priority":["u=0"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["text/css,*/*;q=0.1"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.017571039,"size":51010,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Encoding":["gzip"],"Content-Type":["text/css; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:07:15 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Accept-Ranges":["bytes"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091635.9977791,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.1aaa65cbc351dcc2d6ca.js","headers":{"Sec-Fetch-Dest":["script"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["*/*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.097690379,"size":681446,"status":200,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["text/javascript; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:15 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Accept-Ranges":["bytes"]}}
{"level":"info","ts":1752091636.0581448,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/assets/favicon.png","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["image"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001987209,"size":1296,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Accept-Ranges":["bytes"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:16 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["1296"],"Content-Type":["image/png"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091657.0988815,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Sec-Fetch-User":["?1"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Upgrade-Insecure-Requests":["1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["navigate"],"Priority":["u=0, i"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002251642,"size":595,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:37 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"]}}
{"level":"info","ts":1752091657.1058123,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/login","headers":{"Referer":["https://tankeguard.duckdns.org/login.html"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Mode":["cors"],"Content-Length":["56"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Dest":["empty"],"Accept":["application/json, text/plain, */*"],"Content-Type":["application/json"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":56,"user_id":"","duration":12.541925452,"size":0,"status":0,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Server":["Caddy"]}}
{"level":"info","ts":1752091657.1384006,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.3426965bcaeb3e0efc8c.css","headers":{"Priority":["u=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["text/css,*/*;q=0.1"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["style"],"Referer":["https://tankeguard.duckdns.org/login.html"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.014676524,"size":51010,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:07:37 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Accept-Ranges":["bytes"],"Content-Encoding":["gzip"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/css; charset=utf-8"],"Vary":["Accept-Encoding"]}}
{"level":"info","ts":1752091657.2372599,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.1aaa65cbc351dcc2d6ca.js","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["*/*"],"Sec-Fetch-Dest":["script"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Mode":["no-cors"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.116546646,"size":681446,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["text/javascript; charset=utf-8"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Accept-Ranges":["bytes"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:07:37 GMT"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091657.2873375,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/assets/favicon.png","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["no-cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["image"],"Priority":["u=1, i"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/login.html"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002043124,"size":1296,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["1296"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["image/png"],"Date":["Wed, 09 Jul 2025 20:07:37 GMT"],"Accept-Ranges":["bytes"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091692.3726408,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/login","headers":{"Content-Length":["57"],"Origin":["https://tankeguard.duckdns.org"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Content-Type":["application/json"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":57,"user_id":"","duration":26.113885137,"size":0,"status":0,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Server":["Caddy"]}}
{"level":"error","ts":1752091739.8521833,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Site":["none"],"Priority":["u=0, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003391077,"size":40,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1752091739.9752245,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/favicon.ico","headers":{"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["image"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002529468,"size":40,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1752091743.6735363,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["document"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002920984,"size":40,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1752091743.7101052,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/favicon.ico","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["image"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002677072,"size":40,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1752091743.838373,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"Upgrade-Insecure-Requests":["1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0, i"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002594913,"size":40,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1752091743.8766646,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/favicon.ico","headers":{"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["image"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002515494,"size":40,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"info","ts":1752091744.0210624,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Sec-Fetch-User":["?1"],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Upgrade-Insecure-Requests":["1"],"Cache-Control":["max-age=0"],"Sec-Fetch-Site":["none"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["navigate"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00379215,"size":34,"status":302,"resp_headers":{"Content-Length":["34"],"Content-Type":["text/html; charset=utf-8"],"Location":["/login.html"],"Date":["Wed, 09 Jul 2025 20:09:04 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091744.029207,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Cache-Control":["max-age=0"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-User":["?1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["none"],"Sec-Fetch-Dest":["document"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0, i"],"Sec-Fetch-Mode":["navigate"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002985606,"size":595,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:09:04 GMT"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"]}}
{"level":"info","ts":1752091744.063214,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.3426965bcaeb3e0efc8c.css","headers":{"Priority":["u=0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["text/css,*/*;q=0.1"],"Sec-Fetch-Dest":["style"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.012420821,"size":51010,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Content-Encoding":["gzip"],"Content-Type":["text/css; charset=utf-8"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:09:04 GMT"],"Accept-Ranges":["bytes"]}}
{"level":"info","ts":1752091744.1673884,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.1aaa65cbc351dcc2d6ca.js","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["*/*"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["script"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.116598269,"size":681446,"status":200,"resp_headers":{"Accept-Ranges":["bytes"],"Content-Encoding":["gzip"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["text/javascript; charset=utf-8"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:04 GMT"]}}
{"level":"info","ts":1752091744.2890584,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/assets/favicon.png","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["image"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002045701,"size":1296,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Accept-Ranges":["bytes"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["1296"],"Content-Type":["image/png"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:04 GMT"]}}
{"level":"info","ts":1752091744.3346598,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["none"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Dest":["document"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-User":["?1"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cache-Control":["max-age=0"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["navigate"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00205661,"size":595,"status":200,"resp_headers":{"Content-Type":["text/html; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:09:04 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Content-Encoding":["gzip"]}}
{"level":"info","ts":1752091744.369458,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.3426965bcaeb3e0efc8c.css","headers":{"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["no-cors"],"Sec-Fetch-Dest":["style"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["text/css,*/*;q=0.1"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.014492763,"size":51010,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Encoding":["gzip"],"Content-Type":["text/css; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:04 GMT"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Accept-Ranges":["bytes"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091744.466306,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.1aaa65cbc351dcc2d6ca.js","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["script"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["*/*"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.111676014,"size":681446,"status":200,"resp_headers":{"Content-Encoding":["gzip"],"Date":["Wed, 09 Jul 2025 20:09:04 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Accept-Ranges":["bytes"],"Content-Type":["text/javascript; charset=utf-8"],"Vary":["Accept-Encoding"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091744.5158017,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/assets/favicon.png","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["no-cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["image"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002268749,"size":1296,"status":200,"resp_headers":{"Content-Length":["1296"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["image/png"],"Accept-Ranges":["bytes"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:04 GMT"]}}
{"level":"info","ts":1752091754.6778085,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/login","headers":{"Content-Length":["57"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Origin":["https://tankeguard.duckdns.org"],"Content-Type":["application/json"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":57,"user_id":"","duration":0.054717922,"size":29,"status":403,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"X-Content-Type-Options":["nosniff"],"Date":["Wed, 09 Jul 2025 20:09:14 GMT"],"Content-Length":["29"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091762.010397,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/login","headers":{"Content-Type":["application/json"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Origin":["https://tankeguard.duckdns.org"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Content-Length":["56"],"Sec-Fetch-Site":["same-origin"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":56,"user_id":"","duration":0.08800523,"size":3,"status":200,"resp_headers":{"Pragma":["no-cache"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["3"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Expires":["0"],"Set-Cookie":["REDACTED"],"X-Frame-Options":["SAMEORIGIN"],"X-Content-Type-Options":["nosniff"],"Cache-Control":["no-store, no-cache, must-revalidate, proxy-revalidate"]}}
{"level":"info","ts":1752091762.068097,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Sec-Fetch-User":["?1"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Mode":["navigate"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Referer":["https://tankeguard.duckdns.org/login.html"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Dest":["document"],"Priority":["u=0, i"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.042557694,"size":803,"status":200,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Encoding":["gzip"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/html; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["803"],"Accept-Ranges":["bytes"]}}
{"level":"info","ts":1752091762.1359074,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/main.3426965bcaeb3e0efc8c.css","headers":{"Priority":["u=0"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["no-cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["style"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["text/css,*/*;q=0.1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.036929209,"size":60053,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Accept-Ranges":["bytes"],"Content-Encoding":["gzip"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Content-Type":["text/css; charset=utf-8"],"Vary":["Accept-Encoding"]}}
{"level":"info","ts":1752091762.278694,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/main.1b3a39e5bd62602cfce3.js","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["*/*"],"Sec-Fetch-Dest":["script"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.179714975,"size":1038195,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Accept-Ranges":["bytes"],"Content-Encoding":["gzip"],"Content-Type":["text/javascript; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091762.3696105,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/status","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002212809,"size":214,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["214"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091762.404041,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Cookie":["REDACTED"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002474831,"size":108,"status":200,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["108"]}}
{"level":"info","ts":1752091762.405524,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00389574,"size":1806,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Encoding":["gzip"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Content-Length":["1806"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091762.4069233,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/profile","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00524452,"size":58,"status":200,"resp_headers":{"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["58"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"]}}
{"level":"info","ts":1752091762.4075089,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/tls/status","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["cors"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.005754,"size":475,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["475"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Type":["application/json"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091762.4069448,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/version.json","headers":{"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Content-Type":["application/json"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Content-Length":["21"],"Sec-Fetch-Mode":["cors"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Referer":["https://tankeguard.duckdns.org/"],"Origin":["https://tankeguard.duckdns.org"],"Cookie":["REDACTED"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":21,"user_id":"","duration":0.005248501,"size":18,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["18"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091762.4075634,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Referer":["https://tankeguard.duckdns.org/"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.005729388,"size":50,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["50"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091762.421528,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/profile","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002693407,"size":58,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["58"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091762.4636326,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"PUT","host":"tankeguard.duckdns.org","uri":"/control/profile/update","headers":{"Content-Type":["application/json"],"Sec-Ch-Ua-Mobile":["?0"],"Content-Length":["32"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":32,"user_id":"","duration":0.01622669,"size":3,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["3"]}}
{"level":"info","ts":1752091762.463716,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Content-Type":["application/json"],"Origin":["https://tankeguard.duckdns.org"],"Content-Length":["33"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Cookie":["REDACTED"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.01627012,"size":486,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:22 GMT"],"Content-Length":["486"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091764.97884,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002336084,"size":50,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:09:24 GMT"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Content-Length":["50"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091764.978942,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Fetch-Dest":["empty"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002489219,"size":108,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:24 GMT"],"Content-Type":["application/json"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091764.9796543,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003208848,"size":1812,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Content-Length":["1812"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:09:24 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091764.999617,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Content-Length":["33"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Content-Type":["application/json"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"],"Cookie":["REDACTED"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["application/json, text/plain, */*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002212819,"size":486,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:09:24 GMT"],"Content-Length":["486"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091765.139332,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Accept":["application/json, text/plain, */*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001992359,"size":50,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["50"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091765.139482,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002523494,"size":108,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Vary":["Accept-Encoding"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091765.141195,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Cookie":["REDACTED"],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003798581,"size":1849,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Length":["1849"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091765.1474922,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Content-Type":["application/json"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Referer":["https://tankeguard.duckdns.org/"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Content-Length":["33"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.001906227,"size":486,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Content-Length":["486"],"X-Content-Type-Options":["nosniff"],"Vary":["Accept-Encoding"]}}
{"level":"info","ts":1752091765.3260944,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Cookie":["REDACTED"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Priority":["u=1, i"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002104834,"size":108,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091765.3260877,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002070733,"size":50,"status":200,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["50"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"]}}
{"level":"info","ts":1752091765.3274255,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003475409,"size":1806,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Length":["1806"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091765.3345945,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Content-Type":["application/json"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Content-Length":["33"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["empty"],"Accept":["application/json, text/plain, */*"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Site":["same-origin"],"Priority":["u=1, i"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002112744,"size":486,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Length":["486"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091765.4844553,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Cookie":["REDACTED"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001838892,"size":50,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["50"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091765.4846141,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.0020065,"size":108,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091765.485258,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002619545,"size":1834,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Length":["1834"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091765.4930434,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Content-Length":["33"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Origin":["https://tankeguard.duckdns.org"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Content-Type":["application/json"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.00221311,"size":486,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Content-Length":["486"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:09:25 GMT"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091767.455404,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/filtering/status","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002955107,"size":611,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["611"],"Content-Encoding":["gzip"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 20:09:27 GMT"],"Vary":["Accept-Encoding"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091806.938376,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/filtering/add_url","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["empty"],"Content-Length":["125"],"Content-Type":["application/json"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":125,"user_id":"","duration":30.001118144,"size":0,"status":0,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091849.5437987,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/status","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.010112641,"size":214,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:10:49 GMT"],"Content-Length":["214"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091923.0235507,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/status","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Priority":["u=1, i"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["application/json, text/plain, */*"],"Cookie":["REDACTED"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00492759,"size":214,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Content-Length":["214"],"X-Frame-Options":["SAMEORIGIN"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:03 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091924.799357,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/filtering/add_url","headers":{"Priority":["u=1, i"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Cookie":["REDACTED"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Content-Type":["application/json"],"Sec-Ch-Ua-Mobile":["?0"],"Content-Length":["125"],"Referer":["https://tankeguard.duckdns.org/"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":125,"user_id":"","duration":0.002124424,"size":120,"status":400,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 20:12:04 GMT"],"Content-Length":["120"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"info","ts":1752091926.6539447,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"Cache-Control":["max-age=0"],"Sec-Fetch-Mode":["navigate"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-User":["?1"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Priority":["u=0, i"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Dest":["document"],"Referer":["https://tankeguard.duckdns.org/login.html"],"Cookie":["REDACTED"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001933097,"size":803,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:06 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["803"],"Accept-Ranges":["bytes"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091926.7008584,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/main.3426965bcaeb3e0efc8c.css","headers":{"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["style"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Mode":["no-cors"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["text/css,*/*;q=0.1"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.019339443,"size":60053,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Accept-Ranges":["bytes"],"Content-Type":["text/css; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:12:06 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091926.8434794,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/main.1b3a39e5bd62602cfce3.js","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["*/*"],"Sec-Fetch-Dest":["script"],"Sec-Fetch-Mode":["no-cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.161967464,"size":1038195,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["text/javascript; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:12:06 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Accept-Ranges":["bytes"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091926.93962,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/status","headers":{"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002375515,"size":214,"status":200,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:06 GMT"],"Content-Length":["214"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091927.020163,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/tls/status","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Fetch-Mode":["cors"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002430643,"size":475,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:07 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Type":["application/json"],"Content-Length":["475"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091927.0203087,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/profile","headers":{"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002655997,"size":58,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["58"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:07 GMT"]}}
{"level":"info","ts":1752091927.02209,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/filtering/status","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.004413687,"size":662,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:07 GMT"],"Content-Encoding":["gzip"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Length":["662"],"Content-Type":["application/json"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091927.0222147,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/version.json","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Accept":["application/json, text/plain, */*"],"Content-Type":["application/json"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"],"Content-Length":["21"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Mode":["cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":21,"user_id":"","duration":0.00449206,"size":18,"status":200,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:07 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Length":["18"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091927.026607,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/profile","headers":{"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002625046,"size":58,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:07 GMT"],"Content-Length":["58"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091927.0314908,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/assets/favicon.png","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["no-cors"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["image"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001864555,"size":1296,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Accept-Ranges":["bytes"],"Content-Length":["1296"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:07 GMT"],"Content-Type":["image/png"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091927.0513444,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"PUT","host":"tankeguard.duckdns.org","uri":"/control/profile/update","headers":{"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Content-Type":["application/json"],"Sec-Fetch-Site":["same-origin"],"Cookie":["REDACTED"],"Content-Length":["32"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":32,"user_id":"","duration":0.015734499,"size":3,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:07 GMT"],"Content-Length":["3"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Content-Type":["text/plain; charset=utf-8"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091928.0101523,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Cookie":["REDACTED"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002518462,"size":50,"status":200,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["50"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:08 GMT"],"Via":["1.1 Caddy"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091928.0103667,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Referer":["https://tankeguard.duckdns.org/"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002731351,"size":108,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:08 GMT"],"Content-Length":["108"]}}
{"level":"info","ts":1752091928.0106661,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003080734,"size":1804,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:08 GMT"],"Content-Length":["1804"],"Content-Encoding":["gzip"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091928.032332,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Content-Type":["application/json"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Content-Length":["33"],"Sec-Fetch-Site":["same-origin"],"Cookie":["REDACTED"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.001985509,"size":486,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:08 GMT"],"Content-Length":["486"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091928.832022,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Fetch-Dest":["empty"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002251271,"size":108,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 20:12:08 GMT"],"Content-Length":["108"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Content-Type":["application/json"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091928.8320446,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002257022,"size":50,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["50"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:08 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091928.8331933,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Dest":["empty"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003410808,"size":1819,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Content-Length":["1819"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:08 GMT"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091928.848451,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Dest":["empty"],"Accept":["application/json, text/plain, */*"],"Cookie":["REDACTED"],"Content-Type":["application/json"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Mobile":["?0"],"Content-Length":["33"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002329214,"size":486,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["486"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:08 GMT"],"Content-Type":["application/json"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091929.018856,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Cookie":["REDACTED"],"Accept":["application/json, text/plain, */*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001834583,"size":108,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091929.0190551,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Referer":["https://tankeguard.duckdns.org/"],"Priority":["u=1, i"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002048187,"size":50,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Content-Length":["50"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091929.0203304,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Referer":["https://tankeguard.duckdns.org/"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003326563,"size":1810,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["1810"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Encoding":["gzip"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091929.0294964,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Priority":["u=1, i"],"Content-Type":["application/json"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["application/json, text/plain, */*"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Mobile":["?0"],"Origin":["https://tankeguard.duckdns.org"],"Content-Length":["33"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002078685,"size":486,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["486"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091929.2221358,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001972141,"size":50,"status":200,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"X-Content-Type-Options":["nosniff"],"Content-Length":["50"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091929.2223296,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Accept":["application/json, text/plain, */*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00220308,"size":108,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Content-Length":["108"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091929.2230787,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Mode":["cors"],"Cookie":["REDACTED"],"Accept":["application/json, text/plain, */*"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002932801,"size":1845,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Length":["1845"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091929.2421846,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Content-Type":["application/json"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Content-Length":["33"],"Origin":["https://tankeguard.duckdns.org"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002021022,"size":486,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Length":["486"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091929.3885224,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["application/json, text/plain, */*"],"Referer":["https://tankeguard.duckdns.org/"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001853346,"size":108,"status":200,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["108"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091929.3895018,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002760623,"size":1833,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["1833"],"Content-Encoding":["gzip"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091929.4004884,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001862455,"size":50,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["application/json"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["50"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091929.4135973,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Content-Length":["33"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Referer":["https://tankeguard.duckdns.org/"],"Content-Type":["application/json"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.001920377,"size":486,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["486"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091929.5526502,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Accept":["application/json, text/plain, */*"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00201195,"size":108,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Type":["application/json"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091929.5527685,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002080054,"size":50,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["50"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091929.5538685,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003250891,"size":1835,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Length":["1835"],"Content-Encoding":["gzip"]}}
{"level":"info","ts":1752091929.5649745,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Content-Type":["application/json"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Cookie":["REDACTED"],"Content-Length":["33"],"Origin":["https://tankeguard.duckdns.org"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Accept":["application/json, text/plain, */*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.00269765,"size":486,"status":200,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["application/json"],"Content-Length":["486"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091929.7339036,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002106206,"size":50,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["50"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091929.7338965,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002115084,"size":108,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Content-Length":["108"]}}
{"level":"info","ts":1752091929.7346356,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Sec-Fetch-Mode":["cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002890516,"size":1827,"status":200,"resp_headers":{"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["1827"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Encoding":["gzip"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"]}}
{"level":"info","ts":1752091929.7456355,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Fetch-Site":["same-origin"],"Content-Length":["33"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Content-Type":["application/json"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Origin":["https://tankeguard.duckdns.org"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002294634,"size":486,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["486"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091929.8802598,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002179909,"size":50,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["50"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091929.8807476,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00272694,"size":1827,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Content-Length":["1827"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091929.8803525,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002189807,"size":108,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Content-Length":["108"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091929.8916147,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Content-Length":["33"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["application/json, text/plain, */*"],"Content-Type":["application/json"],"Origin":["https://tankeguard.duckdns.org"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Cookie":["REDACTED"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002396786,"size":486,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:09 GMT"],"Content-Length":["486"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091930.0554793,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002170042,"size":108,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091930.0556383,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002313584,"size":50,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Type":["application/json"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["50"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091930.0563667,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003070442,"size":1820,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Encoding":["gzip"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["1820"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091930.0658672,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Content-Length":["33"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Priority":["u=1, i"],"Content-Type":["application/json"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002370015,"size":486,"status":200,"resp_headers":{"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["486"]}}
{"level":"info","ts":1752091930.2443829,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Referer":["https://tankeguard.duckdns.org/"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Dest":["empty"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002200169,"size":50,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["50"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091930.2446077,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Sec-Fetch-Mode":["cors"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002434689,"size":108,"status":200,"resp_headers":{"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Vary":["Accept-Encoding"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091930.24667,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.004513552,"size":1809,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Length":["1809"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091930.253374,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Origin":["https://tankeguard.duckdns.org"],"Content-Type":["application/json"],"Sec-Fetch-Dest":["empty"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Priority":["u=1, i"],"Content-Length":["33"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002258622,"size":486,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["486"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Frame-Options":["SAMEORIGIN"]}}
{"level":"info","ts":1752091930.42131,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00221753,"size":50,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Content-Length":["50"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091930.4215834,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002495711,"size":108,"status":200,"resp_headers":{"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["108"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"]}}
{"level":"info","ts":1752091930.4225035,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003431131,"size":1820,"status":200,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Vary":["Accept-Encoding"],"Content-Length":["1820"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"]}}
{"level":"info","ts":1752091930.4304745,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Content-Type":["application/json"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["cors"],"Content-Length":["33"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Cookie":["REDACTED"],"Referer":["https://tankeguard.duckdns.org/"],"Priority":["u=1, i"],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002001069,"size":486,"status":200,"resp_headers":{"Content-Length":["486"],"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"]}}
{"level":"info","ts":1752091930.5764482,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002009541,"size":108,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["108"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091930.5765433,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002094976,"size":50,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Length":["50"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091930.577743,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003301764,"size":1839,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Encoding":["gzip"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["1839"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091930.5862,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Cookie":["REDACTED"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Referer":["https://tankeguard.duckdns.org/"],"Content-Type":["application/json"],"Sec-Fetch-Site":["same-origin"],"Content-Length":["33"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002484919,"size":486,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["486"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091930.748103,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Mode":["cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002606646,"size":108,"status":200,"resp_headers":{"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Content-Type":["application/json"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["108"]}}
{"level":"info","ts":1752091930.7482598,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Cookie":["REDACTED"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002463679,"size":50,"status":200,"resp_headers":{"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Type":["application/json"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["50"],"Vary":["Accept-Encoding"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091930.749168,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003691801,"size":1809,"status":200,"resp_headers":{"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["1809"],"Content-Encoding":["gzip"]}}
{"level":"info","ts":1752091930.7576706,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Content-Type":["application/json"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Mobile":["?0"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Dest":["empty"],"Content-Length":["33"],"Cookie":["REDACTED"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002001501,"size":486,"status":200,"resp_headers":{"Content-Type":["application/json"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["486"]}}
{"level":"info","ts":1752091930.9208171,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=1, i"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002844476,"size":108,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["108"],"Vary":["Accept-Encoding"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091930.9208627,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Mode":["cors"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Site":["same-origin"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Dest":["empty"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002820184,"size":50,"status":200,"resp_headers":{"X-Frame-Options":["SAMEORIGIN"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["50"],"Content-Type":["application/json"]}}
{"level":"info","ts":1752091930.9215207,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00346201,"size":1821,"status":200,"resp_headers":{"Content-Length":["1821"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Content-Encoding":["gzip"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"]}}
{"level":"info","ts":1752091930.9294796,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Content-Type":["application/json"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"Content-Length":["33"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Origin":["https://tankeguard.duckdns.org"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Dest":["empty"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002190519,"size":486,"status":200,"resp_headers":{"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:10 GMT"],"Content-Length":["486"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"X-Content-Type-Options":["nosniff"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"info","ts":1752091931.5508175,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002220336,"size":50,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Date":["Wed, 09 Jul 2025 20:12:11 GMT"],"Content-Length":["50"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091931.5507636,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Sec-Fetch-Mode":["cors"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Site":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002169358,"size":108,"status":200,"resp_headers":{"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Content-Length":["108"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:11 GMT"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091931.5516388,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Priority":["u=1, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Accept":["application/json, text/plain, */*"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003069684,"size":1819,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Date":["Wed, 09 Jul 2025 20:12:11 GMT"],"Content-Length":["1819"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"]}}
{"level":"info","ts":1752091931.5632455,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Cookie":["REDACTED"],"Referer":["https://tankeguard.duckdns.org/"],"Accept":["application/json, text/plain, */*"],"Content-Type":["application/json"],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Dest":["empty"],"Content-Length":["33"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Site":["same-origin"],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Priority":["u=1, i"],"Sec-Fetch-Mode":["cors"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.001818153,"size":486,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["486"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Date":["Wed, 09 Jul 2025 20:12:11 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"]}}
{"level":"info","ts":1752091931.7154307,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats/config","headers":{"Sec-Ch-Ua-Mobile":["?0"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Priority":["u=1, i"],"Sec-Fetch-Dest":["empty"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept":["application/json, text/plain, */*"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002058693,"size":50,"status":200,"resp_headers":{"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Date":["Wed, 09 Jul 2025 20:12:11 GMT"],"Content-Length":["50"]}}
{"level":"info","ts":1752091931.7155406,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/access/list","headers":{"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Dest":["empty"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Fetch-Mode":["cors"],"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Priority":["u=1, i"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002270282,"size":108,"status":200,"resp_headers":{"Via":["1.1 Caddy"],"Content-Length":["108"],"Content-Type":["application/json"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:11 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091931.7163029,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/control/stats","headers":{"Referer":["https://tankeguard.duckdns.org/"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=1, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Sec-Fetch-Site":["same-origin"],"Sec-Fetch-Mode":["cors"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Mobile":["?0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.003034554,"size":1816,"status":200,"resp_headers":{"Content-Type":["application/json"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:11 GMT"],"Content-Length":["1816"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"]}}
{"level":"info","ts":1752091931.7268918,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42950","client_ip":"**********","proto":"HTTP/2.0","method":"POST","host":"tankeguard.duckdns.org","uri":"/control/clients/search","headers":{"Sec-Fetch-Site":["same-origin"],"Referer":["https://tankeguard.duckdns.org/"],"Content-Type":["application/json"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""],"Content-Length":["33"],"Sec-Fetch-Mode":["cors"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Origin":["https://tankeguard.duckdns.org"],"Sec-Fetch-Dest":["empty"],"Priority":["u=1, i"],"Accept":["application/json, text/plain, */*"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Mobile":["?0"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":33,"user_id":"","duration":0.002287303,"size":486,"status":200,"resp_headers":{"Vary":["Accept-Encoding"],"X-Frame-Options":["SAMEORIGIN"],"Content-Type":["application/json"],"Date":["Wed, 09 Jul 2025 20:12:11 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Content-Type-Options":["nosniff"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Length":["486"]}}
{"level":"info","ts":1752091932.9266868,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"35558","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001824668,"size":34,"status":302,"resp_headers":{"Via":["1.1 Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/html; charset=utf-8"],"Date":["Wed, 09 Jul 2025 20:12:12 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"X-Content-Type-Options":["nosniff"],"Location":["/login.html"],"Content-Length":["34"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"X-Frame-Options":["SAMEORIGIN"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"]}}
{"level":"info","ts":1752091933.105266,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"35558","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeguard.duckdns.org","uri":"/login.html","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Referer":["https://tankeguard.duckdns.org"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeguard.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.002089394,"size":595,"status":200,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Encoding":["gzip"],"Content-Type":["text/html; charset=utf-8"],"Vary":["Accept-Encoding"],"Date":["Wed, 09 Jul 2025 20:12:13 GMT"],"Referrer-Policy":["strict-origin-when-cross-origin"],"Strict-Transport-Security":["max-age=31536000; includeSubDomains; preload"],"Via":["1.1 Caddy"],"Content-Security-Policy":["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["SAMEORIGIN"]}}
