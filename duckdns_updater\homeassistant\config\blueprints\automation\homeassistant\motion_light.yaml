blueprint:
  name: Motion-activated Light
  description: Turn on a light when motion is detected.
  domain: automation
  source_url: https://github.com/home-assistant/core/blob/dev/homeassistant/components/automation/blueprints/motion_light.yaml
  author: Home Assistant
  input:
    motion_entity:
      name: Motion Sensor
      selector:
        entity:
          filter:
            - device_class: occupancy
              domain: binary_sensor
            - device_class: motion
              domain: binary_sensor
    light_target:
      name: Light
      selector:
        target:
          entity:
            domain: light
    no_motion_wait:
      name: Wait time
      description: Time to leave the light on after last motion is detected.
      default: 120
      selector:
        number:
          min: 0
          max: 3600
          unit_of_measurement: seconds

# If motion is detected within the delay,
# we restart the script.
mode: restart
max_exceeded: silent

triggers:
  trigger: state
  entity_id: !input motion_entity
  from: "off"
  to: "on"

actions:
  - alias: "Turn on the light"
    action: light.turn_on
    target: !input light_target
  - alias: "Wait until there is no motion from device"
    wait_for_trigger:
      trigger: state
      entity_id: !input motion_entity
      from: "on"
      to: "off"
  - alias: "Wait the number of seconds that has been set"
    delay: !input no_motion_wait
  - alias: "Turn off the light"
    action: light.turn_off
    target: !input light_target
