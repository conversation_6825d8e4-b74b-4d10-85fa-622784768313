# === CONFIGURACIÓN NTFY.SH SERVER ===
# Configuración del servidor de notificaciones

# URL base del servidor
base-url: "https://tankenoti.duckdns.org"

# Puerto de escucha (interno del contenedor)
listen-http: ":80"

# Configuración de cache
cache-file: "/var/cache/ntfy/cache.db"
cache-duration: "12h"

# Configuración de autenticación
auth-file: "/var/lib/ntfy/auth.db"
auth-default-access: "deny-all"

# Configuración de logs
log-level: "INFO"
log-format: "text"

# Configuración de límites
visitor-request-limit-burst: 60
visitor-request-limit-replenish: "5s"
visitor-email-limit-burst: 16
visitor-email-limit-replenish: "1h"

# Configuración de retención de mensajes
message-limit: 10000
message-size-limit: "512k"

# Configuración de archivos adjuntos (deshabilitado por seguridad)
attachment-cache-dir: ""
attachment-total-size-limit: "0"

# Configuración de upstream (para temas públicos)
upstream-base-url: "https://ntfy.sh"

# Configuración de CORS para API
enable-signup: false
enable-login: true
enable-reservations: false

# Configuración de métricas
enable-metrics: true

# Configuración de web push (deshabilitado)
web-push-public-key: ""
web-push-private-key: ""
web-push-file: ""
web-push-email-address: ""

# Configuración de SMTP (deshabilitado)
smtp-sender-addr: ""
smtp-sender-user: ""
smtp-sender-pass: ""
smtp-sender-from: ""

# Configuración de Twilio (deshabilitado)
twilio-account: ""
twilio-auth-token: ""
twilio-phone-number: ""

# Configuración específica para Docker
behind-proxy: true
