version: "3.8"
services:
  # === CADDY REVERSE PROXY ===
  caddy:
    image: caddy:2-alpine
    container_name: caddy_proxy
    restart: unless-stopped
    ports:
      - "80:80"     # HTTP
      - "443:443"   # HTTPS
      - "443:443/udp" # HTTP/3 (QUIC)
      - "8081:8081" # NTFY.SH HTTPS
    volumes:
      # Monta el Caddyfile adaptado para Docker (mantiene tu configuración)
      - C:\docker\Caddyfile.host:/etc/caddy/Caddyfile:ro
      # Datos persistentes de Caddy (certificados SSL, etc.)
      - caddy_data:/data
      - caddy_config:/config
      # Monta los directorios de logs manteniendo la estructura de tu configuración original
      - C:\docker\duckdns_updater\caddy\logs:/var/log/caddy
    networks:
      - caddy_network
    depends_on:
      - duckdns_jucago705
      - duckdns_jucago706
    healthcheck:
      test: ["CMD", "caddy", "validate", "--config", "/etc/caddy/Caddyfile"]
      interval: 5m
      timeout: 10s
      retries: 3
      start_period: 30s
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
  # --- DuckDns 1 ---
  duckdns_jucago705:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago705
    restart: unless-stopped
    environment:
      - PUID=1000 # Deja esto como 1000 para Windows
      - PGID=1000 # Deja esto como 1000 para Windows
      - TZ=Europe/Madrid # Zona horaria, cámbiala si estás en otro lugar
      - TOKEN=*************-4963-bd13-fc1199f2f5d9 # <--- ¡CAMBIA ESTO!
      - DOMAINS=tanketorrent.duckdns.org, tankeflix.duckdns.org, tankesonarr.duckdns.org, tankejackett.duckdns.org, tankeeee2.duckdns.org, tankejellyseerr.duckdns.org
      - SUBDOMAINS=false # Pon 'true' si usas subdominios que no estén en la lista DOMAINS
      - LOG_FILE=true # Opcional: crea un archivo de log dentro del volumen
    volumes:
      - ./config:/config # Mapea el volumen para guardar la configuración y logs
    networks:
      - caddy_network

  # --- DuckDns 2 ---
  duckdns_jucago706:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago706
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - TOKEN=1b37bdd3-38a3-4e5f-8eea-57a3c7419c7d
      - DOMAINS=tankeguard.duckdns.org, tankejellyseerr.duckdns.org, tankeradarr.duckdns.org, tankewatch.duckdns.org, tankenoti.duckdns.org
      - SUBDOMAINS=false
      - LOG_FILE=true
    volumes:
      - ./config:/config
    networks:
      - caddy_network

  # --- DuckDns 3 ---
  duckdns_jucago705.2:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago705.2
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - TOKEN=0e8197b7-5fc0-437d-aa89-0cb74b3b511f
      - DOMAINS=tankenoti.duckdns.org
      - SUBDOMAINS=false
      - LOG_FILE=tru
    volumes:
      - ./config:/config
    networks:
      - caddy_network
      

# === ADGUARD HOME DNS SERVER & AD BLOCKER ===
  adguard:
    image: adguard/adguardhome:latest
    container_name: adguard
    environment:
      - TZ=Europe/Madrid
    volumes:
      # Configuración persistente de AdGuard Home
      - C:\docker\duckdns_updater\adguard\work:/opt/adguardhome/work
      - C:\docker\duckdns_updater\adguard\conf:/opt/adguardhome/conf
    ports:
      # Puerto DNS (TCP y UDP) - Puerto estándar para DNS
      - "53:53/tcp"
      - "53:53/udp"
      # Puerto para configuración inicial (se desactiva automáticamente después del setup)
      - "3000:3000/tcp"
      # Puerto para interfaz web (después de la configuración inicial) - Cambiado para evitar conflicto con Caddy
      - "8080:80/tcp"
      # Puertos adicionales para DNS-over-HTTPS y DNS-over-TLS (opcionales) - Cambiado para evitar conflicto con Caddy
      - "8443:443/tcp"
      - "853:853/tcp"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Privilegios necesarios para binding a puertos bajos (53, 80)
    cap_add:
      - NET_ADMIN
      - NET_RAW

# === HOMEPAGE DASHBOARD ===
  homepage:
    image: ghcr.io/gethomepage/homepage:latest
    container_name: homepage
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      # Permitir acceso desde proxy reverso y acceso directo
      - HOMEPAGE_ALLOWED_HOSTS=tankeeee2.duckdns.org,*************:7575,host.docker.internal:7575,localhost:7575
    volumes:
      # Configuración persistente de Homepage
      - C:\docker\duckdns_updater\homepage\config:/app/config
      - C:\docker\duckdns_updater\homepage\icons:/app/public/icons
      # Acceso de solo lectura al socket de Docker para widgets
      - //var/run/docker.sock:/var/run/docker.sock:ro
    ports:
      - "7575:3000"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"

# === PORTAINER DOCKER MANAGEMENT ===
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    environment:
      - TZ=Europe/Madrid
    volumes:
      # Configuración persistente de Portainer
      - C:\docker\duckdns_updater\portainer\data:/data
      # Acceso completo al socket de Docker para gestión
      - //var/run/docker.sock:/var/run/docker.sock
    ports:
      - "9000:9000"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"

# === NTFY.SH NOTIFICATION SERVER ===
  ntfy:
    image: binwiederhier/ntfy:latest
    container_name: ntfy
    environment:
      - TZ=Europe/Madrid
      - NTFY_BASE_URL=https://tankewatch.duckdns.org
      - NTFY_UPSTREAM_BASE_URL=https://ntfy.sh
      - NTFY_CACHE_FILE=/var/cache/ntfy/cache.db
      # Autenticación deshabilitada para facilitar el uso
      # - NTFY_AUTH_FILE=/var/lib/ntfy/auth.db
      # - NTFY_AUTH_DEFAULT_ACCESS=read-write
      - NTFY_BEHIND_PROXY=true
      - NTFY_LOG_LEVEL=INFO
    volumes:
      # Configuración persistente de ntfy.sh
      - C:\docker\duckdns_updater\ntfy\cache:/var/cache/ntfy
      - C:\docker\duckdns_updater\ntfy\lib:/var/lib/ntfy
      - C:\docker\duckdns_updater\ntfy\config:/etc/ntfy
    ports:
      - "8081:80"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: serve

# === WATCHTOWER AUTO-UPDATER ===
  watchtower:
    image: containrrr/watchtower:latest
    container_name: watchtower
    environment:
      - TZ=Europe/Madrid
      # Programar actualizaciones diarias a las 3:00 AM
      - WATCHTOWER_SCHEDULE=0 0 3 * * *
      # Limpiar imágenes antiguas después de actualizar
      - WATCHTOWER_CLEANUP=true
      # Incluir contenedores detenidos
      - WATCHTOWER_INCLUDE_STOPPED=true
      # Reiniciar contenedores uno por uno (más seguro)
      - WATCHTOWER_ROLLING_RESTART=true
      # Configuración de notificaciones via webhook
      - WATCHTOWER_NOTIFICATION_URL=generic+https://tankewatch.duckdns.org/docker-updates
      - WATCHTOWER_NOTIFICATIONS=shoutrrr
      # Logs detallados para monitoreo manual
      - WATCHTOWER_DEBUG=true
      - WATCHTOWER_LOG_LEVEL=info
    volumes:
      # Acceso completo al socket de Docker para gestión de contenedores
      - //var/run/docker.sock:/var/run/docker.sock
      # Logs persistentes
      - C:\docker\duckdns_updater\watchtower\logs:/logs
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Etiquetas para que Watchtower se actualice a sí mismo
    labels:
      - "com.centurylinklabs.watchtower.enable=true"


# === VOLÚMENES ===
volumes:
  caddy_data:
    driver: local
  caddy_config:
    driver: local


# === REDES ===
networks:
  caddy_network:
    driver: bridge
    name: caddy_network