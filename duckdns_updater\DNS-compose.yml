version: "3.8"
services:
  # === CADDY REVERSE PROXY ===
  caddy:
    image: caddy:2-alpine
    container_name: caddy_proxy
    restart: unless-stopped
    ports:
      - "80:80"     # HTTP
      - "443:443"   # HTTPS
      - "443:443/udp" # HTTP/3 (QUIC)
    volumes:
      # Monta el Caddyfile adaptado para Docker (mantiene tu configuración)
      - C:\docker\Caddyfile.host:/etc/caddy/Caddyfile:ro
      # Datos persistentes de Caddy (certificados SSL, etc.)
      - caddy_data:/data
      - caddy_config:/config
      # Monta los directorios de logs manteniendo la estructura de tu configuración original
      - C:\docker\duckdns_updater\caddy\logs:/var/log/caddy
    networks:
      - caddy_network
    depends_on:
      - duckdns_jucago705
      - duckdns_jucago706
    healthcheck:
      test: ["CMD", "caddy", "validate", "--config", "/etc/caddy/Caddyfile"]
      interval: 5m
      timeout: 10s
      retries: 3
      start_period: 30s
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
  # --- DuckDns 1 ---
  duckdns_jucago705:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago705
    restart: unless-stopped
    environment:
      - PUID=1000 # Deja esto como 1000 para Windows
      - PGID=1000 # Deja esto como 1000 para Windows
      - TZ=Europe/Madrid # Zona horaria, cámbiala si estás en otro lugar
      - TOKEN=*************-4963-bd13-fc1199f2f5d9 # <--- ¡CAMBIA ESTO!
      - DOMAINS=tanketorrent.duckdns.org, tankeflix.duckdns.org, tankesonarr.duckdns.org, tankejackett.duckdns.org, tankeeee2.duckdns.org, tankeguard.duckdns.org
      - SUBDOMAINS=false # Pon 'true' si usas subdominios que no estén en la lista DOMAINS
      - LOG_FILE=true # Opcional: crea un archivo de log dentro del volumen
    volumes:
      - ./config:/config # Mapea el volumen para guardar la configuración y logs
    networks:
      - caddy_network

  # --- DuckDns 2 ---
  duckdns_jucago706:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago706
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - TOKEN=1b37bdd3-38a3-4e5f-8eea-57a3c7419c7d
      - DOMAINS=tankeradarr.duckdns.org, tankeguard.duckdns.org
      - SUBDOMAINS=false
      - LOG_FILE=tru
    volumes:
      - ./config:/config
    networks:
      - caddy_network
      
# === HOME ASSISTANT SMART HOME HUB ===
  homeassistant:
    image: ghcr.io/home-assistant/home-assistant:stable
    container_name: homeassistant
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      # Configuración persistente de Home Assistant
      - C:\docker\duckdns_updater\homeassistant\config:/config
      # Opcional: acceso a dispositivos USB (descomenta si necesitas Zigbee/Z-Wave)
      # - /dev/ttyUSB0:/dev/ttyUSB0
    ports:
      - "8123:8123"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Privilegios necesarios para acceso a dispositivos (opcional)
    # privileged: true

# === ADGUARD HOME DNS SERVER & AD BLOCKER ===
  adguard:
    image: adguard/adguardhome:latest
    container_name: adguard
    environment:
      - TZ=Europe/Madrid
    volumes:
      # Configuración persistente de AdGuard Home
      - C:\docker\duckdns_updater\adguard\work:/opt/adguardhome/work
      - C:\docker\duckdns_updater\adguard\conf:/opt/adguardhome/conf
    ports:
      # Puerto DNS (TCP y UDP) - Puerto estándar para DNS
      - "53:53/tcp"
      - "53:53/udp"
      # Puerto para configuración inicial (se desactiva automáticamente después del setup)
      - "3000:3000/tcp"
      # Puerto para interfaz web (después de la configuración inicial) - Cambiado para evitar conflicto con Caddy
      - "8080:80/tcp"
      # Puertos adicionales para DNS-over-HTTPS y DNS-over-TLS (opcionales) - Cambiado para evitar conflicto con Caddy
      - "8443:443/tcp"
      - "853:853/tcp"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Privilegios necesarios para binding a puertos bajos (53, 80)
    cap_add:
      - NET_ADMIN
      - NET_RAW


# === VOLÚMENES ===
volumes:
  caddy_data:
    driver: local
  caddy_config:
    driver: local


# === REDES ===
networks:
  caddy_network:
    driver: bridge
    name: caddy_network