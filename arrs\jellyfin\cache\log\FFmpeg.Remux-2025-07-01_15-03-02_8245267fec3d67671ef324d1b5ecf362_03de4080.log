{"Protocol":0,"Id":"8245267fec3d67671ef324d1b5ecf362","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 3/<PERSON> and <PERSON><PERSON><PERSON> - S03E02 - Rickmancing the Stone HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":271978803,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S03E02 - Rickmancing the Stone HDTV-720p","IsRemote":false,"ETag":"13b0efab0a166f0f2abdcc5ca49efd75","RunTimeTicks":12991040000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1201796,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":102,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":119,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1674870,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E02 - Rickmancing the Stone HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x582670d5b840] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E02 - Rickmancing the Stone HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Tras el Rickazón verde
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Summer empieza a actuar en este episodio, bro. Y Morty se vuelve loco, bro.
    show            : Rick y Morty
    episode_id      : 2
    season_number   : 3
  Duration: 00:21:39.10, start: 0.000000, bitrate: 1674 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1201 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x582670d75200] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1201 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa0.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa1.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa2.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa3.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa4.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa5.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa6.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa7.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa8.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa9.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa10.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa11.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa12.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa13.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa14.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa15.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa16.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa17.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa18.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa19.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa20.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa21.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa22.ts' for writing
size=N/A time=00:00:45.15 bitrate=N/A speed=45.1x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa23.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa24.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa25.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa26.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa27.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa28.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa29.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa30.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa31.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa32.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa33.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa34.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa35.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa36.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa37.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa38.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa39.ts' for writing
size=N/A time=00:02:25.60 bitrate=N/A speed=  97x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa40.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa41.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa42.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa43.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa44.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa45.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa46.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa47.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa48.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa49.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa50.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa51.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa52.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa53.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa54.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa55.ts' for writing
size=N/A time=00:03:58.27 bitrate=N/A speed= 119x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa56.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa57.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa58.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa59.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa60.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa61.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa62.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa63.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa64.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa65.ts' for writing
size=N/A time=00:04:58.52 bitrate=N/A speed= 119x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa66.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa67.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa68.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa69.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa70.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa71.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa72.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa73.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa74.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa75.ts' for writing
size=N/A time=00:06:04.64 bitrate=N/A speed= 121x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa76.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa77.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa78.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa79.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa80.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa81.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa82.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa83.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa84.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa85.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa86.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa87.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa88.ts' for writing
size=N/A time=00:07:19.10 bitrate=N/A speed= 125x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa89.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa90.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa91.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa92.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa93.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa94.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa95.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa96.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa97.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa98.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa99.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa100.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa101.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa102.ts' for writing
size=N/A time=00:08:42.56 bitrate=N/A speed= 131x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa103.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa104.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa105.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa106.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa107.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa108.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa109.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa110.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa111.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa112.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa113.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa114.ts' for writing
size=N/A time=00:09:58.36 bitrate=N/A speed= 133x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa115.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa116.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa117.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa118.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa119.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa120.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa121.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa122.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa123.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa124.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa125.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa126.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa127.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa128.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa129.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa130.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa131.ts' for writing
size=N/A time=00:11:34.56 bitrate=N/A speed= 139x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa132.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa133.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa134.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa135.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa136.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa137.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa138.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa139.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa140.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa141.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa142.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa143.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa144.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa145.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa146.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa147.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa148.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa149.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa150.ts' for writing
size=N/A time=00:13:31.23 bitrate=N/A speed= 147x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa151.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa152.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa153.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa154.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa155.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa156.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa157.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa158.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa159.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa160.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa161.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa162.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa163.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa164.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa165.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa166.ts' for writing
size=N/A time=00:15:08.44 bitrate=N/A speed= 151x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa167.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa168.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa169.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa170.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa171.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa172.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa173.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa174.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa175.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa176.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa177.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa178.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa179.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa180.ts' for writing
size=N/A time=00:16:34.04 bitrate=N/A speed= 153x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa181.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa182.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa183.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa184.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa185.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa186.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa187.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa188.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa189.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa190.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa191.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa192.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa193.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa194.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa195.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa196.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa197.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa198.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa199.ts' for writing
size=N/A time=00:18:22.56 bitrate=N/A speed= 157x    
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa200.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa201.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa202.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa203.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa204.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa205.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa206.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa207.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa208.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa209.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa210.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa211.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa212.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa213.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa214.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa215.ts' for writing
[hls @ 0x582670d754c0] Opening '/cache/transcodes/e85bfe4b3871411fb14a7f8a012f7ffa216.ts' for writing
[out#0/hls @ 0x582670d75200] video:190564KiB audio:20298KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:20:01.17 bitrate=N/A speed= 162x    