{"Protocol":0,"Id":"d08d2edd5fa616f92fccd92be7c6e3dc","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 3/<PERSON> and <PERSON><PERSON><PERSON> - S03E01 - The Rickshank Rickdemption HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":295416756,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S03E01 - The Rickshank Rickdemption HDTV-720p","IsRemote":false,"ETag":"81c6460caac52557adc6ebca560677f9","RunTimeTicks":13431040000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1328885,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":105,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":131,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1759606,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E01 - The Rickshank Rickdemption HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x5688abcc5840] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E01 - The Rickshank Rickdemption HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Cadena Rickpetua
    date            : 2024
    encoder         : Lavf61.9.106
    description     : La familia se ocupa de sus problemas en este episodio  ¿Qué hará Rick? Este es un episodio real, bro.
    show            : Rick y Morty
    episode_id      : 1
    season_number   : 3
  Duration: 00:22:23.10, start: 0.000000, bitrate: 1759 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1328 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x5688abd34740] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1328 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b0.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b1.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b2.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b3.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b4.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b5.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b6.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b7.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b8.ts' for writing
size=N/A time=00:00:12.19 bitrate=N/A speed=12.2x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b9.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b10.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b11.ts' for writing
size=N/A time=00:00:32.16 bitrate=N/A speed=21.4x    
size=N/A time=00:00:35.87 bitrate=N/A speed=17.9x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b12.ts' for writing
size=N/A time=00:00:38.75 bitrate=N/A speed=15.5x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b13.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b14.ts' for writing
size=N/A time=00:00:52.00 bitrate=N/A speed=17.3x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b15.ts' for writing
size=N/A time=00:00:59.10 bitrate=N/A speed=16.9x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b16.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b17.ts' for writing
size=N/A time=00:01:06.59 bitrate=N/A speed=16.6x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b18.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b19.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b20.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b21.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b22.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b23.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b24.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b25.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b26.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b27.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b28.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b29.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b30.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b31.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b32.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b33.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b34.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b35.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b36.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b37.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b38.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b39.ts' for writing
size=N/A time=00:03:23.58 bitrate=N/A speed=45.2x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b40.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b41.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b42.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b43.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b44.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b45.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b46.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b47.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b48.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b49.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b50.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b51.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b52.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b53.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b54.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b55.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b56.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b57.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b58.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b59.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b60.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b61.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b62.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b63.ts' for writing
size=N/A time=00:05:41.02 bitrate=N/A speed=68.2x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b64.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b65.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b66.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b67.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b68.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b69.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b70.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b71.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b72.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b73.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b74.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b75.ts' for writing
size=N/A time=00:06:53.18 bitrate=N/A speed=75.1x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b76.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b77.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b78.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b79.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b80.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b81.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b82.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b83.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b84.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b85.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b86.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b87.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b88.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b89.ts' for writing
size=N/A time=00:08:20.96 bitrate=N/A speed=83.5x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b90.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b91.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b92.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b93.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b94.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b95.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b96.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b97.ts' for writing
size=N/A time=00:09:08.19 bitrate=N/A speed=84.3x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b98.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b99.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b100.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b101.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b102.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b103.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b104.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b105.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b106.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b107.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b108.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b109.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b110.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b111.ts' for writing
size=N/A time=00:10:30.56 bitrate=N/A speed=  90x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b112.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b113.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b114.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b115.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b116.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b117.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b118.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b119.ts' for writing
size=N/A time=00:11:18.56 bitrate=N/A speed=90.4x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b120.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b121.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b122.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b123.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b124.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b125.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b126.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b127.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b128.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b129.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b130.ts' for writing
size=N/A time=00:12:22.56 bitrate=N/A speed=92.8x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b131.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b132.ts' for writing
size=N/A time=00:12:39.20 bitrate=N/A speed=89.3x    
size=N/A time=00:12:41.44 bitrate=N/A speed=84.6x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b133.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b134.ts' for writing
size=N/A time=00:12:51.74 bitrate=N/A speed=81.2x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b135.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b136.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b137.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b138.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b139.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b140.ts' for writing
size=N/A time=00:13:26.36 bitrate=N/A speed=80.6x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b141.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b142.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b143.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b144.ts' for writing
size=N/A time=00:13:52.96 bitrate=N/A speed=79.3x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b145.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b146.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b147.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b148.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b149.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b150.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b151.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b152.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b153.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b154.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b155.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b156.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b157.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b158.ts' for writing
size=N/A time=00:15:10.56 bitrate=N/A speed=82.8x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b159.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b160.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b161.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b162.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b163.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b164.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b165.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b166.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b167.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b168.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b169.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b170.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b171.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b172.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b173.ts' for writing
size=N/A time=00:16:45.40 bitrate=N/A speed=87.4x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b174.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b175.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b176.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b177.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b178.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b179.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b180.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b181.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b182.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b183.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b184.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b185.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b186.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b187.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b188.ts' for writing
size=N/A time=00:18:10.56 bitrate=N/A speed=90.9x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b189.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b190.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b191.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b192.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b193.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b194.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b195.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b196.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b197.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b198.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b199.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b200.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b201.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b202.ts' for writing
size=N/A time=00:19:36.64 bitrate=N/A speed=94.1x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b203.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b204.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b205.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b206.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b207.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b208.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b209.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b210.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b211.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b212.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b213.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b214.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b215.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b216.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b217.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b218.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b219.ts' for writing
size=N/A time=00:21:18.56 bitrate=N/A speed=98.3x    
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b220.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b221.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b222.ts' for writing
[hls @ 0x5688abd18a80] Opening '/cache/transcodes/d229b8c2dd06c44080a71a03ae9fe63b223.ts' for writing
[out#0/hls @ 0x5688abd34740] video:217855KiB audio:20986KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:21:39.09 bitrate=N/A speed=98.7x    