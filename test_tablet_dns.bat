@echo off
echo ========================================
echo Verificando conectividad AdGuard Home para tablet
echo ========================================
echo.

echo 1. Verificando que AdGuard Home responde en puerto 53...
nslookup google.com 192.168.18.10

echo.
echo 2. Verificando puerto DNS-over-TLS (853)...
telnet 192.168.18.10 853

echo.
echo 3. Verificando acceso web a AdGuard Home...
curl -I http://192.168.18.10:8080

echo.
echo 4. Verificando dominio externo...
curl -I https://tankeguard.duckdns.org

echo.
echo ========================================
echo Verificacion completada!
echo.
echo Si todos los tests pasan, tu tablet podra conectarse correctamente.
echo ========================================
pause
