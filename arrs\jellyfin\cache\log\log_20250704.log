[2025-07-04 00:40:31.663 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 00:40:31.743 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 00:40:31.745 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-04 01:59:57.780 +02:00] [INF] [207] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-04 02:00:00.000 +02:00, which is 00:00:02.2808877 from now.
[2025-07-04 01:59:58.908 +02:00] [INF] [211] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-04 02:00:00.331 +02:00] [INF] [211] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 3 seconds
[2025-07-04 02:00:01.310 +02:00] [INF] [209] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-05 02:00:00.000 +02:00, which is 23:59:58.6894099 from now.
[2025-07-04 02:00:28.574 +02:00] [INF] [209] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-04 02:00:28.790 +02:00] [INF] [209] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 26 seconds
[2025-07-04 02:59:57.101 +02:00] [INF] [235] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-04 03:00:00.000 +02:00, which is 00:00:02.8990240 from now.
[2025-07-04 03:00:00.583 +02:00] [INF] [234] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 4 seconds
[2025-07-04 03:00:01.002 +02:00] [INF] [243] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-05 03:00:00.000 +02:00, which is 23:59:58.9979126 from now.
[2025-07-04 03:00:02.199 +02:00] [INF] [243] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-04 03:20:28.162 +02:00] [INF] [3] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-04 03:20:28.456 +02:00] [INF] [3] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-04 03:20:28.745 +02:00] [INF] [3] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-04 03:20:28.996 +02:00] [INF] [3] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-04 03:20:31.934 +02:00] [INF] [218] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 4 seconds
[2025-07-04 03:20:34.833 +02:00] [WRN] [3] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-04 03:20:37.860 +02:00] [WRN] [3] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-04 03:21:03.242 +02:00] [INF] [90] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-04 03:21:06.985 +02:00] [INF] [114] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-04 03:21:06.985 +02:00] [INF] [114] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-04 03:21:08.466 +02:00] [ERR] [207] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-04 03:21:39.544 +02:00] [INF] [143] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 1 minute(s) and 11 seconds
[2025-07-04 03:21:39.975 +02:00] [INF] [28] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-04 03:21:40.100 +02:00] [INF] [3] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-04 03:21:40.316 +02:00] [INF] [48] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-04 04:39:46.414 +02:00] [INF] [95] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 04:39:46.415 +02:00] [INF] [95] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 04:39:46.578 +02:00] [INF] [95] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-04 12:06:46.904 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-04 12:06:47.003 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_DATA_DIR, /config]"]
[2025-07-04 12:06:47.010 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-04 12:06:47.015 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-04 12:06:47.015 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-04 12:06:47.016 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-04 12:06:47.016 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-04 12:06:47.016 +02:00] [INF] [1] Main: Processor count: 12
[2025-07-04 12:06:47.017 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-04 12:06:47.017 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-04 12:06:47.017 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-04 12:06:47.018 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-04 12:06:47.019 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-04 12:06:47.024 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-04 12:06:47.045 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-04 12:06:47.606 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-04 12:06:47.868 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-04 12:06:47.973 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-04 12:06:47.991 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-04 12:06:48.025 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-04 12:06:48.233 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-04 12:06:48.233 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-04 12:06:48.234 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-04 12:06:48.235 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-04 12:06:48.236 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-04 12:06:48.236 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-04 12:06:48.236 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-04 12:07:02.967 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-04 12:07:02.970 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-04 12:07:02.971 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-04 12:07:02.971 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-04 12:07:02.972 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-04 12:07:03.002 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-04 12:07:03.002 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-04 12:07:03.124 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-04 12:07:03.757 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-04 12:07:03.802 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-04 12:07:03.809 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-04 12:07:03.839 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-05 03:00:00.000 +02:00, which is 14:52:56.1603474 from now.
[2025-07-04 12:07:03.894 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-05 02:00:00.000 +02:00, which is 13:52:56.1055313 from now.
[2025-07-04 12:07:03.956 +02:00] [INF] [11] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-04 12:07:04.151 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-04 12:07:04.230 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-04 12:07:04.254 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-04 12:07:04.287 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-04 12:07:04.467 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-04 12:07:05.182 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-04 12:07:06.920 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-04 12:07:06.955 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-04 12:07:08.654 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-07-04 12:07:17.261 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-04 12:07:17.274 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-04 12:07:17.274 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-04 12:07:17.274 +02:00] [INF] [1] Main: Startup complete 0:00:31.3815913
[2025-07-04 14:42:43.826 +02:00] [INF] [130] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 14:42:43.830 +02:00] [INF] [130] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 14:42:43.834 +02:00] [INF] [130] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-04 16:57:17.008 +02:00] [INF] [219] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 16:57:17.011 +02:00] [INF] [219] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 16:57:17.012 +02:00] [INF] [219] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-04 17:16:11.981 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-04 17:16:12.119 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_LOG_DIR, /config/log]"]
[2025-07-04 17:16:12.121 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-04 17:16:12.122 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-04 17:16:12.122 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-04 17:16:12.123 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-04 17:16:12.123 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-04 17:16:12.123 +02:00] [INF] [1] Main: Processor count: 12
[2025-07-04 17:16:12.123 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-04 17:16:12.123 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-04 17:16:12.123 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-04 17:16:12.124 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-04 17:16:12.124 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-04 17:16:12.124 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-04 17:16:12.124 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-04 17:16:13.182 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-04 17:16:13.743 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-04 17:16:13.939 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-04 17:16:13.966 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-04 17:16:13.991 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-04 17:16:14.322 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-04 17:16:14.322 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-04 17:16:14.323 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-04 17:16:14.335 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-04 17:16:14.336 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-04 17:16:14.336 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-04 17:16:14.336 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-04 17:16:28.679 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-04 17:16:28.685 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-04 17:16:28.686 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-04 17:16:28.686 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-04 17:16:28.687 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-04 17:16:28.707 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-04 17:16:28.707 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-04 17:16:28.828 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-04 17:16:29.446 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-04 17:16:29.490 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-04 17:16:29.495 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-04 17:16:29.516 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-05 03:00:00.000 +02:00, which is 09:43:30.4834925 from now.
[2025-07-04 17:16:29.539 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-05 02:00:00.000 +02:00, which is 08:43:30.4606702 from now.
[2025-07-04 17:16:29.688 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-04 17:16:29.747 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-04 17:16:29.810 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-04 17:16:29.833 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-04 17:16:29.858 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-04 17:16:30.028 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-04 17:16:31.402 +02:00] [INF] [11] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-04 17:16:32.557 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-04 17:16:32.586 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-04 17:16:34.136 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-07-04 17:16:43.372 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-04 17:16:43.375 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-04 17:16:43.375 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-04 17:16:43.375 +02:00] [INF] [1] Main: Startup complete 0:00:31.3016385
[2025-07-04 19:55:05.890 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 19:55:05.925 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 19:55:05.930 +02:00] [INF] [29] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-04 21:38:40.555 +02:00] [INF] [64] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 21:38:40.661 +02:00] [INF] [64] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-04 21:38:40.737 +02:00] [INF] [64] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
