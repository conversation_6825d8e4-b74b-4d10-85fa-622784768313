[2025-07-08 13:24:11.164 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-08 13:24:11.229 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CONFIG_DIR, /config/config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_CACHE_DIR, /cache]"]
[2025-07-08 13:24:11.230 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-08 13:24:11.251 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-08 13:24:11.251 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: Processor count: 12
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-08 13:24:11.252 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-08 13:24:11.794 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-08 13:24:12.370 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-08 13:24:12.594 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-08 13:24:12.602 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-08 13:24:12.654 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-08 13:24:12.849 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-08 13:24:12.849 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-08 13:24:12.850 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-08 13:24:12.851 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-08 13:24:12.852 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-08 13:24:12.852 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-08 13:24:12.852 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-08 13:24:25.468 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-08 13:24:25.472 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-08 13:24:25.472 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-08 13:24:25.473 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-08 13:24:25.473 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-08 13:24:25.492 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-08 13:24:25.493 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-08 13:24:25.612 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-08 13:24:26.210 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-08 13:24:26.279 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-08 13:24:26.307 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-09 03:00:00.000 +02:00, which is 13:35:33.6922354 from now.
[2025-07-08 13:24:26.384 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-09 02:00:00.000 +02:00, which is 12:35:33.6156869 from now.
[2025-07-08 13:24:26.908 +02:00] [INF] [11] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-08 13:24:27.253 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-08 13:24:27.316 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-08 13:24:27.445 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-08 13:24:27.512 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-08 13:24:27.633 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-08 13:24:27.801 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-08 13:24:28.669 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-08 13:24:29.403 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-08 13:24:29.425 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-08 13:24:31.620 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 2 seconds
[2025-07-08 13:24:41.485 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-08 13:24:41.488 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-08 13:24:41.488 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-08 13:24:41.488 +02:00] [INF] [1] Main: Startup complete 0:00:30.7568392
[2025-07-08 14:11:33.934 +02:00] [INF] [117] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-08 14:11:33.937 +02:00] [INF] [117] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-08 14:11:33.941 +02:00] [INF] [117] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-08 15:39:32.920 +02:00] [INF] [74] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-08 15:41:45.151 +02:00] [INF] [61] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-08 15:41:52.334 +02:00] [INF] [61] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-08 15:41:52.335 +02:00] [INF] [61] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-08 15:41:52.336 +02:00] [INF] [61] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-08 15:41:52.346 +02:00] [INF] [61] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-08 15:41:53.720 +02:00] [WRN] [61] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-08 15:41:53.926 +02:00] [INF] [121] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/267a103319284ef8936bf1ee26fbc619.png"
[2025-07-08 15:41:54.335 +02:00] [INF] [121] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/267a103319284ef8936bf1ee26fbc619.png"
[2025-07-08 15:41:55.176 +02:00] [WRN] [71] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-08 15:41:56.997 +02:00] [INF] [121] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Folder", Name: "Dragons - Gift of the Night Fury (2011)", Path: "/CONTENIDO/PELIS/Dragons - Gift of the Night Fury (2011)", Id: 6d6fe4dd-2309-fe11-145f-331e33558965
[2025-07-08 15:41:57.953 +02:00] [INF] [87] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-08 15:41:58.014 +02:00] [INF] [87] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-07-08 15:41:58.848 +02:00] [INF] [65] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-07-08 15:41:58.848 +02:00] [INF] [65] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-07-08 15:41:59.062 +02:00] [INF] [102] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Rick y Morty"
[2025-07-08 15:41:59.354 +02:00] [ERR] [82] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-07-08 15:42:01.921 +02:00] [INF] [102] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E07 - Ricker Than Fiction HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-07-08 15:42:03.937 +02:00] [INF] [97] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Episode", Name: "Ricker Than Fiction", Path: "", Id: f845f6a6-52c2-055f-6c74-33b88081c54c
[2025-07-08 15:42:04.049 +02:00] [INF] [97] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Rick y Morty"
[2025-07-08 15:42:04.049 +02:00] [INF] [97] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 6ed225c1-b48f-10be-fa7e-3694be710444
[2025-07-08 15:42:13.220 +02:00] [INF] [14] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-08 15:42:13.976 +02:00] [INF] [14] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-08 15:42:13.979 +02:00] [INF] [14] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-07-08 15:42:13.979 +02:00] [INF] [14] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-07-08 15:42:13.979 +02:00] [INF] [14] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Update to process
[2025-07-08 15:42:13.983 +02:00] [INF] [14] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-07-08 15:42:13.983 +02:00] [INF] [14] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-07-08 15:42:13.983 +02:00] [INF] [14] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 episodes with event type Update
[2025-07-08 15:42:15.423 +02:00] [INF] [97] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-07-08 15:42:15.423 +02:00] [INF] [97] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-07-08 15:42:15.423 +02:00] [INF] [97] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-07-08 15:42:22.698 +02:00] [INF] [101] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 30 seconds
[2025-07-08 15:42:22.702 +02:00] [INF] [106] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-08 15:42:22.907 +02:00] [INF] [70] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-08 15:42:23.096 +02:00] [INF] [97] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-08 15:42:46.610 +02:00] [INF] [70] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Más Rick que la ficción". Stopped at "31681" ms
[2025-07-08 15:42:46.612 +02:00] [INF] [97] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-08 15:42:47.754 +02:00] [WRN] [101] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-07-08 15:43:09.226 +02:00] [INF] [101] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-08 15:43:57.224 +02:00] [INF] [104] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-08 15:44:45.223 +02:00] [INF] [104] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-08 15:45:33.222 +02:00] [INF] [100] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-08 15:46:21.217 +02:00] [INF] [99] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-08 15:47:09.220 +02:00] [INF] [99] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-07-08 15:47:21.104 +02:00] [WRN] [100] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-07-08 15:47:21.113 +02:00] [INF] [100] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-08 16:04:44.180 +02:00] [INF] [93] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Más Rick que la ficción". Stopped at "1344481" ms
[2025-07-08 16:29:35.533 +02:00] [INF] [10] Emby.Server.Implementations.Session.SessionManager: Sending shutdown notifications
[2025-07-08 19:59:15.048 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-07-08 19:59:15.108 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]"]
[2025-07-08 19:59:15.109 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-07-08 19:59:15.115 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-07-08 19:59:15.115 +02:00] [INF] [1] Main: Architecture: X64
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: User Interactive: True
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: Processor count: 12
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-07-08 19:59:15.116 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-07-08 19:59:15.489 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-07-08 19:59:15.801 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-07-08 19:59:15.888 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-07-08 19:59:15.896 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-07-08 19:59:15.908 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-07-08 19:59:16.021 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-08 19:59:16.021 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-07-08 19:59:16.021 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-07-08 19:59:16.022 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-07-08 19:59:16.023 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-07-08 19:59:16.023 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-07-08 19:59:16.023 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-07-08 19:59:27.594 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-07-08 19:59:27.598 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-07-08 19:59:27.599 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-07-08 19:59:27.599 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-07-08 19:59:27.599 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-07-08 19:59:27.658 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-07-08 19:59:27.658 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-07-08 19:59:27.776 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-07-08 19:59:28.375 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-07-08 19:59:28.418 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-07-08 19:59:28.421 +02:00] [INF] [11] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-08 19:59:28.508 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-09 03:00:00.000 +02:00, which is 07:00:31.4912590 from now.
[2025-07-08 19:59:28.565 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-09 02:00:00.000 +02:00, which is 06:00:31.4346111 from now.
[2025-07-08 19:59:28.665 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-08 19:59:28.990 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-08 19:59:29.122 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-07-08 19:59:29.209 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-07-08 19:59:29.238 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-07-08 19:59:29.264 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-07-08 19:59:29.448 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-07-08 19:59:31.590 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-07-08 19:59:31.645 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-07-08 19:59:33.724 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 2 seconds
[2025-07-08 19:59:43.352 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-07-08 19:59:43.363 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-07-08 19:59:43.364 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-07-08 19:59:43.364 +02:00] [INF] [1] Main: Startup complete 0:00:28.7344246
[2025-07-08 20:00:38.886 +02:00] [INF] [22] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-08 20:01:04.661 +02:00] [INF] [24] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-08 20:01:05.259 +02:00] [INF] [22] Jellyfin.Api.Controllers.DynamicHlsController: Current HLS implementation doesn't support non-keyframe breaks but one is requested, ignoring that request
[2025-07-08 20:01:05.273 +02:00] [INF] [22] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E10 - The Rickchurian Mortydate HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 libfdk_aac -ac 2 -ab 256000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename \"77703bc0f6c75ff567460e8ec29f6676-1.mp4\" -start_number 0 -hls_segment_filename \"/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676%d.mp4\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676.m3u8\""
[2025-07-08 20:01:06.625 +02:00] [WRN] [22] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-07-08 20:01:30.104 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-07-08 20:02:11.300 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/77703bc0f6c75ff567460e8ec29f6676.m3u8"
[2025-07-08 20:02:13.060 +02:00] [INF] [30] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Web" "10.10.7" playing "El Ricksajero del Mortimiedo". Stopped at "1305935" ms
[2025-07-08 20:02:18.142 +02:00] [INF] [30] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-08 20:05:20.456 +02:00] [INF] [33] Emby.Server.Implementations.Session.SessionManager: Sending shutdown notifications
[2025-07-08 20:05:20.465 +02:00] [INF] [29] Jellyfin.Networking.PortForwardingHost: Stopping NAT discovery
[2025-07-08 20:05:20.471 +02:00] [INF] [45] Main: Running query planner optimizations in the database... This might take a while
[2025-07-08 20:05:20.493 +02:00] [INF] [29] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-08 20:05:20.494 +02:00] [INF] [29] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-08 20:05:20.495 +02:00] [INF] [29] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-08 20:05:20.505 +02:00] [INF] [45] Emby.Server.Implementations.ApplicationHost: Disposing "CoreAppHost"
[2025-07-08 20:05:20.505 +02:00] [INF] [45] Emby.Server.Implementations.ApplicationHost: Disposing "MusicBrainzArtistProvider"
[2025-07-08 20:05:20.505 +02:00] [INF] [45] Emby.Server.Implementations.ApplicationHost: Disposing "MusicBrainzAlbumProvider"
[2025-07-08 20:05:20.505 +02:00] [INF] [45] Emby.Server.Implementations.ApplicationHost: Disposing "PluginManager"
