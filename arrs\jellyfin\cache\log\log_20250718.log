[2025-07-18 00:48:20.022 +02:00] [INF] [90] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-07-18 00:48:24.451 +02:00] [INF] [57] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-18 00:48:25.412 +02:00] [INF] [3] Jellyfin.Api.Controllers.DynamicHlsController: Current HLS implementation doesn't support non-keyframe breaks but one is requested, ignoring that request
[2025-07-18 00:48:25.446 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/PELIS/<PERSON> Wick - Chapter 4 (2023)/<PERSON> Wick - Chapter 4 (2023) Bluray-1080p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type fmp4 -hls_fmp4_init_filename \"7a451e5924c557a0ad08d4e0ef99e429-1.mp4\" -start_number 0 -hls_segment_filename \"/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e429%d.mp4\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e429.m3u8\""
[2025-07-18 00:48:28.901 +02:00] [INF] [88] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e429.m3u8"
[2025-07-18 00:48:29.259 +02:00] [INF] [88] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-07-18 00:48:29.263 +02:00] [INF] [88] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/7a451e5924c557a0ad08d4e0ef99e429.m3u8"
[2025-07-18 00:48:30.805 +02:00] [INF] [57] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Web" "10.10.7" playing "John Wick 4". Stopped at "1758" ms
[2025-07-18 00:48:33.033 +02:00] [INF] [90] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "John Wick 4", Path: "/CONTENIDO/PELIS/John Wick - Chapter 4 (2023)/John Wick - Chapter 4 (2023) Bluray-1080p.mkv", Id: 20034cd5-2067-d575-02c2-df4412f102e0
[2025-07-18 00:48:33.050 +02:00] [INF] [90] Emby.Server.Implementations.Library.LibraryManager: Deleting item path, Type: "Movie", Name: "John Wick 4", Path: "/CONTENIDO/PELIS/John Wick - Chapter 4 (2023)", Id: 20034cd5-2067-d575-02c2-df4412f102e0
[2025-07-18 00:48:43.206 +02:00] [INF] [89] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-07-18 00:48:43.206 +02:00] [INF] [89] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-07-18 00:48:45.034 +02:00] [INF] [89] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-18 00:52:07.267 +02:00] [INF] [88] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-07-18 00:52:18.464 +02:00] [INF] [49] Emby.Server.Implementations.Session.SessionManager: Sending shutdown notifications
[2025-07-18 00:52:18.543 +02:00] [INF] [87] Jellyfin.Networking.PortForwardingHost: Stopping NAT discovery
[2025-07-18 00:52:18.575 +02:00] [INF] [88] Main: Running query planner optimizations in the database... This might take a while
[2025-07-18 00:52:18.675 +02:00] [INF] [84] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-07-18 00:52:18.676 +02:00] [INF] [84] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-18 00:52:18.677 +02:00] [INF] [84] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-18 00:52:18.762 +02:00] [INF] [88] Emby.Server.Implementations.ApplicationHost: Disposing "CoreAppHost"
[2025-07-18 00:52:18.763 +02:00] [INF] [88] Emby.Server.Implementations.ApplicationHost: Disposing "MusicBrainzArtistProvider"
[2025-07-18 00:52:18.767 +02:00] [INF] [88] Emby.Server.Implementations.ApplicationHost: Disposing "MusicBrainzAlbumProvider"
[2025-07-18 00:52:18.768 +02:00] [INF] [88] Emby.Server.Implementations.ApplicationHost: Disposing "PluginManager"
